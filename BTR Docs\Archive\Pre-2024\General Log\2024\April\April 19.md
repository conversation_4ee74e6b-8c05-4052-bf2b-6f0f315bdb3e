# April 19

Im sick it sucks

Writing out ideas for how to setup and optimize game

- Use scriptable objets to define attributes
    - Projectiles
    - Enemies
    
    Events - public event Action
    
    register for these changes
    
    Seems like the easiest - best way to use Events in Unity
    
    This is an Observer pattern?
    
    If data is constantly changing, this is not a great pattern to use
    
    Ideas for use 
    
    - register projectiles? Register enemies?
    
    Singleton
    
    Using for Game Manager currently
    
    May be useful for other stuff
    
    Code Monkey Intermediate course
    
    Shortcuts
    
    Ctrl + left / right for cursor movement
    
    Home and end keys, beginning and end of lines
    
    Visual Commander OR some other tool to allow easy skipping of X lines at a time.
    
    Maybe 5 lines at a time? Would be better movement then pg Up / Down
    
    Ctrl + SHift + up / Down - when asymbol is selected, move between instacnes of that symbol
    
    example - variable, method, etc
    
    Make sure my refactor affects all instances across the project, and then refactor!
    
    A lot of stuff could be named better
    
    Should I put all my classes I've written in a custom name space? Would this be good?
    
    No! Does t work that way
    
    Should extend my use of interfaces, would be helpful for organization 
    
    What are some good ways I can use Flexalon?
    
    Projectile factory - useful to me?
    
    https://assetstore.unity.com/packages/tools/particles-effects/projectile-factory-behavior-based-projectile-system-281692