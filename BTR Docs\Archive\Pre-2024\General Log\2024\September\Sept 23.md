# Sept 23

Adjusted Player Shooting script to for shooting projectiles straight without a locked on enemy. 

NEED TO TEST

Fix the QTE idea - use for different length enemies as test? what other ideas make sense?

Need to also make Infinite Snake chasing snake NOT be lockable - need to add paramters to allow for this. 

- Does Player need a check on enemies to see whether they are lockable or not?

Interested in Perfect Culling asset but may be the case that GPU Imstancer can achieve better results for me. Need to try that out again

Introduced Stamina for ricochet

Changed out some sounds, developing a new approach

Changed Player Locked State of Projectile to old version, still trying to figure out shooting these straight. 

!!!

maybe just recycle locked projectiles and then call the shoot method for however many were left? Shoot from the reticle - similar to static shooting calls. 

!!!!

**Ideation**

QTE

use for different length enemies

use for traps & obstacles

It’s not about just ideas that would add to complexity, but are also easily implementable and are multipliers of the current ideas