# Vision / Systems

Rez-like rhythm action game

Faster paced? More interaction

Extending the way rhythm / action / melody interact

Alternate cross hairs per target / area?? Are there combos per enemy?

Systems based games vs Content based games - where do I fit?

Design - refer to design mags / typography / 

blue / yellow

42 minutes Live EP 5 Sonic Bloom - life cycle of deving a game

Elements of creation / audio editing

We can make moves in rhythm

We can make moves to the beat / remix a bit

**What can happen in a song?**

Lead the player into interacting with our systems

Score is a Timer - you are buying yourself time by getting enemies

HOWEVER time continues when things are glitching/paused 

You cannot freeze time indefinitely? 

IDEA: select targets - freeze time lets you move their position - targets explode or chain with other enemies

IDEA: No dolly, your location changes based on moving across selected targets in time. 

IDEA: If moving selected objects while time is frozen, lock on is disabled? Only able to move object during the time freezes. this would make time freeze mechanic used quiet frequently. Also makes me think - how to do this in 3d? visually hard to read. sort of like super hyper cube

IDEA: Slowly emit shapes that can be drawn through lock on. emitted through bursts.  completing shape does something? extra points?

IDEA: MATCH 2 / 3 style gameplay? Lock on / colours mean anything?

How does any element relate to music / composing / editing?

IDEA: Every extend extra style gameplay

Lock on to one, blow up and chain of melodies

Think this is in child of eden?

IDEA: Move dolly more on each loop

Can move backwards a bit more

Good for overwhelming parts

Both bullethelland extend modes can exist in same game!

Lock on to targets - melody / rhythm

Target lock on in rhythm

Target Firing in rhythm

Quick time slowdown

Quicker time slowdown

Get points

Get more points with combos