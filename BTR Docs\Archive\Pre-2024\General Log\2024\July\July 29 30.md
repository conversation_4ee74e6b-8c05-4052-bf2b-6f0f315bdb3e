# July 29/30

Koreographer track issue was breaking the infinite areas

go over how that works again - unsure why this broke things

- i udnerstand the track it wanted wasnt there, but i dont know why im use on collection of tracks vs another, need to evaluate how all of that works again

Setup GIt for this project again

Need to approach oncoming tasks as small pieces, time blocked. This will help me actually get things done. 

31st

https://www.youtube.com/watch?v=Dv7KImnPnm8

Section sounds into SUb / LOw / Mids / Highs and consider what type of sounds get what

Bus Structure

- Define busses for general soudn types, and properly sidechain them for what needs to be highlighted

Transient / Body / Tail structure for sound clips may help with setup in FMOD

Use Spatializer to give certain sounds bigger impact

Testing game

- Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it
- Locked state material defined on projectile - problem with this level is the scale of the projecitle makes it too big when locked, looks odd - Sclaed down from 10 to 3 currently
- Verify Infinite Snake View Target - is this necessary anymore?
- Adding deformables to the tail end of the infinit ouroboros generation
    - deformable’s are being added, need to verify they properly deform

GOt Deformables working in Infinite Snake section! Good stuff