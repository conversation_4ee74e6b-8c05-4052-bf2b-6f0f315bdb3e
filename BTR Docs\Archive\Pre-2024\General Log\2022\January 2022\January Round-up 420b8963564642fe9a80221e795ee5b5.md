# January Round-up

Starting with mid Dec.- last time I posted on Slack

Last goal - “<PERSON>haviour Designer + A* Pathfinder + Ultimate Spawner + Pool Manager. This sets me up to quickly prototype enemies + level design. Can spawn enemies with different AI patterns across a moving nav mesh in timed / staggered waves”

Troubleshooting! 

- Pooled Enemies not finding their Navmesh (was developer issue!)
- Spawn From Pool class - careful updating! I made my own changes it looks like 😐
- Looking into Behavior Designer’s setup - basic movement working but many features were not
    - Global variables, references, conditional aborts, etc
    - Waypoint vs random wandering - static or while navmesh moving
    - Had Dynamic waypoints working successfully, but unsure of future utility
- Added basic enemy targeting. Can choose and enemy and have all collected projectiles shoot at them
- Bullets inheiriting enemy rotation - not fully fixed?
- Realized an altered or different Minimap solution is needed
    - Due to map not updating points - want to change color of locked on vs free bullets
- Added some more fluid movement to projectiles
    - Caused spinning in circles issues, but mostly resolved
- Adjusting camera distance / angle for better view of field
    - Trying to make each rotated perspective see more / enough of others
- Tried bringing in Deathmatch AI but too many features I don’t need
    - Complex but also simplifies some aspects, would take a lot of customizing and digging MAYBE
- Sped up editor → play time with a cool trick!
    - [https://www.youtube.com/watch?v=P7cYVg5fAvY](https://www.youtube.com/watch?v=P7cYVg5fAvY)
- Tried solutions like Mesh Combine / Mesh Simplify / Mesh Baker to make 1000’s of things work in a scene
    - Mesh Baker / Texture Baker worked best - bake in quadrants so things can be culled
- Used Automatic LOD free asset to generates LODS and it works well!
- Tried various ‘paint prefab onto scene’ assets and Prefab Brush+ worked well!
- GPU Instancer seems better then all Mesh combining assets for scene optimization
    - Works great, remember if you add things to scene, need to reregister objects
- Have Ethereal URP somewhat working? Not entirely sure how I feel about it
    - Could be cool for Control-like effects
- Imlemented IDamageable from Beahavior Designer for Attack / Damage
- Behavior Designer
    - Scoot and Shoot / Flank both working, so Tactical pack things should all work?
- Modeled some enemies with ‘corruption’ shader on them
- Disabled target cube for visual clutter, but need to look at it’s use again!
- Lots of different assorted bugfixing!

Ideas!

- Use a similar effect for one level? [https://www.youtube.com/watch?v=3AIsleRlx5Q](https://www.youtube.com/watch?v=3AIsleRlx5Q)
- Use Judgement Silversword Shield effect ?
- Lerp colors! [https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/](https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/)
    - Probably use DOTween or something
- how to hide stuff in debug but remain public?
- Reference Area X for visual clarity when lots of far away bullets / items are on screen moving around