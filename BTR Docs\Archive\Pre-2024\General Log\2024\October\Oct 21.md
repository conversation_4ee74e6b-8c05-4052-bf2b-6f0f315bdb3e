# Oct 21

Was quite sick for the past week, just looking at things again now

Updating Cinemachine 3 camera to be a bit more refined

Updating Feel so it works with Cinemachine 3

- Implemented basic ones for hit and ricochet

Update several projectiles system / time manager so that projectile spawning is responsive to the changes in the time scale

Need to setup proper SDF’s and level bits for Scene 3

Remeber vectooField is the property originally used

May need to find something better than the SDF’s

Or research SDF more to see how to make them fit

Important I investigate this

https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005
Seems some of my jobs are getting in the way of each other, may need to figure out they scheduling so they don’t interrupt one another

Need to figure out the see through issue where enemies are more visible when behind / underneath the mobius/ground/snake

OCT 22

Adjusting shaders on main character so i can exclude them from the bitcrushing effect 

Ultimate Lit Shader is done - use this more or try others

This didnt work, taking another approach 

going to see where in the stack i want it to render

- adding the option to the render feature to choose where it’s rendered

Maybe I can set certian objects on layers where they are rendered on top of this?

Vaguely have this working

Effect intensity of JPG is being reset on start to 0 - need to fix this

OCT 23rd

Effect intensity of JPG is being reset on start to 0 - FIXED

trying to resolve issue with projectiles not shooting at times

i think it has to do with player locking on to projectile causing some interruption in how the enemies shoot

realizing this has more to do with what part is repeating in FMOD

Addressed this with an alternate ‘shoot every loop’ pattern when i nthe lock state,

located in the Enemy Shooting Manager

Also set a minmum loop time, so that if a loop is too short it’ll be every two loops 

IDEA

Item pickup using Dodge button

ITEM - Increases number of possible locks for player

Look up what yo uwant to use stamina for again??>????

Making things a bit more efficient

- Audio Manager for projectiles, no longer many fmod instances of sounds, just one used for all
    - may expand on this, small pool etc
- Edited a Highlighter script to activate more gradually to minimzie frame spike with enemy loading

vsync forces 60 fps, need to fix this

Build Test

- CPU limited is the issue for frame rate

Need to fix lighting in several stages

Need to finish the explode enemy

Need to debug all audio cutting out but then returning - whats causing that to happen?

THink

Minimum Viable Product - so you can tweak things once implemented

Implement Final snake boss!

Implement item pickups!