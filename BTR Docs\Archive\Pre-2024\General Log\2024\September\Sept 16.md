# Sept 16

Collection of things that may be help by o1

- Projectile movement
    - There are still bullets getting stuck in spinning circles
    - jagged movement
    - accuracy attribute
- Enemy behaviours implementation
    - line of sight
    - distance from one another
- optimize render pipeline
    - look at files, make edits where useful
- optimize any intense shaders
- 

General errors

- any lock ons must be disabled before moving to next section or there are errors.
- In build, there are camera issue in both Snake Infinite sections.
    - Seemingly camera issues in other spots as well, verify this

Creative Idea

- Charge and explode enemy type
- Improve feel of new shooting system
- Need to finish building out QTE shots

Organization

- Koreographer - Chart out my approach with appropriate naming, will make things easier moving forward
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl

![image.png](Sept%2016%20103ffc7f811f807d8035f38335d83548/image.png)

Look into turbulence particle affector - big resource hog

Remvoed it - this is resovled!