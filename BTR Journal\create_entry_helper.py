#!/usr/bin/env python3
"""
BTR Journal Entry Helper for AI Assistants
Provides functions to automate journal entry creation and index updates.
"""

import os
import re
from datetime import datetime
from pathlib import Path

class BTRJournalHelper:
    def __init__(self, base_path="j:/BTR U6 2025 RG/BTR_Journal"):
        self.base_path = Path(base_path)
        self.templates_path = self.base_path / "Templates"
        self.index_path = self.base_path / "Index"
        
    def get_current_timestamp(self):
        """Get current timestamp in ISO format with timezone"""
        return datetime.now().strftime("%Y-%m-%dT%H:%M:%S-04:00")
    
    def get_current_date(self):
        """Get current date in YYYY-MM-DD format"""
        return datetime.now().strftime("%Y-%m-%d")
    
    def get_month_folder(self):
        """Get current month folder path"""
        now = datetime.now()
        month_name = now.strftime("%m-%B")  # e.g., "07-July"
        return self.base_path / "2025" / month_name
    
    def create_month_folder(self):
        """Create month folder if it doesn't exist"""
        month_folder = self.get_month_folder()
        month_folder.mkdir(parents=True, exist_ok=True)
        return month_folder
    
    def get_template_content(self, entry_type):
        """Get template content based on entry type"""
        templates = {
            "bug": "bug_fix_template.md",
            "feature": "feature_template.md",
            "debug": "debug_session_template.md"
        }
        
        template_file = templates.get(entry_type.lower())
        if not template_file:
            raise ValueError(f"Unknown entry type: {entry_type}")
        
        template_path = self.templates_path / template_file
        if not template_path.exists():
            raise FileNotFoundError(f"Template not found: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def generate_filename(self, entry_type, description):
        """Generate filename for journal entry"""
        date = self.get_current_date()
        type_prefix = entry_type.upper()
        # Clean description for filename
        safe_description = re.sub(r'[^\w\s-]', '', description)
        safe_description = re.sub(r'[-\s]+', '_', safe_description)
        return f"{date}_{type_prefix}_{safe_description}.md"
    
    def create_journal_entry(self, entry_type, description, content_updates=None):
        """Create a new journal entry"""
        # Create month folder
        month_folder = self.create_month_folder()
        
        # Generate filename and path
        filename = self.generate_filename(entry_type, description)
        filepath = month_folder / filename
        
        # Get template content
        template_content = self.get_template_content(entry_type)
        
        # Replace timestamp
        timestamp = self.get_current_timestamp()
        content = template_content.replace("YYYY-MM-DDTHH:MM:SS-TZ", timestamp)
        
        # Apply any content updates
        if content_updates:
            for placeholder, replacement in content_updates.items():
                content = content.replace(placeholder, replacement)
        
        # Write file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filepath
    
    def update_bug_fixes_index(self, date, priority, system, issue, status, filename):
        """Update the bug fixes index with new entry"""
        index_file = self.index_path / "bug_fixes_index.md"
        
        if not index_file.exists():
            return False
        
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create new table row
        relative_path = f"../2025/{date[5:7]}-{datetime.strptime(date, '%Y-%m-%d').strftime('%B')}/{filename}"
        status_icon = "✅" if status.lower() == "fixed" else "🔄"
        new_row = f"| {date} | {priority} | {system} | {issue} | {status_icon} {status} | [{filename}]({relative_path}) |"
        
        # Find the table and add new row
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "| Date | Priority | System | Issue | Status | File |" in line:
                # Insert after the header separator
                lines.insert(i + 2, new_row)
                break
        
        # Update statistics
        updated_content = '\n'.join(lines)
        
        # Write back
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True
    
    def update_system_changes_index(self, date, change_type, system, change, impact, filename):
        """Update the system changes index with new entry"""
        index_file = self.index_path / "system_changes_index.md"
        
        if not index_file.exists():
            return False
        
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create new table row
        relative_path = f"../2025/{date[5:7]}-{datetime.strptime(date, '%Y-%m-%d').strftime('%B')}/{filename}"
        new_row = f"| {date} | {change_type} | {system} | {change} | {impact} | [{filename}]({relative_path}) |"
        
        # Find the table and add new row
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "| Date | Type | System | Change | Impact | File |" in line:
                # Insert after the header separator
                lines.insert(i + 2, new_row)
                break
        
        # Update timestamp
        timestamp = self.get_current_timestamp()
        updated_content = '\n'.join(lines)
        updated_content = re.sub(
            r'\*Last Updated: .*?\*',
            f'*Last Updated: {timestamp}*',
            updated_content
        )
        
        # Write back
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True

# Example usage functions for AI assistants
def create_bug_fix_entry(description, system, priority="Medium", files_modified=None):
    """Create a bug fix journal entry"""
    helper = BTRJournalHelper()
    
    content_updates = {
        "[System/Component being debugged]": system,
        "[Critical/High/Medium/Low]": priority
    }
    
    if files_modified:
        files_section = "\n".join([f"- `{file}` - {desc}" for file, desc in files_modified.items()])
        content_updates["- `path/to/file1.cs` - Description of changes\n- `path/to/file2.cs` - Description of changes"] = files_section
    
    filepath = helper.create_journal_entry("bug", description, content_updates)
    filename = filepath.name
    date = helper.get_current_date()
    
    # Update indexes
    helper.update_bug_fixes_index(date, priority, system, description, "Fixed", filename)
    helper.update_system_changes_index(date, "Bug Fix", system, f"Fixed {description}", f"{priority} - Restored functionality", filename)
    
    return filepath

def create_feature_entry(description, system, status="Complete", requirements=None):
    """Create a feature development journal entry"""
    helper = BTRJournalHelper()
    
    content_updates = {
        "[Name of the feature]": description,
        "[Planning/In Progress/Complete/On Hold]": status
    }
    
    if requirements:
        req_section = "\n".join([f"- [ ] {req}" for req in requirements])
        content_updates["- [ ] Requirement 1\n- [ ] Requirement 2\n- [ ] Requirement 3"] = req_section
    
    filepath = helper.create_journal_entry("feature", description, content_updates)
    return filepath

def create_debug_entry(description, system, duration="Unknown"):
    """Create a debug session journal entry"""
    helper = BTRJournalHelper()
    
    content_updates = {
        "[System/Component being debugged]": system,
        "[Time spent debugging]": duration
    }
    
    filepath = helper.create_journal_entry("debug", description, content_updates)
    return filepath

if __name__ == "__main__":
    # Test the helper
    helper = BTRJournalHelper()
    print(f"Base path: {helper.base_path}")
    print(f"Current timestamp: {helper.get_current_timestamp()}")
    print(f"Month folder: {helper.get_month_folder()}")
