# May 3

Stemming from yesterday’s work - lock on to trigger different parts of the drum loop? 

172 Flux

Add element when locking on 

also change drum pattern in response

magnet transitions - can these move me back and forth easily to places in the song?

FMOD Error - Global paramters howing unavailable in Events in Unity, set to Local

In lock on of Crosshair - reference musicPlayback event emitter

States of Player Shooting

Not Locking / Locking / Holding / Firing

Need consistent event ID’s for Koreographer 

“Perc” for drums / baseline track

Can use 

musicPlayback.EventInstance.setParameterByName("Lock State", 2);

to change lock state in Crosshair class

Locking tag is probably a good idea, some transitory tag between locking and firing really

ALso screen shake when lcoked on until fired? screen shake off on shoot tag

Updating Feel to try and get Cinemachine camera shake working again

WORKS! Shaking Camera!

Some tips on using Feedbacks

[https://www.youtube.com/watch?v=pMOPgoTNYFk](https://www.youtube.com/watch?v=pMOPgoTNYFk)

Add Shoot Feedbacks to game 

Figure out bullet speeds

Transition to new sections with targets taken out or something else?

Prototype!