#!/usr/bin/env python3
"""
BTR Journal Entry Creator
Automates the creation of timestamped journal entries for the BTR project.
"""

import os
import sys
from datetime import datetime
import shutil

def get_current_timestamp():
    """Get current timestamp in ISO format with timezone"""
    return datetime.now().strftime("%Y-%m-%dT%H:%M:%S%z")

def get_current_date():
    """Get current date in YYYY-MM-DD format"""
    return datetime.now().strftime("%Y-%m-%d")

def get_current_month_folder():
    """Get current month folder path"""
    now = datetime.now()
    year = now.strftime("%Y")
    month = now.strftime("%m-%B")
    return f"2025/{month}"

def create_folder_if_not_exists(path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created folder: {path}")

def get_template_path(entry_type):
    """Get template file path based on entry type"""
    templates = {
        "bug": "Templates/bug_fix_template.md",
        "feature": "Templates/feature_template.md", 
        "debug": "Templates/debug_session_template.md"
    }
    return templates.get(entry_type.lower())

def create_journal_entry(entry_type, description):
    """Create a new journal entry from template"""
    
    # Get paths
    template_path = get_template_path(entry_type)
    if not template_path:
        print(f"Error: Unknown entry type '{entry_type}'. Use: bug, feature, debug")
        return False
    
    if not os.path.exists(template_path):
        print(f"Error: Template not found: {template_path}")
        return False
    
    # Create month folder
    month_folder = get_current_month_folder()
    create_folder_if_not_exists(month_folder)
    
    # Generate filename
    date = get_current_date()
    type_prefix = entry_type.upper()
    safe_description = description.replace(" ", "_").replace("/", "_").replace("\\", "_")
    filename = f"{date}_{type_prefix}_{safe_description}.md"
    filepath = os.path.join(month_folder, filename)
    
    # Copy template to new file
    shutil.copy2(template_path, filepath)
    
    # Replace timestamp placeholder in the new file
    timestamp = get_current_timestamp()
    with open(filepath, 'r') as f:
        content = f.read()
    
    content = content.replace("YYYY-MM-DDTHH:MM:SS-TZ", timestamp)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"Created journal entry: {filepath}")
    print(f"Please edit the file to add your specific details.")
    
    return True

def main():
    """Main function"""
    if len(sys.argv) != 3:
        print("Usage: python create_journal_entry.py <type> <description>")
        print("Types: bug, feature, debug")
        print("Example: python create_journal_entry.py bug 'shooting system not working'")
        return
    
    entry_type = sys.argv[1]
    description = sys.argv[2]
    
    # Change to journal directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    create_journal_entry(entry_type, description)

if __name__ == "__main__":
    main()
