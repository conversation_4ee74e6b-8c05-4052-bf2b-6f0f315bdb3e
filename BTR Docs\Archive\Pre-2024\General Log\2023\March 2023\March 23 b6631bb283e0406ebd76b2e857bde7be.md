# March 23

Buto not working 100% so adjusted to work in a manageable state

Unity crashing, but changed from D12 to D11 and seems fine now

A* Graph issues, pulled in graph from previous scene and now it works

Enemy AI still bugging out, need to fix this

Need to work on controller use in UI menus

Taking a look at previous implementation 

Experimenting with Settings UI - needs camera for cursor selection to work. BUT post processing gets in the way. Dont want to make a new camera if I don’t have to? Maybe not so bad if setting camera active / disabled