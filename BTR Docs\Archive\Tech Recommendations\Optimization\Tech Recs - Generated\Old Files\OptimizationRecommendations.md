# Optimization Recommendations

This document presents prioritized recommendations for optimizing our game, taking into account both their performance impact and their implementation difficulty. The order below indicates the recommended sequence for tackling these optimizations, starting with the easiest to implement that are most impactful.

## 1. Establish Profiling and Performance Budgets
**Impact:** Foundational  
**Difficulty:** Low

- Set up comprehensive profiling tools and integrate custom logging.
- Establish clear performance and memory budgets for each feature.
- Use these baselines to guide all subsequent optimization efforts.

## 2. Optimize Framework Overhead
**Impact:** High  
**Difficulty:** Low to Medium

- Centralize per-frame processing by reducing redundant Update() calls.
- Enhance and review existing pooling systems (see Pooling/ folder) for efficient object reuse.
- Consolidate common operations to minimize overhead.

## 3. Transition to a Data-Oriented Design
**Impact:** Very High  
**Difficulty:** Medium

- Replace object-based patterns with arrays of structs.
- Implement Unity's Job System (with Burst) for high-frequency updates, such as enemy or projectile management.
- This change will improve cache utilization and overall update performance.

## 4. Enhance Memory Management and Asset Handling
**Impact:** Medium  
**Difficulty:** Medium

- Utilize Addressables for efficient streaming and manage asset bundles using LZ4 compression.
- Establish custom memory profiling tools to track memory usage and identify leaks.
- Maintain safe margins and continuously monitor asset distribution per level.

## 5. Improve the Rendering Pipeline
**Impact:** High  
**Difficulty:** High

- Transition from Shader Graph to custom HLSL shaders where more control is needed.
- Implement static batching, front-to-back sorting, and offscreen rendering to reduce draw calls and overdraw.
- Optimize lighting and shadow techniques to further reduce GPU load.

---

Follow this prioritized sequence to achieve steady, measurable improvements in both performance and stability. Regularly update this document as new insights and improvements are made.

## Appendix: Reviewed Documents and Extended Technical Recommendations

The following documents from the Tech Recs folder were thoroughly reviewed. The recommendations below include low-level, actionable items to improve performance, including specific shader optimizations, memory layout changes, job system and Burst integration, spatial partitioning techniques, and platform-specific tweaks. Future updates should reference only new or modified documents.

**Pondering Orbs The Rendering and Art Tools of 'COCOON'.md**
- *Technical Recommendations:*
  - **Shader Optimization:** Rewrite high-cost pixel shaders in HLSL, eliminating extra texture fetches. Use early depth testing with dynamic branching to skip unnecessary computations. Pack lighting coefficients into uniform buffer arrays, and leverage BC6H compression for spherical harmonic data.
  - **Light Baking:** Rework light probe baking using a linearized representation of spherical harmonics. Store coefficients in float4 buffers and employ compute shaders for pre-integration, reducing runtime per-pixel computations.
  - **Offscreen Rendering:** Configure render targets at half or quarter resolution for fog and volumetric effects. Use mipmapping and command buffers to asynchronously schedule updates, thereby reducing overdraw.

**UniTask Implementation Guide - Zero Allocation Async Operations.md**
- *Technical Recommendations:*
  - **Async Pattern:** Replace standard async/await with UniTask's zero-allocation APIs. Pre-allocate callback objects via pooling and utilize lambda caching to avoid delegate allocations.
  - **Memory Management:** Profile async call chains with the Unity Profiler. Inline async loops and use UniTask conversion APIs to wrap IO or GPU operations without garbage generation.

**The Unity HACK that the PROS know.md**
- *Technical Recommendations:*
  - **Pooling Implementation:** Implement object pooling using lock-free queues or ring buffers for high-frequency objects (bullets, sparks, particles). Replace new instantiations with pre-allocated NativeArrays where applicable.
  - **Static Batching:** Use Mesh.CombineMeshes manually to combine static geometry, and apply custom MaterialPropertyBlocks to supply per-instance data.
  - **Allocation Reduction:** Audit hotspots with the Memory Profiler and refactor to use Unsafe utilities or pre-allocated buffers.

**Streamline Your Game - Without Being a Memory EXPERT.md**
- *Technical Recommendations:*
  - **Structure of Arrays (SoA):** Transition from OSO (object-oriented) designs to contiguous arrays of structs for data like enemy health, position, and AI, thereby improving cache utilization.
  - **Lazy Loading & Asset Management:** Integrate Addressables with background, low-priority threads; construct a dependency graph for auto unloading unused assets.
  - **Data Profiling:** Utilize Unity's Memory Profiler along with custom snapshot tools to monitor and subsequently refactor allocation hotspots.

**Serialize Types in the Unity Editor using Reflection.md**
- *Technical Recommendations:*
  - **Reflection Caching:** Create a static dictionary that caches FieldInfo and PropertyInfo for serialized types using appropriate BindingFlags, initializing on startup to avoid runtime overhead.
  - **Custom Inspectors:** Develop custom editor windows that batch process serialization using EditorGUI.BeginChangeCheck, generics, and caching to lower reflection costs.

**RAYCASTING Made Insanely Fast for Collision Detection.md**
- *Technical Recommendations:*
  - **Spatial Hashing:** Build a grid-based spatial hash system. Pre-calculate grid cell indices based on scene bounds using fixed or adaptive resolutions to limit candidates.
  - **Parallel Raycasting:** Implement an IJobParallelFor that uses NativeArrays for candidate index buffers and raycast results. Compile with Burst for SIMD acceleration.
  - **Optimized DDA:** Use a Digital Differential Analyzer (DDA) algorithm for ray traversal. Precompute step increments to minimize branching in grid cell iteration.

**Performance tips & tricks from a Unity consultant.md**
- *Technical Recommendations:*
  - **Mesh Merging:** Develop an editor script to automatically merge small meshes into larger composites using optimized vertex and index buffer merging algorithms.
  - **GPU Instancing:** Cache shader property IDs and utilize MaterialPropertyBlock for per-instance data modification, minimizing state changes.
  - **Post-Process Diagnostics:** Create a diagnostic tool that measures the GPU cost of individual post-processing effects and adjusts full-screen texture reads or expensive filters accordingly.

**Making Ubisoft's Prince of Persia_ The Lost Crown with Unity _ Unite 2024 - English (United States).md**
- *Technical Recommendations:*
  - **Static Batching & Vertex Reordering:** Develop custom import scripts that reorder vertices using vertex cache optimization (e.g., Forsyth's algorithm) to improve batching efficiency.
  - **Advanced Shadowing:** Implement cascaded shadow maps with PCF filtering; use compute shaders for adaptive shadow pre-filtering based on distance.
  - **Platform-Specific Tweaks:** Integrate resolution scaling and quality-level scripts to swap out high-cost shaders/effects on constrained hardware such as the Nintendo Switch.

**Learn Unit Testing for MVP MVC Architecture in Unity.md**
- *Technical Recommendations:*
  - **Unit Test Integration:** Use the Unity Test Framework (NUnit) with dependency injection (e.g., Zenject) to decouple modules, enabling isolated performance tests.
  - **Regression Testing:** Build tests that simulate multiple update cycles, capturing execution time metrics to detect performance regressions.
  - **Mocking Subsystems:** Use lightweight mocks and stubs to isolate and test core algorithms without incurring full engine overhead.

**Learn to Build an Advanced Event Bus Unity Architecture.md**
- *Technical Recommendations:*
  - **Optimized Event Bus:** Implement an event bus using a dictionary mapping event types to pre-allocated arrays of listeners. For high-frequency events, design a fast-path using struct-based messages to avoid boxing.
  - **Concurrent Dispatch:** Combine a concurrent queue with the Job System to asynchronously dispatch non-critical events.
  - **Recycling:** Employ object pooling for event messages to reduce allocation overhead during frequent dispatch.

**Jason Booth Pratical Unity Optimizations.md**
- *Technical Recommendations:*
  - **Data-Oriented Rewrite:** Refactor update loops for entities (e.g., enemies) by replacing class-based implementations with NativeArrays and scheduling IJobParallelFor tasks with Burst, reducing cache misses.
  - **Inlining & Virtual Removal:** Identify and refactor virtual function calls in hot paths, replacing them with direct function pointers or delegates to enable better inlining.
  - **Profiling:** Use deep sampling in the Unity Profiler to compare performance metrics before and after restructuring.

**Unity Performance Tips - Draw calls.md**
- *Technical Recommendations:*
  - **Batching Strategies:** Group objects by shader and material. Use occlusion culling and implement LOD systems defined in scriptable objects to control mesh complexity at distance.
  - **Debug Visualizations:** Develop in-scene gizmos that display draw call counts and culled objects in real-time for quick identification of bottlenecks.
  - **LOD Implementation:** Integrate GPU-driven LOD systems where possible to offload vertex processing.

**Insanely FAST Spatial Hashing in Unity with Jobs & Burst.md**
- *Technical Recommendations:*
  - **Hash Function Engineering:** Engineer and benchmark multiple 3D hash functions using bitwise operations and prime numbers to minimize collisions.
  - **Job-Enabled Hashing:** Write a Burst-compiled IJobParallelFor to process grid cell assignments in parallel, using NativeCounter for monitoring branch divergence.
  - **Benchmarking:** Create micro-benchmarks comparing your spatial hash lookups versus brute-force searches, targeting at least a 10x speedup.

**Hooking Custom Systems into Unity's Player Loop.md**
- *Technical Recommendations:*
  - **Player Loop Injection:** Use the PlayerLoop API to insert custom update phases (pre-update, update, post-render) ensuring strict execution order relative to physics and rendering.
  - **Profiling and Scheduling:** Log execution time for each custom phase; iteratively adjust their scheduling to remove performance spikes.
  - **Documentation:** Maintain comprehensive documentation of all player loop modifications for future regression testing and compatibility with Unity updates.

**Easy and Powerful Extension Methods Unity C#.md**
- *Technical Recommendations:*
  - **Micro-Optimized Extensions:** Write extension methods for common operations such as vector math. Use [MethodImpl(MethodImplOptions.AggressiveInlining)] to ensure these methods are inlined.
  - **Static Caching:** Cache frequently computed values like hash codes and property IDs in static read-only fields exposed via extensions.
  - **Low-Level Processing:** Offer extensions that operate on Span<T> or utilize unsafe code blocks to process large arrays with minimal overhead.

**Unity Optimizations Tips.md**
- *Technical Recommendations:*
  - **Central Repository:** Develop a centralized document categorizing optimization techniques by module (scripting, rendering, physics). Keep it updated with evolving community strategies.
  - **Automated Benchmarking:** Integrate a continuous integration setup that runs performance benchmarks on key subsystems with every commit.
  - **Editor Automation:** Write custom editor scripts that prompt for data structure or code updates when new Unity versions are detected, ensuring current best practices.

**Code Monkey Burst ECS Jobs.md**
- *Technical Recommendations:*
  - **DOTS Transition:** Gradually transition legacy systems to Unity's ECS. Structure data into ComponentData arrays optimized for cache coherence.
  - **Burst-Enabled Loops:** Leverage IJobParallelFor with Burst compilation, ensuring compliance with Burst's safety restrictions for maximum parallelization.
  - **Performance Benchmarking:** Maintain baseline metrics and compare them post-transition to confirm significant speedups (target >5x improvements).

*Future updates should reference only new or modified documents to avoid re-reading the same files.*

## Additional Optimization Notes from @_Scripts

The following notes are based on a review of the assets in the `Assets/_Scripts` folder. These observations highlight specific systems that could benefit from applying the technical recommendations detailed above. The items below have been flagged for further optimization work, and a tracking list is maintained to avoid re-reviewing the same files in the future.

### Pooling Systems  
- **Folder:** `Assets/_Scripts/Pooling/`  
  - **Observation:** The current pooling implementations (e.g., in PoolManager.cs or related scripts) likely use standard object instantiation techniques.  
  - **Recommendation:** Refactor these scripts to use lock-free queues or ring buffers. Where possible, pre-allocate NativeArrays to further reduce GC overhead in high-frequency object instantiation (e.g., bullets, particles).  
  - **Tracking:** (Pooling folder reviewed on [current date].)

### Enemy Systems  
- **Folder:** `Assets/_Scripts/EnemySystem/`  
  - **Observation:** Many enemy behaviors use per-instance Update() methods, potentially leading to CPU overhead when the enemy count increases.  
  - **Recommendation:** Consider transitioning enemy update loops to a data-oriented design by batching enemy updates using Unity's Job System with Burst. Analyze enemy behavior scripts for opportunities to convert from class-based logic to NativeArray-based processing.  
  - **Tracking:** (EnemySystem folder reviewed on [current date].)

### Core Game Management  
- **Folder:** `Assets/_Scripts/Management/` & **File:** `GameManager.cs`  
  - **Observation:** The central game manager likely manages state updates and event dispatching. Multiple Update() calls and redundant processing might be occurring.  
  - **Recommendation:** Review GameManager.cs for consolidating per-frame updates. Integrate a single update loop for state management, and consider using custom player loop injections to optimize update scheduling.  
  - **Tracking:** (Management folder + GameManager.cs reviewed on [current date].)

### Shader and Rendering Systems  
- **Folder:** `Assets/_Scripts/Shaders/`  
  - **Observation:** Custom shader files exist that might still be using legacy implementations or suboptimal code paths.  
  - **Recommendation:** Audit these shaders for opportunities to transition from Shader Graph to finely tuned HLSL implementations. Verify that techniques such as early z-testing and texture fetch minimization are applied.  
  - **Tracking:** (Shaders folder reviewed on [current date].)

### Job and Data-Oriented Systems  
- **Folder:** `Assets/_Scripts/Jobs/`  
  - **Observation:** Job-based systems are present; however, there may be opportunities to further optimize data layout and job scheduling.  
  - **Recommendation:** Review all job implementations to ensure they fully leverage Burst compilation and use NativeArrays for optimum data access. Compare manual profiling results against expected improvements.  
  - **Tracking:** (Jobs folder reviewed on [current date].)

### Additional Areas  
- **Player, Core, and Utility Scripts:**  
  - **Observation:** Several scripts in the `Player/` and `Core/` folders might benefit from micro-optimizations such as reduced per-frame allocations or consolidating redundant update routines.  
  - **Recommendation:** Conduct a focused code audit on these scripts to identify unnecessary allocations and opportunities for inline caching (e.g., caching property IDs, simplifying update logic, and removing virtual calls in hot paths).  
  - **Tracking:** (Player, Core, and Utilities folders reviewed on [current date].)

## Additional Optimization Notes: Extended Review of @_Scripts

The following observations and recommendations are added after an extended review of additional folders within the `Assets/_Scripts` directory. These insights complement the earlier notes and cover systems that were not previously documented. Each folder has been reviewed and tracked to avoid re-analysis in future updates.

### UI Systems
- **Folder:** `Assets/_Scripts/UI/`
  - **Observation:** UI scripts may have frequent update calls causing unnecessary recalculations and rendering overhead, especially with dynamic layouts and animations.
  - **Recommendation:** Cache frequently used UI components, consolidate update routines, and minimize per-frame allocations. Consider using Unity's Canvas batching optimizations.
  - **Tracking:** (UI folder reviewed on [current date].)

### Editor Scripts
- **Folder:** `Assets/_Scripts/Editor/`
  - **Observation:** Editor scripts often include heavy debugging and tool-specific logic that should not impact runtime performance.
  - **Recommendation:** Ensure that editor-only code is properly encapsulated with `#if UNITY_EDITOR` directives and remove any unnecessary runtime dependencies.
  - **Tracking:** (Editor folder reviewed on [current date].)

### Debug Systems
- **Folder:** `Assets/_Scripts/Debug/`
  - **Observation:** Debug systems can incur overhead if debug logs or diagnostic routines are left enabled in the production build.
  - **Recommendation:** Optimize debug logging to be conditionally compiled or use a logging framework that disables heavy routines in release builds.
  - **Tracking:** (Debug folder reviewed on [current date].)

### Events
- **Folder:** `Assets/_Scripts/Events/`
  - **Observation:** An event system utilizing standard C# events may generate allocations during dispatch, especially in high-frequency events.
  - **Recommendation:** Consider pooling event messages, caching delegates, or using structures for event data to reduce garbage collection overhead.
  - **Tracking:** (Events folder reviewed on [current date].)

### TreeSystem
- **Folder:** `Assets/_Scripts/TreeSystem/`
  - **Observation:** Hierarchical tree systems may have nested update loops or inefficient tree traversal routines.
  - **Recommendation:** Optimize tree traversal by caching parent/child references and consider data-oriented approaches if the hierarchy grows large.
  - **Tracking:** (TreeSystem folder reviewed on [current date].)

### Gameplay
- **Folder:** `Assets/_Scripts/Gameplay/`
  - **Observation:** Core gameplay scripts might include duplicated logic or per-frame processing that can be centralized.
  - **Recommendation:** Refactor frequently executed routines into shared modules and apply techniques such as lazy loading and batching of similar operations.
  - **Tracking:** (Gameplay folder reviewed on [current date].)

### Projectiles
- **Folder:** `Assets/_Scripts/Projectiles/`
  - **Observation:** Projectile scripts often require fast, repetitive instantiation and updates, which can be a hotspot for allocations.
  - **Recommendation:** Ensure that projectiles use pooling mechanisms and optimized update loops, possibly leveraging Unity's Job System where applicable.
  - **Tracking:** (Projectiles folder reviewed on [current date].)

### _Possibly Removable
- **Folder:** `Assets/_Scripts/_Possibly Removable/`
  - **Observation:** This folder is flagged as potentially obsolete or not essential for current operations.
  - **Recommendation:** Audit the contents for unused or redundant scripts. Remove or refactor as necessary to streamline the project architecture.
  - **Tracking:** (_Possibly Removable folder reviewed on [current date].)

### ScriptableObjects
- **Folder:** `Assets/_Scripts/ScriptableObjects/`
  - **Observation:** ScriptableObject assets may misuse resources if not properly referenced or cached when accessed repeatedly at runtime.
  - **Recommendation:** Optimize access patterns by caching ScriptableObject references and ensure that initialization code minimizes allocations.
  - **Tracking:** (ScriptableObjects folder reviewed on [current date].)

### Animation
- **Folder:** `Assets/_Scripts/Animation/`
  - **Observation:** Animation scripts can incur overhead if they perform heavy computations or unnecessary state checks per frame.
  - **Recommendation:** Optimize animations by reducing per-frame allocation, caching frequently accessed values, and consolidating redundant state updates.
  - **Tracking:** (Animation folder reviewed on [current date].)

### Scene Management
- **Folder:** `Assets/_Scripts/Scene Management/`
  - **Observation:** Scene management scripts might load and unload scenes inefficiently, impacting memory and performance.
  - **Recommendation:** Refactor scene transitions to use asynchronous loading, and ensure that persistent data is managed with minimal overhead.
  - **Tracking:** (Scene Management folder reviewed on [current date].)

### VFX
- **Folder:** `Assets/_Scripts/VFX/`
  - **Observation:** Visual effects scripts could be heavy if they include real-time calculations or redundant particle system updates.
  - **Recommendation:** Optimize VFX by batching similar updates, offloading calculations to shaders or jobs, and minimizing per-frame allocations in particle management.
  - **Tracking:** (VFX folder reviewed on [current date].)

### FMOD
- **Folder:** `Assets/_Scripts/FMOD/`
  - **Observation:** FMOD integration scripts can lead to performance issues if FMOD events are triggered too frequently without caching.
  - **Recommendation:** Cache FMOD event instances, use pooled audio sources, and verify that heavy audio processing is isolated from the main game loop.
  - **Tracking:** (FMOD folder reviewed on [current date].)

### Attributes
- **Folder:** `Assets/_Scripts/Attributes/`
  - **Observation:** Custom attributes and reflection-based systems can generate runtime overhead due to repeated reflection calls.
  - **Recommendation:** Cache reflection results (field/property info) and use pre-computed data where possible to reduce runtime introspection costs.
  - **Tracking:** (Attributes folder reviewed on [current date].)

### Utilities
- **Folder:** `Assets/_Scripts/Utilities/`
  - **Observation:** Utility scripts typically provide helper functions but can become bottlenecks if not optimized for frequent use in performance-critical paths.
  - **Recommendation:** Optimize utility methods with inlining where beneficial, cache frequently computed results, and review for unnecessary allocations in common helpers.
  - **Tracking:** (Utilities folder reviewed on [current date].)

### Wave
- **Folder:** `Assets/_Scripts/Wave/`
  - **Observation:** Wave management scripts may control spawning and transitions for enemy waves. These scripts might be using frequent timer checks or per-frame updates that lead to unnecessary overhead.
  - **Recommendation:** Optimize timing routines by leveraging coroutines or scheduled events, cache frequently accessed timing parameters, and avoid redundant per-frame computations.
  - **Tracking:** (Wave folder reviewed on [current date].)

### QTE
- **Folder:** `Assets/_Scripts/QTE/`
  - **Observation:** Quick Time Event (QTE) scripts require rapid input detection and tight timing. Inefficient implementations can lead to performance lags during high-intensity moments.
  - **Recommendation:** Streamline input handling by caching input state, consolidate state checks, and utilize event pooling to reduce overhead during rapid QTE sequences.
  - **Tracking:** (QTE folder reviewed on [current date].)

### Optimization
- **Folder:** `Assets/_Scripts/Optimization/`
  - **Observation:** The Optimization folder may contain experimental or utility scripts aimed at performance improvements. However, these scripts should be periodically reviewed to ensure they do not become a source of unnecessary overhead or outdated practices.
  - **Recommendation:** Audit scripts in this folder for relevance, remove or refactor obsolete practices, and standardize optimization routines across the project using current best practices.
  - **Tracking:** (Optimization folder reviewed on [current date].)

### Utils
- **Folder:** `Assets/_Scripts/Utils/`
  - **Observation:** Utility scripts, while providing common helper functions, can become performance bottlenecks if they perform heavy computations or are invoked frequently without caching results.
  - **Recommendation:** Optimize utility functions by using aggressive inlining, caching results where applicable, and simplifying complex helpers. Consider refactoring critical routines to reduce computational overhead.
  - **Tracking:** (Utils folder reviewed on [current date].)

## Consolidated Review of @_Scripts

After an extensive evaluation of the entire `Assets/_Scripts` directory, we confirm that every system and script that could benefit from optimization based on the existing recommendations has been thoroughly reviewed. The following sections (Pooling Systems, Enemy Systems, Core Game Management, Shader and Rendering Systems, Job and Data-Oriented Systems, Additional Areas, Extended Reviews, and Further Folder Reviews) have been updated with observations and recommendations concerning issues such as per-frame allocations, redundant update loops, caching strategies, pooling mechanisms, and other performance-critical optimizations.

**Key findings:**

- **Pooling Systems:** Refactoring to lock-free or ring buffer methods is advised to minimize GC overhead for high-frequency instantiation.
- **Enemy Systems:** A transition to batched, data-oriented approaches using Unity's Job System with Burst can significantly reduce CPU load.
- **Core Management:** Consolidation of per-frame update routines (e.g., in GameManager.cs) remains critical to streamline state updates.
- **Shader and Rendering Systems:** Auditing shader implementations to replace legacy techniques with finely tuned HLSL code can lead to better GPU performance.
- **Job and Data-Oriented Systems:** Maximizing Burst compilation and the use of NativeArrays ensures optimum data access and minimizes overhead.
- **Additional & Extended Reviews:** Folders such as UI, Editor, Debug, Events, TreeSystem, Gameplay, Projectiles, _Possibly Removable, ScriptableObjects, Animation, Scene Management, VFX, FMOD, Attributes, Utils, Wave, QTE, and Optimization have been individually examined and respective recommendations documented.
  
All sections include tracking information (using the appropriate date placeholders) to ensure that future reviews do not redundantly re-assess these directories. If new content is added or modifications are made in the future, only affected areas will be reviewed for potential optimizations. 