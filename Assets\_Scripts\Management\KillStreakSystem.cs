using UnityEngine;
using System.Collections.Generic;

namespace BTR
{
    /// <summary>
    /// Manages kill streak mechanics including multipliers, time windows, and milestone tracking.
    /// Integrates with the centralized scoring system to provide dynamic score multipliers.
    /// </summary>
    [System.Serializable]
    public class KillStreakMilestone
    {
        [SerializeField] private int killCount;
        [SerializeField] private string milestoneName;
        [SerializeField] private int bonusScore;
        
        public int KillCount => killCount;
        public string MilestoneName => milestoneName;
        public int BonusScore => bonusScore;
        
        public KillStreakMilestone(int kills, string name, int bonus = 0)
        {
            killCount = kills;
            milestoneName = name;
            bonusScore = bonus;
        }
    }

    public class KillStreakSystem : MonoBehaviour
    {
        [Header("Kill Streak Settings")]
        [SerializeField] private float streakTimeWindow = 3f;
        [SerializeField] private float baseMultiplier = 1.5f;
        [SerializeField] private float multiplierIncrement = 0.5f;
        [SerializeField] private float maxMultiplier = 5f;
        [SerializeField] private bool enableDebugLogs = false;
        
        [Header("Milestone Configuration")]
        [SerializeField] private List<KillStreakMilestone> milestones = new List<KillStreakMilestone>();
        
        // Current streak state
        private int currentStreak = 0;
        private float lastKillTime = 0f;
        private float currentMultiplier = 1f;
        private bool isStreakActive = false;
        
        // Events for integration
        public System.Action<int, float> OnStreakChanged;
        public System.Action<int, string, int> OnMilestoneReached; // streak, milestone name, bonus score
        public System.Action OnStreakEnded;
        
        // Cached references
        private GameEvents gameEvents => GameEventsManager.Instance?.Events;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeDefaultMilestones();
        }
        
        private void Start()
        {
            ResetStreak();
            
            if (enableDebugLogs)
            {
                Debug.Log($"[KillStreakSystem] Initialized - Time Window: {streakTimeWindow}s, Base Multiplier: {baseMultiplier}x, Max: {maxMultiplier}x");
            }
        }
        
        private void Update()
        {
            // Check if streak should expire
            if (isStreakActive && Time.time - lastKillTime > streakTimeWindow)
            {
                EndStreak();
            }
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Called when an enemy is killed. Updates streak and calculates new multiplier.
        /// </summary>
        public void OnEnemyKilled()
        {
            float currentTime = Time.time;
            
            // Check if this kill is within the time window
            if (currentStreak == 0 || currentTime - lastKillTime <= streakTimeWindow)
            {
                currentStreak++;
                lastKillTime = currentTime;
                isStreakActive = true;
                
                // Calculate new multiplier
                UpdateMultiplier();
                
                // Check for milestones
                CheckMilestones();
                
                // Fire events
                OnStreakChanged?.Invoke(currentStreak, currentMultiplier);
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[KillStreakSystem] Kill streak: {currentStreak} (multiplier: {currentMultiplier:F1}x)");
                }
            }
            else
            {
                // Time window expired, start new streak
                ResetStreak();
                OnEnemyKilled(); // Recursive call to handle the new kill
            }
        }
        
        /// <summary>
        /// Gets the current score multiplier based on kill streak.
        /// </summary>
        public float GetCurrentMultiplier()
        {
            return currentMultiplier;
        }
        
        /// <summary>
        /// Gets the current kill streak count.
        /// </summary>
        public int GetCurrentStreak()
        {
            return currentStreak;
        }
        
        /// <summary>
        /// Returns true if a kill streak is currently active.
        /// </summary>
        public bool IsStreakActive()
        {
            return isStreakActive && currentStreak > 1;
        }
        
        /// <summary>
        /// Gets the time remaining in the current streak window.
        /// </summary>
        public float GetTimeRemaining()
        {
            if (!isStreakActive) return 0f;
            return Mathf.Max(0f, streakTimeWindow - (Time.time - lastKillTime));
        }
        
        /// <summary>
        /// Manually resets the kill streak (useful for testing or special events).
        /// </summary>
        public void ResetStreak()
        {
            bool wasActive = isStreakActive;
            
            currentStreak = 0;
            currentMultiplier = 1f;
            isStreakActive = false;
            lastKillTime = 0f;
            
            if (wasActive)
            {
                OnStreakEnded?.Invoke();
                
                if (enableDebugLogs)
                {
                    Debug.Log("[KillStreakSystem] Streak reset");
                }
            }
        }
        
        #endregion
        
        #region Private Methods
        
        private void InitializeDefaultMilestones()
        {
            if (milestones.Count == 0)
            {
                milestones.Add(new KillStreakMilestone(2, "Double Kill", 5));
                milestones.Add(new KillStreakMilestone(3, "Triple Kill", 10));
                milestones.Add(new KillStreakMilestone(4, "Multi Kill", 15));
                milestones.Add(new KillStreakMilestone(5, "Mega Kill", 25));
                milestones.Add(new KillStreakMilestone(7, "Ultra Kill", 35));
                milestones.Add(new KillStreakMilestone(10, "Monster Kill", 50));
            }
        }
        
        private void UpdateMultiplier()
        {
            if (currentStreak <= 1)
            {
                currentMultiplier = 1f;
            }
            else
            {
                // Calculate multiplier: base + (streak - 2) * increment
                // Streak 2 = base multiplier, each additional kill adds increment
                currentMultiplier = baseMultiplier + (currentStreak - 2) * multiplierIncrement;
                currentMultiplier = Mathf.Min(currentMultiplier, maxMultiplier);
            }
        }
        
        private void CheckMilestones()
        {
            foreach (var milestone in milestones)
            {
                if (currentStreak == milestone.KillCount)
                {
                    OnMilestoneReached?.Invoke(currentStreak, milestone.MilestoneName, milestone.BonusScore);
                    
                    // Fire game event for UI feedback
                    if (gameEvents != null)
                    {
                        gameEvents.TriggerStreakMilestone(currentStreak, milestone.MilestoneName);
                    }
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[KillStreakSystem] Milestone reached: {milestone.MilestoneName} ({currentStreak} kills) - Bonus: {milestone.BonusScore}");
                    }
                    
                    break;
                }
            }
        }
        
        private void EndStreak()
        {
            if (isStreakActive)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[KillStreakSystem] Streak ended: {currentStreak} kills, {currentMultiplier:F1}x multiplier");
                }
                
                OnStreakEnded?.Invoke();
                ResetStreak();
            }
        }
        
        #endregion
        
        #region Editor/Debug Methods
        
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugTriggerKill()
        {
            OnEnemyKilled();
        }
        
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugResetStreak()
        {
            ResetStreak();
        }
        
        public string GetDebugInfo()
        {
            return $"Streak: {currentStreak}, Multiplier: {currentMultiplier:F1}x, Active: {isStreakActive}, Time Remaining: {GetTimeRemaining():F1}s";
        }
        
        #endregion
    }
}
