# BTR Current Documentation

*Last Updated: 2025-07-18*

## Quick Navigation

### 🏗️ Architecture
- **[[Architecture/System Overview]]** - Main system architecture
- **[[Architecture/Tree System Architecture]]** - Procedural tree generation
- **[[Architecture/Radar System Architecture]]** - Entity tracking and visualization
- **[[Architecture/Performance Monitoring System]]** - Performance analysis and optimization
- **[[Architecture/Utility Systems]]** - Helper functions and extensions
- **[[Architecture/audio_system_architecture]]** - FMOD integration
- **[[Architecture/gameplay_systems_architecture]]** - Core gameplay systems
- **[[Architecture/ui_system_architecture]]** - UI system design
- **[[Architecture/vfx_system_architecture]]** - Visual effects architecture

### ⚡ Performance
- **[[Performance/Editor Performance Investigation Log]]** - Current performance issues
- **[[Performance/Editor Performance Optimization Guide]]** - Optimization strategies
- **[[Performance/MMFeedbacks and FMOD Optimization Guide]]** - Audio optimization
- **[[Performance/Play Mode Entry Optimization Guide]]** - Editor workflow optimization

### 🐛 Bug Tracking
- **[[Bug Tracking/KnownIssues]]** - Current known issues
- **[[Bug Tracking/Resolutions]]** - Resolved issues and solutions

### 🔧 Development
- **[[Development/Unity Domain Reload Requirements]]** - Critical technical requirements

## System Quick Reference

### Core Managers
- **GameManager** - Central game state coordination
- **AudioManager** - FMOD integration and audio management
- **TimeManager** - Time control and chronos integration
- **SceneManagerBTR** - Scene loading and transitions
- **ScoreManager** - Scoring and progression

### Major Systems
- **Player System** - Movement, health, shooting, locking
- **Enemy System** - AI behaviors, combat, configuration
- **Projectile System** - High-performance projectile management
- **UI System** - Player interface and HUD management

### Key Interfaces
- **IEventCompatible** - Event system integration
- **IDamageable** - Unified damage handling
- **IEnemy** - Enemy behavior contracts
- **IProjectile** - Projectile system contracts

## Recent Updates

### 2025-07-18
- Reorganized documentation structure
- Created consolidated architecture overview
- Moved outdated content to archive
- Updated project references from "BTR" to "BTR"

### 2025-04-09
- Enemy system architecture updates
- Performance optimization documentation
- Bug tracking system improvements

## Development Workflow

1. **Architecture Changes** - Update relevant architecture documents
2. **Performance Issues** - Document in Performance section
3. **Bug Fixes** - Update Bug Tracking with resolution
4. **New Features** - Update system architecture documentation

## Archive Policy

- **Current** - Active development documentation
- **Archive/Pre-2024** - Historical development logs and research
- **Archive/Business** - Funding and business documentation
- **TrashBin** - Deprecated documentation (review before deletion)

## Navigation Tips

- Use `[[]]` links for internal navigation
- Check last updated dates for currency
- Reference architecture diagrams for system understanding
- Follow the established tagging system for organization