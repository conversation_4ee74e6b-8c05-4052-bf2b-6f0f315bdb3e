---
title: Better Save/Load System using Data Binding in Unity
tags: [Unity, DataBinding, SaveSystem]
date: 2025-01-20
---

# Better Save/Load System using Data Binding in Unity

## System Architecture
```mermaid
graph TD
    A[SaveLoadSystem] --> B[DataService]
    B --> C[Serializer]
    A --> D[Player]
    A --> E[Inventory]
    D --> F[PlayerData]
    E --> G[InventoryData]
```

## Core Components
### 1. Serializer Interface
```csharp
public interface ISerializer {
    string Serialize<T>(T obj);
    T Deserialize<T>(string data);
}
```

### 2. Data Service Interface
```csharp
public interface IDataService {
    void Save<T>(T data, string fileName, bool overwrite = true);
    T Load<T>(string fileName);
    void Delete(string fileName);
    void DeleteAll();
    IEnumerable<string> ListSaves();
}
```

## Implementation

### JSON Serializer
```csharp
public class JsonSerializer : ISerializer {
    public string Serialize<T>(T obj) {
        return JsonUtility.ToJson(obj);
    }

    public T Deserialize<T>(string data) {
        return JsonUtility.FromJson<T>(data);
    }
}
```

### File Data Service
```csharp
public class FileDataService : IDataService {
    private readonly ISerializer _serializer;
    private readonly string _dataPath;
    private const string FileExtension = ".json";

    public FileDataService(ISerializer serializer) {
        _serializer = serializer;
        _dataPath = Application.persistentDataPath;
    }

    public void Save<T>(T data, string fileName, bool overwrite = true) {
        var fileLocation = Path.Combine(_dataPath, fileName + FileExtension);
        
        if (!overwrite && File.Exists(fileLocation)) {
            throw new IOException("File already exists");
        }
        
        File.WriteAllText(fileLocation, _serializer.Serialize(data));
    }

    public T Load<T>(string fileName) {
        var fileLocation = Path.Combine(_dataPath, fileName + FileExtension);
        
        if (!File.Exists(fileLocation)) {
            throw new FileNotFoundException();
        }
        
        return _serializer.Deserialize<T>(File.ReadAllText(fileLocation));
    }
}
```

## Data Binding System

### Serializable GUID
```csharp
[System.Serializable]
public struct SerializableGuid {
    public int Part1;
    public int Part2;
    public int Part3;
    public int Part4;
    
    // Comparison and conversion methods...
}
```

### ISavable Interface
```csharp
public interface ISavable {
    SerializableGuid Id { get; }
}
```

### IBindable Interface
```csharp
public interface IBindable<T> where T : ISavable {
    void Bind(T data);
}
```

## Player Implementation

### Player Data
```csharp
[System.Serializable]
public class PlayerData : ISavable {
    public SerializableGuid Id { get; set; }
    public Vector3 Position;
    public Quaternion Rotation;
}
```

### Player Binding
```csharp
public class Player : MonoBehaviour, IBindable<PlayerData> {
    private PlayerData _data;
    
    public void Bind(PlayerData data) {
        _data = data;
        transform.position = data.Position;
        transform.rotation = data.Rotation;
    }

    private void Update() {
        if (_data != null) {
            _data.Position = transform.position;
            _data.Rotation = transform.rotation;
        }
    }
}
```

## Inventory System

### Inventory Data
```csharp
[System.Serializable]
public class InventoryData : ISavable {
    public SerializableGuid Id { get; set; }
    public List<ItemData> Items;
    public int Capacity;
    public int Coins;
}
```

### Inventory Binding
```csharp
public class Inventory : MonoBehaviour, IBindable<InventoryData> {
    private InventoryData _data;
    
    public void Bind(InventoryData data) {
        _data = data;
        
        if (data.Items == null || data.Items.Count == 0) {
            // Initialize new inventory
            data.Items = new List<ItemData>(Capacity);
            // Add starter items...
        }
        
        // Sync inventory UI with data
    }
}
```

## Save/Load System

### Game Data Structure
```csharp
[System.Serializable]
public class GameData {
    public string Name;
    public string CurrentLevel;
    public PlayerData Player;
    public InventoryData Inventory;
}
```

### SaveLoadSystem Implementation
```csharp
public class SaveLoadSystem : PersistentSingleton<SaveLoadSystem> {
    private IDataService _dataService;
    private GameData _currentGameData;

    protected override void Awake() {
        base.Awake();
        _dataService = new FileDataService(new JsonSerializer());
    }

    public void NewGame(string gameName) {
        _currentGameData = new GameData {
            Name = gameName,
            CurrentLevel = "Demo",
            Player = new PlayerData(),
            Inventory = new InventoryData()
        };
        
        SceneManager.LoadScene(_currentGameData.CurrentLevel);
    }

    public void SaveGame() {
        _dataService.Save(_currentGameData, _currentGameData.Name);
    }

    public void LoadGame(string gameName) {
        _currentGameData = _dataService.Load<GameData>(gameName);
        SceneManager.LoadScene(_currentGameData.CurrentLevel);
    }
}
```

## Best Practices
1. Use [[Data Binding]] to keep game state synchronized
2. Implement proper error handling for file operations
3. Use [[GUIDs]] for unique identification of game objects
4. Consider [[Encryption]] for sensitive game data
5. Implement [[Versioning]] for save file compatibility

## Extensions
- [[Cloud Saving Integration]]
- [[Binary Serialization]]
- [[Data Compression]]
- [[Save File Encryption]]
- [[Automatic Backup System]]