# Ryan Clark Notes

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Research/Ryan%20Clark%20Notes/Untitled.png)

Evaluate the moats of your competition

Roll your snowball
Select game ideas with strong pre-play hooks

Study the market, know the zeitgeist

Seek low frequency events but don't rely on them
Find a sustainable rate of change

Play to your strengths, get help from others to shore up weaknesses

Pre game hooks

- Unique visual aesthetics
- Music-based gameplay: music-responsive mechanics

IDEA: Thumper

"Rhythm Violence": Thumper is branded as a rhythm violence game, a unique concept that draws intrigue.

How can I do the same?

‘Dramatic Shooter’

---

June 6th

Displace tool helps round out shapes, use this for geometry!

Revisiting enemy movement to try and address falling off of mesh.

Using AI Destination setter example script as basis of movement.

After much testing, seem to have enemies working properly without falling off a complex mesh! Need to test more mesh and see what works / doesn’t

No rigidbodies on enemies so altering projectile script so that it can work without it

Have this fixed!

Need shooting reticle to appear in center and on top of everything (except player?)

Working well! Could make adjustments

Mobius 5 needs help with enclosing enemies. They fall out of the tube - how to prevent this?

---

June 9th - Power ups

Incentive to get all projectile lock ons filled?

- Damage increase for each projectile that hits
    - Could have bonus / add ons that change what this increase is
    - Default is 1.5 multiplier

Database of enemies to hit ideal score?

Added a lockShooter method to dolly manager, so that the reticle is not flying off to the side when the game starts

---

**June 12**

Updated A* Package to 74, seems like good improvements there

Updated Behavior Designer, and it’s movement pack

Updating A* integration package for BD

Updated Buto

Had some issues with the new A* integration from Behaviour Designer, reverted back to older implementation and not flagging issues

Added an animation trigger for jumping between snakes

**June 13**

Fixed animation issue, working properly now

Removign Rigidbody from Shooting gameobject due to possible physics issues

Thinking about level design and enemies

Pre-Play hooks

Does it have testable pre-play hooks that will come through well in a trailer, screenshot, or text description?

Need to clean up current parts of the level to get things working properly

Ouroboros

3 snakes - 2 outside 1 inside

move along backs of snakes in various directions

Snake Boss

Enemy Designs

5 types based on Shapes

Need variations of those types

Ouroboros

Dodecahedron - basic multilayer enemy

[Enemy Design Ideas](Enemy%20Design%20Ideas%20949d91fffa0d4bb3933b5cb31be9cf8d.md)

Enemy possibilities

- variety of shooting patterns and shapes
- variety of movement types

How do these tie to music?

**June 14**

Learning about Addressables

[How to use Addressables FASTER Loading FREE Memory SMALL Download](https://www.youtube.com/watch?v=C6i_JiRoIfk)

Looked at Build Reports

Learning Dynamic Resolution

Basic CodeMonkey implementation added to feature testing of project.

Need to build on this if I want to actually use it. Need to integrate Frame Timing DX12 features. Refer to Unity Documentation for this

Looking at replacing Rewired with new unity input system.

Did this, most controls adapted. Much easier to add other controls now as well

Ouroboros boss idea

- Shield that only works in forward or backward time. So one snake is shielded at a time, requires you to alternate time to attack each. the shield shifts back and forth.

**June 18**

Looking over memory usage and addressables again

Applying compression to game assets in scene, this is reversible incase it has adverse effects.

Ghostrunner level design talk - go over this again! Possible good advice

**June 19**

Go through my youtube history for analyzing best videos

Also GIC conference has amazing optimization talk - look at raycast advice

**June 20**

Looking at some optimizations in this talk

[#BestTalkAward Universal Optimization Guide For Unity || Grzegorz Wątroba || Wayward Preacher](https://www.youtube.com/watch?v=V9RM-1ik5C0)

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Research/Ryan%20Clark%20Notes/Untitled%201.png)

Changed Renderer on Render Camera - gained some performance! Look into changing again if errors occur

Changed Crosshair to use NonAlloc Raycast - if there are issues check the size of the arrays

Shooter Movement issues occuring - removing rigidbody and adjusting Remitcle Movement SPhere to test.

This seems to have fixed things?

Thinking about a dodge or shield mechanic to make things more interesting…..

Glitch time, zap nearby projectiles?

Mechanic Ideas

**6. Filters and Effects (Synthesizer/Ableton Live):** Effects like reverb, delay, flanger, and phaser could be tied to in-game power-ups, causing the game world to behave differently. For instance, "reverb" could create echoes of your shots, "delay" could slow down enemies, and "phaser" could warp the game space.

**9. Sampling (Ableton Live):** Able to "sample" or copy the abilities of defeated enemies, players can use this to their advantage in future encounters.

**1. Sound Design (Synthesizer):** Players can craft their unique sounds (weapons, abilities) using synthesizer principles - adjusting waveforms, oscillators, ADSR (Attack, Decay, Sustain, Release) envelopes, etc. Each parameter could have a specific effect on the weapon or power's behavior.

**4. Automation (Ableton Live):** Much like automating a track in Ableton, players could set paths for their character or shots to follow, providing a more strategic approach to tackling enemies.

**5. Time Stretching (Ableton Live/DJ Mixer):** This feature could be adapted as a game mechanic where the player can stretch or compress time, affecting enemies' speed or the character's reaction time.

**7. Sidechain Compression (Ableton Live):** Drawing from the concept of sidechain compression, when one game action (like a powerful attack) could trigger a decrease in something else (like defense or movement speed), forcing players to balance their actions.

**8. DJ Scratching (DJ Mixer):** Incorporate a 'scratching' mechanic that allows players to rewind or fast-forward the game for a few seconds, useful for avoiding enemy attacks or repositioning.

**10. Envelope Follower (Ableton Live/Synthesizer):** This could be a special ability that allows the player's character to mimic the attack pattern of an enemy temporarily, providing a unique way to counteract enemy attacks.

**1. Sound Waves (Synthesizer):** Every attack could generate a unique sound wave that impacts the environment or enemies in unique ways. Different waveforms (sine, square, triangle, sawtooth) could correspond to different types of attacks.

**2. Pitch Shift (Ableton Live/Synthesizer):** Shifting the pitch could manipulate the gravity or vertical movement of your character. Higher pitch could correspond to jumps or elevated movement, and lower pitch to ducking or diving.

**3. Sync (DJ Mixer):** Like a DJ syncing two tracks, players could need to "sync" their actions with the rhythm of the background music to maximize their attack power or dodge incoming shots.

**5. Cue Points (DJ Mixer/Ableton Live):** Similar to setting cue points in tracks, players can mark certain strategic points in the game level that they can quickly return to or trigger specific events.

**7. LFO (Low-Frequency Oscillator, Synthesizer):** Just like using LFOs to modulate sound parameters, players could adjust an LFO to change aspects of their character's abilities, adding a layer of strategy and customization.

**8. Resampling (Ableton Live):** After defeating certain enemies, the player can "resample" their abilities or characteristics, adding them to their own set of abilities.

**4. Warp Modes (Ableton Live):** Like warping audio in Ableton, players could "warp" the game environment to slow down, speed up, or distort space.

**6. Granular Synthesis (Synthesizer/Ableton Live):** Players could break down their abilities into "grains" and rearrange or modify them to create custom abilities, mirroring the process of granular synthesis.

**5. Sends and Returns (Ableton Live):** Inspired by the concept of sending an audio signal to an effect and returning it to the mix, players could send out attacks or abilities that then return after a period, possibly with added power or additional effects.

**10. Sound Isolation (DJ Mixer):** Players could isolate specific frequencies (like isolating vocals in a track) to expose vulnerabilities in enemies or to uncover hidden paths or items.

Chronos - locked on to 6 bullets - can initiate slow time - destroys bullets, and then you can deal with the rest that are around.

Added inside snake eq trigger - need to make this more interesting but it’s a start

Not working properly, seems triggered even when player is far away. Need to look into this further

Ouroboros

Mix of lockable and not lockable bullets?

Have a shield / pulse wave that destroys them when close

Dichotomy  - call and response?

Glitch time when this happens, or slow down time?

Cycle out abilties? Right stick cycles, left stick does the ability?

If all bullets lockable, then pulsewave not entirely necessary, more choice abilities

Pulsewave can be a frequency sweep?

New ability per section?

Ouroboros - Slow and rewind time

Ophanim -