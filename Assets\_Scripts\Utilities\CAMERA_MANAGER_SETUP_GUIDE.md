# SimpleCameraManager Setup Guide

## 🎥 Problem Solved

The `CinemachineCameraSwitching` component was removed but was handling critical functionality:
- Scene transition camera effects
- Player body/reticle visibility management during transitions
- Event-driven camera state management

## ✅ Solution: SimpleCameraManager

A simplified replacement that maintains essential functionality without requiring complex camera switching.

## 🛠️ Setup Instructions

### **Step 1: Add SimpleCameraManager to Scene**

1. **Find your main camera GameObject** in the scene (usually has a Cinemachine Virtual Camera)
2. **Add the SimpleCameraManager component** to this GameObject
3. **Configure the component**:
   - **Player Body**: Drag your player body GameObject (the visible player model)
   - **Reticle**: Drag your reticle/crosshair GameObject
   - **Transition Duration**: Set to desired transition time (default: 1.0 seconds)
   - **Hide Elements During Transition**: Check this to hide player/reticle during transitions
   - **Enable Debug Logs**: Check for troubleshooting (disable in production)

### **Step 2: Configure Player Facing Forward Event (Optional)**

If you have specific logic that should run when the player faces forward:
1. **Expand the "Player Facing Forward" UnityEvent**
2. **Add listeners** for any components that need to know when player is facing forward
3. **Example**: UI updates, animation triggers, etc.

### **Step 3: Test the Setup**

1. **Play the scene**
2. **Check console logs** - should see:
   - `[GameManager] SimpleCameraManager found and will handle camera transitions`
   - `[SceneSwitchCleanup] SimpleCameraManager found and main camera state restored`
3. **Test scene transitions** - player body/reticle should hide during transitions and restore afterward

## 🎯 What It Does

### **Automatic Event Handling**:
- `OnTransitionCameraRequested` - Triggers transition effect
- `OnStartingTransition` - Triggers transition effect  
- `OnSceneTransitionStarted` - Hides player elements during scene transition
- `OnSceneTransitionCompleted` - Restores player elements after scene transition

### **Player Element Management**:
- **During Transition**: Hides player body and reticle for clean visual transitions
- **After Transition**: Restores player body and reticle visibility
- **Position Reset**: Resets player body to local position (0,0,0)

### **State Management**:
- **SetMainCamera()**: Restores normal gameplay state (called by SceneSwitchCleanup)
- **SwitchToTransitionCamera()**: Handles transition state
- **IsInTransition()**: Public method to check transition state

## 🔧 Context Menu Options

Right-click on the SimpleCameraManager component in the inspector:
- **Set Main Camera**: Manually restore main camera state
- **Switch To Transition Camera**: Manually trigger transition
- **Test Transition**: Run a full transition test cycle

## 🚨 Troubleshooting

### **"No camera management component found"**
- Make sure SimpleCameraManager is added to a GameObject in the scene
- Check that the GameObject is active

### **Player elements not hiding during transitions**
- Verify Player Body and Reticle references are assigned
- Check that "Hide Elements During Transition" is enabled
- Enable debug logs to see transition events

### **Events not firing**
- Ensure GameEventsManager.Instance exists in the scene
- Check that GameEvents ScriptableObject is properly configured
- Enable debug logs to see event subscription status

## 📋 Migration Checklist

- [ ] Add SimpleCameraManager component to main camera GameObject
- [ ] Assign Player Body reference
- [ ] Assign Reticle reference  
- [ ] Configure transition settings
- [ ] Test scene transitions
- [ ] Verify console shows no camera-related errors
- [ ] Remove any references to old CinemachineCameraSwitching (if any remain)

## 🎮 Expected Behavior

**Before Transition**:
- Player body visible
- Reticle visible
- Normal gameplay state

**During Transition**:
- Player body hidden (if configured)
- Reticle hidden (if configured)
- Transition effects active

**After Transition**:
- Player body restored and visible
- Reticle restored and visible
- Player facing forward event triggered
- Normal gameplay state resumed

## 💡 Benefits Over Original

- **Simpler**: No complex camera switching or animation dependencies
- **Reliable**: Direct GameObject visibility control
- **Flexible**: Easy to configure and customize
- **Maintainable**: Clear, straightforward code
- **Compatible**: Works with existing event system
- **Fallback Support**: GameManager and SceneSwitchCleanup support both old and new systems

---

**Status**: Ready for implementation - add SimpleCameraManager to your scene and configure as described above.
