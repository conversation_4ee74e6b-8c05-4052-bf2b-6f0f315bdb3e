# May 24

Made adjustment to a script for <PERSON> Head movement in Ouroboros level, could be useful to general timed movement. 

Added Ground collision on main camera, seems pretty good!

Also sped things up, trying to balance some gameplay things better

Do I need a ricochet / shield element?

Made a mistake with this and created an element where you can lay traps basically

<aside>
💡 Idea!

</aside>

Seems cool! Keeping this in mind for now - happens when target cube remain target of bullets, they still swarm around it

<aside>
💡 Idea!

</aside>

Key idea - feels like the bullets are chasing you. Important to have proper balance between speed of movement / speed of bullets to achieve this effect - feels like you’re moments from being hit