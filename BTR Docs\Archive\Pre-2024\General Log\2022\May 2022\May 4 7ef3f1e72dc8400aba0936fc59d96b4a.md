# May 4

File naming for Kore assets

Drums 1-8

Drums 1-16

Drums 1-32

Naming scheme for Perc Koreo Track ID 

Perc

Perc Half

Perc Half Half

These names can carry over to all sections and Kore<PERSON> will automatically pick it up

Trying to update Background Color Changer

Using Unity Events - that seems to work fine!

<aside>
💡 But the colors only switch a couple times and stop - not sure why.

</aside>

Doesnt appear to be Koreographer issue. Tried to fix multiple ways there and nothing changed the behaviour

NEXT UP

Advance section of song when certain criteria met

Every wave? No, should be able to set how many waves it takes before next section

New Script - Change Song Section

How many waves per section? I may want to adjust this

Read in a text file that allows me to adjust number of waves per song section?

Maybe using Json is best!

Working through Game States

Basic version of Music / Wave controller based around JSON descriptions implemented!

Need to flesh out more - Start section also not working

Changing sections - I think sections in song and sections in AdjustSongParameters need to be different, Can increment through the sections listed on the json file, but some can be repeats and in a differnet order

Seems to be working / SORT OF

Need to feel out these transition areas more, add a visual effect for when they’re kicked

Need to bug test bullet issues as well

Need to find current song section and initiate transition effects when a transition occurs 

What are transition effects?

Can we change levels? Do we fly to a new area? Do a camera zoom and move?

BIG COUNT DOWN ON SCREEN maybe?

Note - wave delay will happen every time currently - even if there is no transition!

Need to set Wave Spawner files to match the JSON files I think?

Think about this one some more  - split off waves 

Need to write out the entirety of this system! See where things could be better connected

Start with FMOD → Build Drum loops and transitions here

Try some wave patterns in WaveSpawner 

Write those Patterns in JSON for proper section transitions

Reinstall and troubleshoot amplify animation pack