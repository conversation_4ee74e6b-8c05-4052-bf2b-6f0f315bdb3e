using UnityEngine;
using Stylo.Cadance;
using Stylo.Cadance.FMOD;
using BTR;

/// <summary>
/// Comprehensive setup and diagnostic tool for the Cadance musical timing system.
/// Use this to properly configure Cadance for shooting system integration.
/// </summary>
public class CadanceSystemSetup : MonoBehaviour
{
    [Header("Setup Configuration")]
    [SerializeField] private bool autoSetupOnStart = false;
    [SerializeField] private string shootingEventID = "Beat"; // Default event ID for shooting
    
    [Header("Diagnostic Information")]
    [SerializeField] private bool cadanceExists = false;
    [SerializeField] private bool fmodManagerExists = false;
    [SerializeField] private bool runtimeConfigExists = false;
    [SerializeField] private bool musicControllerExists = false;
    [SerializeField] private string currentEventID = "";
    
    private CrosshairCore crosshairCore;
    
    private void Start()
    {
        if (autoSetupOnStart)
        {
            SetupCadanceSystem();
        }
        
        PerformDiagnostics();
    }
    
    [ContextMenu("Setup Cadance System")]
    public void SetupCadanceSystem()
    {
        Debug.Log("=== CADANCE SYSTEM SETUP STARTING ===");
        
        // Step 1: Ensure Cadance instance exists
        SetupCadanceInstance();
        
        // Step 2: Setup FMOD integration
        SetupFMODIntegration();
        
        // Step 3: Setup event debugger
        SetupEventDebugger();
        
        // Step 4: Configure CrosshairCore event ID
        ConfigureShootingEventID();
        
        Debug.Log("=== CADANCE SYSTEM SETUP COMPLETE ===");
        Debug.Log("Next steps:");
        Debug.Log("1. Add FMOD Event Emitter to scene with music");
        Debug.Log("2. Load a CadanceAsset with timing events");
        Debug.Log("3. Ensure music is playing to generate events");
    }
    
    private void SetupCadanceInstance()
    {
        Debug.Log("[CadanceSetup] Setting up Cadance instance...");
        
        // Get or create Cadance instance
        var cadance = Cadance.Instance;
        if (cadance != null)
        {
            Debug.Log("[CadanceSetup] ✅ Cadance instance created/found");
            cadanceExists = true;
            
            // Ensure it persists across scenes
            if (cadance.transform.parent == null)
            {
                DontDestroyOnLoad(cadance.gameObject);
            }
        }
        else
        {
            Debug.LogError("[CadanceSetup] ❌ Failed to create Cadance instance!");
        }
    }
    
    private void SetupFMODIntegration()
    {
        Debug.Log("[CadanceSetup] Setting up FMOD integration...");
        
        var fmodManager = FMODCadanceManager.Instance;
        if (fmodManager != null)
        {
            Debug.Log("[CadanceSetup] ✅ FMODCadanceManager created/found");
            fmodManagerExists = true;
        }
        else
        {
            Debug.LogError("[CadanceSetup] ❌ Failed to create FMODCadanceManager!");
        }
    }
    
    private void SetupEventDebugger()
    {
        Debug.Log("[CadanceSetup] Setting up event debugger...");
        
        var cadance = Cadance.Instance;
        if (cadance != null)
        {
            var debugger = cadance.GetComponent<CadanceEventDebugger>();
            if (debugger == null)
            {
                debugger = cadance.gameObject.AddComponent<CadanceEventDebugger>();
                Debug.Log("[CadanceSetup] ✅ CadanceEventDebugger added");
            }
            else
            {
                Debug.Log("[CadanceSetup] ✅ CadanceEventDebugger already exists");
            }
        }
    }
    
    private void ConfigureShootingEventID()
    {
        Debug.Log("[CadanceSetup] Configuring shooting event ID...");
        
        crosshairCore = FindFirstObjectByType<CrosshairCore>();
        if (crosshairCore != null)
        {
            // Get current event ID
            var field = typeof(CrosshairCore).GetField("eventIDShooting", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                currentEventID = (string)field.GetValue(crosshairCore);
                Debug.Log($"[CadanceSetup] Current shooting event ID: '{currentEventID}'");
                
                if (string.IsNullOrEmpty(currentEventID))
                {
                    field.SetValue(crosshairCore, shootingEventID);
                    Debug.Log($"[CadanceSetup] ✅ Set shooting event ID to: '{shootingEventID}'");
                }
            }
        }
        else
        {
            Debug.LogWarning("[CadanceSetup] ⚠️ CrosshairCore not found in scene");
        }
    }
    
    [ContextMenu("Perform Diagnostics")]
    public void PerformDiagnostics()
    {
        Debug.Log("=== CADANCE SYSTEM DIAGNOSTICS ===");
        
        // Check Cadance instance
        var cadance = Cadance.Instance;
        cadanceExists = cadance != null;
        Debug.Log($"Cadance Instance: {(cadanceExists ? "✅ EXISTS" : "❌ MISSING")}");
        
        // Check FMOD manager
        var fmodManager = FMODCadanceManager.Instance;
        fmodManagerExists = fmodManager != null;
        Debug.Log($"FMOD Manager: {(fmodManagerExists ? "✅ EXISTS" : "❌ MISSING")}");
        
        // Check runtime config
        var config = CadanceRuntimeConfig.Instance;
        runtimeConfigExists = config != null;
        Debug.Log($"Runtime Config: {(runtimeConfigExists ? "✅ EXISTS" : "❌ MISSING")}");
        
        // Check music controller
        if (cadance != null)
        {
            musicControllerExists = cadance.musicPlaybackController != null;
            Debug.Log($"Music Controller: {(musicControllerExists ? "✅ EXISTS" : "❌ MISSING")}");
        }
        
        // Check CrosshairCore event ID
        crosshairCore = FindFirstObjectByType<CrosshairCore>();
        if (crosshairCore != null)
        {
            var field = typeof(CrosshairCore).GetField("eventIDShooting", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                currentEventID = (string)field.GetValue(crosshairCore);
                Debug.Log($"Shooting Event ID: '{currentEventID}'");
            }
        }
        
        // Overall status
        bool systemReady = cadanceExists && fmodManagerExists && runtimeConfigExists;
        Debug.Log($"System Status: {(systemReady ? "🟢 READY" : "🔴 NEEDS SETUP")}");
        
        if (!systemReady)
        {
            Debug.Log("Run 'Setup Cadance System' to fix issues");
        }
        
        if (!musicControllerExists)
        {
            Debug.LogWarning("⚠️ No music controller - add FMOD Event Emitter with music to generate timing events");
        }
    }
    
    [ContextMenu("Test Event Registration")]
    public void TestEventRegistration()
    {
        var cadance = Cadance.Instance;
        if (cadance == null)
        {
            Debug.LogError("Cannot test - Cadance instance not found");
            return;
        }
        
        string testEventID = string.IsNullOrEmpty(currentEventID) ? shootingEventID : currentEventID;
        
        Debug.Log($"Testing event registration for: '{testEventID}'");
        
        // Register for test event
        cadance.RegisterForEvents(testEventID, OnTestEvent);
        Debug.Log($"✅ Registered for event: '{testEventID}'");
        
        // Unregister after 5 seconds
        Invoke(nameof(UnregisterTestEvent), 5f);
    }
    
    private void OnTestEvent(CadanceEvent evt)
    {
        Debug.Log($"🎵 TEST EVENT RECEIVED: {evt.EventID} at sample {evt.StartSample}");
    }
    
    private void UnregisterTestEvent()
    {
        var cadance = Cadance.Instance;
        if (cadance != null)
        {
            string testEventID = string.IsNullOrEmpty(currentEventID) ? shootingEventID : currentEventID;
            cadance.UnregisterForEvents(testEventID, OnTestEvent);
            Debug.Log($"Unregistered from test event: '{testEventID}'");
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 200, 400, 300));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔧 CADANCE SYSTEM SETUP", GUI.skin.label);
        
        GUILayout.Label($"Cadance: {(cadanceExists ? "✅" : "❌")}");
        GUILayout.Label($"FMOD Manager: {(fmodManagerExists ? "✅" : "❌")}");
        GUILayout.Label($"Runtime Config: {(runtimeConfigExists ? "✅" : "❌")}");
        GUILayout.Label($"Music Controller: {(musicControllerExists ? "✅" : "❌")}");
        GUILayout.Label($"Event ID: '{currentEventID}'");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🛠️ SETUP CADANCE SYSTEM"))
        {
            SetupCadanceSystem();
        }
        
        if (GUILayout.Button("🔍 RUN DIAGNOSTICS"))
        {
            PerformDiagnostics();
        }
        
        if (GUILayout.Button("🧪 TEST EVENT REGISTRATION"))
        {
            TestEventRegistration();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
