# April 7

Working on boss ideas of Twin Snakes / Time

Also optimization

**2. Extending ProjectileStateBased for Specific Enemy Behaviors**

If EnemyTwinSnakeBoss requires specific projectile behaviors not covered by the current states in ProjectileStateBased, consider adding new states. For example, if the twin snake boss shoots a special type of projectile that splits into two after a certain time, you might need to create a new ProjectileState subclass to handle this behavior.

### 4. Handling Projectile Pool Types

If EnemyTwinSnakeBoss uses a unique type of projectile not shared with other enemies, consider adding a new ProjectilePoolType enum value and adjusting the pooling logic in ProjectileManager to accommodate this. This would involve creating and managing a new pool specifically for these projectiles.

`ProjectileStateBased.cs`

`ReplyApplyCopy`

`public enum ProjectilePoolType`

`{`

`StaticEnemy,`

`EnemyBasicSetup,`

`TwinSnakeBoss *// New pool type for TwinSnakeBoss projectiles*`

`}`

### Conclusion

The current implementation of ProjectileManager and ProjectileStateBased appears flexible enough to support the integration with EnemyTwinSnakeBoss without immediate changes. However, based on specific gameplay requirements or desired projectile behaviors, the optional adjustments outlined above can help ensure a robust and flexible projectile system that accommodates the unique aspects of the EnemyTwinSnakeBoss.

```csharp
 //This doesnt work! Error with assinging a new clock at runtime. Need to look into Chronos.
           // newTimeline.clock = clock;
```