# Sept 19

Fixing vfx Graph heat distortion issues by importing from older project

- In Shader Graphs / Datamosh folder
- Good for void area

QTE idea - snake enemies have as many commands as they have length

Looking into performance hit that occurs when player shoots. Noticed on steam deck, something like a 15 fps drop. 

- ProjectileManager optimizations recommended, testing these

Adjsuting aim assist, not there yet but getting closer

Changing Default controls for new asiming system

Broke ability to damage mid boss snakes, need to fix. not sure if projectiles are not shooting properly or its a snake not taking damage issue

Adjustments to try and make snake not upside down for infinite generation area - not working. need to rethink approach here

Slowing projectiles a little before they hit player, for better player experience reacting to them 

Need a different sound for when yo uuse the shield and a projectiel doesnt boucne fof

make this more obviouis! bigger visual cue too