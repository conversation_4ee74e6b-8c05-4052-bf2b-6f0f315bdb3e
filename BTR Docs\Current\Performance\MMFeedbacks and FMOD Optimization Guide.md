# MMFeedbacks and FMOD Optimization Guide

## Overview

Your audit shows **80+ RuntimeInitializeOnLoadMethod calls from MMFeedbacks** and **10+ FMOD platform initializers**. This guide provides specific steps to reduce their impact while keeping the functionality you need.

## MMFeedbacks Optimization (Expected Savings: 5-8 seconds)

### Problem
MMFeedbacks initializes modules for ALL render pipelines (Built-in, URP, HDRP, PostProcessing) even if you only use one.

### Solution 1: Use the Performance Optimizer Tool
1. Go to `BTR > Performance > Performance Optimizer`
2. Click "Disable Unused MMFeedbacks Modules"
3. This automatically adds define symbols to disable unused render pipeline modules

### Solution 2: Manual Define Symbols
Add these to `Project Settings > Player > Scripting Define Symbols`:

**For URP Projects (your setup):**
```
MM_HDRP_DISABLED
MM_POSTPROCESSING_DISABLED
```

**Additional Optional Defines:**
```
MM_CINEMACHINE_DISABLED (if not using Cinemachine with MMFeedbacks)
MM_TEXTMESHPRO_DISABLED (if not using TextMeshPro with MMF<PERSON>backs)
```

### Solution 3: Remove Unused MMFeedbacks Folders
If you're not using certain render pipelines, you can delete these folders:
- `Assets/Feel/MMFeedbacks/MMFeedbacksForThirdParty/HDRP/` (if not using HDRP)
- `Assets/Feel/MMFeedbacks/MMFeedbacksForThirdParty/PostProcessing/` (if not using Post Processing Stack v2)

## FMOD Platform Optimization (Expected Savings: 1-2 seconds)

### Problem
FMOD initializes platform-specific code for ALL platforms (iOS, Android, WebGL, etc.) even if you only target PC.

### Solution 1: Use the Performance Optimizer Tool
1. Go to `BTR > Performance > Performance Optimizer`
2. Click "Disable Unused FMOD Platforms"
3. This renames unused platform folders to `.disabled`

### Solution 2: Manual FMOD Settings
1. Go to `FMOD > Edit Settings` in Unity
2. Under "Platform Specific" settings:
   - **Keep enabled:** Windows, Mac, Linux (desktop platforms)
   - **Disable:** Android, iOS, WebGL, tvOS, VisionOS (if not targeting these)

### Solution 3: Manual Folder Disabling
Rename these folders to disable unused platforms:
```
Assets/Plugins/FMOD/platforms/android → android.disabled
Assets/Plugins/FMOD/platforms/ios → ios.disabled
Assets/Plugins/FMOD/platforms/html5 → html5.disabled
Assets/Plugins/FMOD/platforms/tvos → tvos.disabled
Assets/Plugins/FMOD/platforms/visionos → visionos.disabled
```

## Additional High-Impact Optimizations

### 1. Remove Completely Unused Packages
Based on your audit, consider removing these if not used:

**High Impact (3+ InitializeOnLoad each):**
- **Amplify Shader Pack** (3 InitializeOnLoad calls)
- **Chroma** (4 InitializeOnLoad calls)
- **Advanced Dissolve** (if not using dissolve effects)

**Medium Impact (1-2 InitializeOnLoad each):**
- **RealToon** (welcome screen initialization)
- **MeshFusionPro** (if not using mesh fusion)
- **Raymarcher** (if not using raymarching)

### 2. Optimize Curvy Splines
Add this define symbol to disable network features:
```
CURVY_DISABLE_NETWORK
```

### 3. Configure Feel/NiceVibrations
If not using haptic feedback, you can disable:
```
MOREMOUNTAINS_NICEVIBRATIONS_DISABLED
```

## Step-by-Step Implementation

### Phase 1: Quick Wins (5 minutes)
1. **Use the Performance Optimizer tool:**
   - `BTR > Performance > Performance Optimizer`
   - Click "Disable Unused MMFeedbacks Modules"
   - Click "Disable Unused FMOD Platforms"

### Phase 2: Package Removal (10 minutes)
1. **Remove unused packages:**
   - Check if you use Amplify Shader Pack, Chroma, Advanced Dissolve
   - Remove via Package Manager or delete folders
   - Test your project after each removal

### Phase 3: Fine-tuning (5 minutes)
1. **Add additional define symbols if needed:**
   - `MM_CINEMACHINE_DISABLED` (if not using Cinemachine with MMFeedbacks)
   - `CURVY_DISABLE_NETWORK` (to optimize Curvy Splines)

## Expected Results

**Before Optimization:**
- MMFeedbacks: ~80 RuntimeInitializeOnLoadMethod calls
- FMOD: ~10 platform initializers
- Total startup impact: ~8-12 seconds

**After Optimization:**
- MMFeedbacks: ~20-30 RuntimeInitializeOnLoadMethod calls (60-75% reduction)
- FMOD: ~3-4 platform initializers (70% reduction)
- Total startup impact: ~2-4 seconds (75% improvement)

## Verification

After applying optimizations:
1. **Run the InitializeOnLoad auditor again** to see the reduction
2. **Test play mode entry time** - should be significantly faster
3. **Test your MMFeedbacks** to ensure they still work correctly
4. **Test your audio** to ensure FMOD still works correctly

## Troubleshooting

### If MMFeedbacks Stop Working
- Remove the define symbols you added
- Check which specific feedbacks you're using and only disable unused render pipelines

### If FMOD Audio Breaks
- Re-enable the platform folders you disabled
- Check FMOD settings to ensure your target platform is enabled

### If You Need Disabled Features Later
- Remove the corresponding define symbols
- Re-enable platform folders by removing the `.disabled` extension

## Maintenance

- **When updating MMFeedbacks:** Re-apply define symbols after updates
- **When adding new platforms:** Re-enable corresponding FMOD platform folders
- **Regular audits:** Run the InitializeOnLoad auditor monthly to catch new bottlenecks

This optimization should reduce your startup time from 20-25 seconds to 8-12 seconds, getting you much closer to your target of 2-5 seconds.
