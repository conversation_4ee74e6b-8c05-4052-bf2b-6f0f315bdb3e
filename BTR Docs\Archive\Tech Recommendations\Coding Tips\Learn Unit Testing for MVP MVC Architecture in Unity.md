---
title: Unit Testing for MVP/MVC Architecture in Unity
tags: [Unity, Testing, MVP, MVC, UnitTesting]
date: 2025-01-20
---

# [[Unit Testing for MVP/MVC Architecture in Unity]]

## [[Overview]]
This tutorial covers unit testing in Unity using the MVP (Model-View-Presenter) and MVC (Model-View-Controller) architectures. These patterns make testing easier by centralizing logic in pure C# classes.

## [[Setup]]

### [[Test Assemblies]]
Create separate assemblies for editor and runtime tests:
```json
// Editor Tests Assembly Definition
{
    "name": "Editor.Tests",
    "references": [
        "Unity.TestFramework",
        "Substitute",
        "Architecture"
    ],
    "includePlatforms": ["Editor"]
}

// Runtime Tests Assembly Definition  
{
    "name": "Runtime.Tests",
    "references": [
        "Unity.TestFramework",
        "Substitute",
        "Architecture"
    ],
    "includePlatforms": ["Any"]
}
```

### [[Test Framework]]
Install the Unity Test Framework and NSubstitute for mocking:
```csharp
// Package Manager
com.unity.test-framework
com.unity.nsubstitute
```

## [[Core Testing Concepts]]

### [[Assertions]]
```csharp
// Basic assertions
Assert.That(username, Does.StartWith("U"));
Assert.That(username, Does.EndWith("3"));

// Collection assertions
var numbers = new List<int> { 1, 2, 3, 4, 5 };
Assert.That(numbers, Does.Contain(3));
Assert.That(numbers, Is.All.Positive);
Assert.That(numbers, Has.Exactly(2).LessThan(3));
```

### [[Test Structure]]
```csharp
[Test]
public void TestExample()
{
    // Arrange
    var controller = new CoinController();
    
    // Act
    controller.CollectCoins(5);
    
    // Assert
    Assert.That(controller.TotalCoins, Is.EqualTo(5));
}
```

## [[Testing MVC Architecture]]

### [[Coin Collection System]]
```csharp
public interface ICoinController
{
    void CollectCoins(int amount);
    void UpdateView();
    void Save();
    void Load();
}

public class CoinController : ICoinController
{
    private readonly ICoinModel model;
    private readonly ICoinView view;
    private readonly ICoinService service;

    public CoinController(ICoinModel model, ICoinView view, ICoinService service)
    {
        this.model = model ?? throw new ArgumentNullException(nameof(model));
        this.view = view ?? throw new ArgumentNullException(nameof(view));
        this.service = service ?? throw new ArgumentNullException(nameof(service));
    }

    public void CollectCoins(int amount) => model.AddCoins(amount);
    public void UpdateView() => view.UpdateCoinDisplay(model.Coins);
    public void Save() => service.Save(model);
    public void Load() => model.SetCoins(service.Load());
}
```

### [[Mocking Dependencies]]
```csharp
[TestFixture]
public class CoinControllerTests
{
    private ICoinModel model;
    private ICoinView view;
    private ICoinService service;
    private CoinController controller;

    [SetUp]
    public void Setup()
    {
        model = Substitute.For<ICoinModel>();
        view = Substitute.For<ICoinView>();
        service = Substitute.For<ICoinService>();
        
        controller = new CoinController(model, view, service);
    }

    [Test]
    public void CollectCoins_UpdatesModel()
    {
        // Arrange
        model.Coins.Returns(new Observable<int>(0));
        
        // Act
        controller.CollectCoins(5);
        
        // Assert
        model.Received().AddCoins(5);
    }
}
```

## [[Play Mode Testing]]

### [[Scene Loading]]
```csharp
[TestFixture]
public class PlayModeTests
{
    [UnityTest]
    public IEnumerator Application_IsPlaying()
    {
        // Arrange & Act
        yield return null;
        
        // Assert
        Assert.That(Application.isPlaying, Is.True);
    }

    [UnityTest]
    [LoadScene("UnderwaterScene")]
    public IEnumerator Scene_HasRequiredObjects()
    {
        // Arrange
        yield return null;
        
        // Act
        var player = GameObject.Find("Player");
        
        // Assert
        Assert.That(player, Is.Not.Null);
    }
}

public class LoadSceneAttribute : NUnitAttribute, IOuterUnityTestAction
{
    private readonly string scenePath;

    public LoadSceneAttribute(string scenePath)
    {
        this.scenePath = scenePath;
    }

    public IEnumerator BeforeTest(ITest test)
    {
        Debug.Assert(scenePath.EndsWith(".unity"));
        yield return EditorSceneManager.LoadSceneInPlayMode(scenePath, new LoadSceneParameters(LoadSceneMode.Single));
    }

    public IEnumerator AfterTest(ITest test) => null;
}
```

## [[Best Practices]]
1. **[[Test Logic, Not Implementation]]**: Focus on testing business logic rather than implementation details
2. **[[Use Mocks Wisely]]**: Mock dependencies to isolate the unit under test
3. **[[Follow AAA Pattern]]**: Arrange, Act, Assert for clear test structure
4. **[[Parameterize Tests]]**: Use `[TestCase]` for testing multiple scenarios
5. **[[Test Edge Cases]]**: Include tests for null values, boundary conditions, and error cases
6. **[[Keep Tests Fast]]**: Avoid unnecessary setup and long-running operations
7. **[[Maintain Test Coverage]]**: Aim for high coverage of critical paths

## [[Additional Resources]]
- [[Unity Documentation: Test Framework]]
- [[MVP/MVC Architecture Patterns]]
- [[Unit Testing Best Practices]]
- [Complete Test Examples on GitHub](https://github.com/Unity-Technologies/UnitTestingExamples)
- [Testing Patterns in Game Development](https://example.com/testing-patterns)