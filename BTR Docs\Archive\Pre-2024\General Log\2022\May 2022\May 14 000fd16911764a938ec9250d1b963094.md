# May 14

Buidlings have no issue in build! It’s just in editor play mode. 

Need to check profile during build mode playing

Profiler auto connects to build - does so by IP. Just open it in Unity Editor

Enemies getting stuck - is this due to colliding with buildings?

Whats the best LOD group Fade Mode?

Only using LOD group 2 and 3 - can I delete the others?

Maybe use Cross Fade instead of Speed Tree due to only 2 LOD groups?

May need to adjust this approach

Some info

[Level of Detail in Unity Using LODGroups, Dithering, and Crossfade! ✔️ 2020.3 | Game Dev Tutorial](https://www.youtube.com/watch?v=-mE4qreuqJY)

Can setup LOD levels in Blender when making things and export that properly to Unity

Verify Crossfade is working - may not be in URP. The video has instructions on how to deal with this

Doesn’t seem to work to me! May need work around

Load a screen with controls on start? Explain gameplay then throw you in?

Intro Setup will do this

Immediately Pauses game to show controls and explain how things work 

Basic screens setup - need to show actions and write out controls

Resource for changing scenes - how quick can this happen?

[Unity Scenes 2: Keeping Objects When Changing Scenes (DontDestroyOnLoad)](https://www.youtube.com/watch?v=vGxNLVXYIto)

Any way to give a red blue glasses shifted lines / perspective view ?

Render objects on one layer as blue and slightly off? Others as red?

Typography ideas

[https://fontsinuse.com/uses/28760/neon-genesis-evangelion](https://fontsinuse.com/uses/28760/neon-genesis-evangelion)