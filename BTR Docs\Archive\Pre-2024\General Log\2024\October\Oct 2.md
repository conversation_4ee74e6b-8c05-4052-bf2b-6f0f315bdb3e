# Oct. 2

Rough overview of things (top of head)

- Projectiles working for shooting enemy targets and shooting straight
- FMOD mostly reorganized, cleaner approach to how this is setup - less confusing!
- Projectiles shooting in Interior scene (ouroboros)

ToDo

- Get Enemy Explodes damaging the Player
    - After that
        - Refine spawn points
        - Refine flashing / sfx distances
        - Refine timeline for how long it takes to reach the player
        - Refine placement of static shooters

- Get the snake like dodeca enemies working better or ditch them
    - QTE for these?

- Infinite Snake
    - Refine shooting pattern - use multiple?
    - 

Idea

- Reticle speed slows down for easier aiming
- This comes from <PERSON> pachi slow down with big laser vs regular shots and you move faster
- Thinks of the reticle as a shmup ship? In this context, what can be done differently?

Use a lock on like layer section with wild missile curvy lasers as fx? Have some projectile fx packs that would work well for this I think 

See also zero ranger / crimson clover

Maybe implement the power up after shots mechanic that kill knight uses? Think about it or similar things