# Oct. 24

Looking at optimization for fun 

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image.png)

Highlighter causing spike on enemy spawning

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%201.png)

Fmod Projectiles issue - thought I resolved this?

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%202.png)

Unsure what this is

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%203.png)

Some mesh levels have this issue, some don’t, investigate what it is

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%204.png)

Why AI waiting take so much time?

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%205.png)

Infinite Snake - optimize this if possible

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%206.png)

Not sure what’s happening when particle systems have this issue

Issues largely CPU bound - for higher frame rates GPU bound things to look into, but not the main concern

Can I use Adaptive Probe Volumes? Seems I can turn it on

Need to set layers that cast shadows for optimization

How to reduce setpass calls when lots of geometry? Optimize Batching?

Frame Debugger will show why things cannot be batched - analyze

More materials = more setpass calls. Reduce materials if possible

Simple Lit best performance Shader?

Disable MipMaps? What are MipMaps? Don’t know settings related to this

Need to learn how timeline view works

Attempting to batch timeline inits due to performance spike on static shooters of projectiles 

**Testing**

Projectiles having issues wehn colliding with Non enemy objects. This mostly occurs when player is straight shooting them. Some changes made to address this, but analyze further and test - this can cause projectile pool to come to a halt, and can be the reason why shooting stops worknig for everything. 

IMPORTANT NOTE - AIMovementSystem has been modified to address the job issues 

[https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005](https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005)

If updating A*, need to do this again 

Frame Debugger help, fidnign ways to batch things that previously werren’t batching. Snake Static Shooters are different now, if look is bad adjust and test, but has reduced bathcing. find more things like this 

Increased fmod channel count from 32 to 64 to see if it effects build sound dropout

increased fmod channerl count to 128

Also made an Audio Manager to help deal with releasing instances

Reference this for playing sounds

Should clean up instances so channels are not filled out

**Oct. 25th**

Lots of optimizations in materials

Need to repalce particle effects and shjaders with more efficient things in general

Look at Enemy Shooting FX

Particle Explosion

Particle Hit Explosion

Anything that there’s a lot of