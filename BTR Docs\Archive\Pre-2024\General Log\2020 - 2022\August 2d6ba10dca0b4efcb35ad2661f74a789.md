# August

**August 1**

Played with shaders and possible new unity assets to use

Looking at clamping Z movement of Reticle/Shooter

Reference original Starfox project for Reticle movement

Locked player right now to figure out reticle movement

Issues with clamping Reticle position

Removed clamping - still some jittery on left/right positions. Front/Back are fine 

Find way to move bullets in opposite trajectory of their travelling direction

- may need to change based on viewpoint?

Clamping breaks at high speeds!!

Need a different way to clamp- see what's wrong here

FIXEDUPDATE RECTIFIED ALL PROBLEMS??? OK!

Seems to fix all physics based problems wow

**August 3**

TODO:

Adjust bullets so when switching perspectives, they fly opposite of the aim reticle

IDEA:

Allow reticle to move bullets around?

Aim Assist for firing bullets? This could be way too complicated imo

- Maybe depends on enemy?

Did an hour of Freya's Shader course

Projectiles firing in correct direction now!

Create enemies that get hit by projectiles Squares with holes in them? 

Draw out scenarios

Sensor toolkit added - watch tutorial videos

[https://www.youtube.com/watch?v=hVBpwwjqM00](https://www.youtube.com/watch?v=hVBpwwjqM00)

Refer to action blocks Figma file

Try this asset for exploration? [https://assetstore.unity.com/packages/templates/packs/space-journey-42259](https://assetstore.unity.com/packages/templates/packs/space-journey-42259) - downloaded 

Refer to Reboot for world wide web ideas?

Think of Lanthimos and false realities - [https://www.youtube.com/watch?v=2pPGeVkBYrU](https://www.youtube.com/watch?v=2pPGeVkBYrU)

**August 4**

Thinking Cow Catcher will no longer work, actually causes errors

Maybe if bullets leave a proximity they destroy themselves? LOOK INTO THIS

Trying Sensor toolkit on shooting enemies

Would trigger enter etc with wide range be better? Thinking line of site features may make Sensor Toolkit the better option

Sensor Toolkit working! Line of sight working!

Created Blockable Layer for things that can block line of sight of enemies

Line of sight may be iffy, be aware!

IDEA- CowCatcher causes bullets to reduce in speed if approaching from behind? collision causes slow down

Greybox Enemy Placement showing promise! Experiment with designs

**August 5**

Adding hittable targets - destroyed on impact

Looking at radar - adding enemies and hittable targets - need to think of design philosophy here

THings need to happen for a reason 

Some combination of xall response and more traditional gameplay as seen in orta or zwei -  think how to meld thumper and these - fast paced but free enemies to grab or hit

**August 6**

Hard long day at work. No work done really. 

**August 7**

Lots of experimenting with fog - not much luck! Ethereal URP is difficult

Think I will stick with it though. Like it more then not having it

**August 9**

Found this - [https://www.resurgamstudios.com/cgf-manual](https://www.resurgamstudios.com/cgf-manual)

Seems like a cool way to have aim assist for bullets

**August 10**

Bullet speeds and level design

Trying to improve bullet orbit issues - maybe it worked?

Looking at level design structures - lots of trial and error

Something broken in rotating bullets now

When they parent horizontal platform - is something going wrong?

Player model need to be tagger Player Aim Target now or fix this

Enable Player Visibility when not use Sensor for control of enemies

Rotating seems to be an issue with speed? Working in Player Movement Testing scene

May need to reduce overall speeds on levels for some functionality?

Use combo of L/R and Glitching to position things. Likely need to refine glithcing

Objects still moving when locked on... why? Shouldnt it stop? Look into this

Player Movement Testing

**August 11**

Looking into objects not locking on and staying still 

- rb.constraints = RigidbodyConstraints.FreezeAll;
- rb.constraints = RigidbodyConstraints.None;

IDEA: Move # of Locks into aiming reticle

IDEA: after locks, release of projectiles aims wherever cursor is pointing

- pass reticle/rayspawn aiming to launchback function?

Need to shoot in general direction of rayspawn, hitting target isn't necessary

Seems to be working! Need to find a way to aim them at general direction of where Reticle is looking as well, rather then just forward

Maybe add Layer to things that can be hit? Maybe end of range OR target option

IDEA: Change shape of reticle depending on mode - lock vs shoot

**August 12**

Trying to pass a target (RaycastTarget) to Launchback command in order to aim the bullets at a specific point (the direction of the reticle)

setup tempV target to test homing of bullets

need to go over homing again

Launchback is creating sphere at target location - use these as lock on points for homing bullets?    

Successfully creating sphere at target location and projectiles shoot towards them!

Switch this to a prefab that's destroyed on impact, maybe has a collision radius as well?

**August 13**

Using targetCube prefabs for targets of bullets now

Using Target Solo for collision detection script on these. Mostly working

Likely need a self destruct on these aiming cubes incase no collision

IDEA: only one cube as target instead of multiple, scaled according to number of bullets selected? EXPLORE

OPS Fixes

Removed destroy from TargetSolo - bullet should recycle itself

Fixing bullet's target - reassigning from targetcube to player at death

Fixing homing(true/false) of bullet on recycle

Fixed launching to false in Death phase, seems to fix issues with bullets not recycled and fired properly

**August 14**

Cleaning up office!

Setup Bugs sections for Notion

Doing some bug fixing as well

Need to Look over some technical documentation on assets used  - 

Koreogrpaher and Object Particle Spawner

- barely did this, but need to return to it!

**August 16**

[https://www.youtube.com/watch?v=CTBor4rhnQs&t=1s](https://www.youtube.com/watch?v=CTBor4rhnQs&t=1s)

Thinking about Level Design - Radically Nonlinear Level Design

Discuss Doom 2016's levels. Combo of linear areas with combat arenas

Goals were constant movement and improvisation of navigation

Look up this Doom 2016 talk - push forward combat?

MDA framework for game analysis mentioned

What about Linear level Design?

Watched this on visual novels as well

[https://www.youtube.com/watch?v=vrxz3s0L8F8](https://www.youtube.com/watch?v=vrxz3s0L8F8)

**August 18**

I am sick, have been for a couple days probably. Not fun!

**August 21**

First day feeling mostly better

Boomerang X Unity Dev Spotlight - [https://www.youtube.com/watch?v=L7lf2VnTC74](https://www.youtube.com/watch?v=L7lf2VnTC74)

Cool shared effect shown, and ideas on tutorials

Look at Shield Effect again. Also Vertex Animations! 0 animations moved from cpu to gpu - vertex

good for porting to consoles and such apparently - how to do this?

Collider view shown in video? What is this?

Need to learn how modelling / shaders work. What are UV Maps?

They use a Mesh Combiner - then triangle eraser - custom tool for reduction for switch

They have waves and setup the waves per level - worth revisiting!

Sliders for changing object color for accessiblity

![Untitled](August%202d6ba10dca0b4efcb35ad2661f74a789/Untitled.png)

How is color applied? slider controls global shader variable

Discussion on community management - Discord channel

Early thing - every day - discord acronyms channel - D&D-like prompt posted with acronym ### random generated - funny and interesting community things listed

Different themes every month as well

Weekly silly channel as well

Looking up decimation tools

Zbrush - installed, need to try

Trying to make a Boss Test level - Boss Test 1

Spin in circle, being shot at

Found error with bullet direction when not homing, doesn't just go relative forward

Partially fixed, but unsure why in some direction z value is flipped. rotating bullet shooter / origin point to compensate

Homing bullets are weird! Not sure what's going on! Lots of weirdness

Need to standardize on bullet types

**August 22**

Editing Target Solo so that it doesn't kill itself

Integrating Ultimate Spawner + Wave component in Boss Test 1

Thinking about Boomerang X here

Fixed  weird z behaviour on Standard Bullets by flipping x and y

Removed box collider on Target Cube

Converted Bullet Death to Coroutine, to see if it can be properly detected by TargetSolo - Does Not help

Issues around destroying enemies still present

Maybe something like (if bullet stops moving, recycle it?)

TargetSolo is disabled yet im still able to destroy prisms - why???  

**August 24**

Prisms still jiggling when destroyed

Realized that turning off a script DOES NOT disable the behaviour here (Target Solo caused animation death)

IDEA: TargetCubes are sticky, need X number on hitting/attached an enemy to kill them

Enemy has a counter for how many are currently touching

Certain can accept X numebr of noise, large amount overwhelms them

IDEA: One enemies type does not affect itself - need to shoot it at another enemy type

Enemies can recognize their own type of data, this makes sense. So we need to mix in ways it does not expect

Conceptually - do i really want this?

What if you just dont know? different bullet types but not sure what each one does necessarily?

Using Triggers to keep tally of aim objects added - need to add delay to destruction (maybe add it on the ontrigger exit of the last cube? so that bullet has hit it and its destroyed)

This is not entirely working and making things more complicated - scrap it? What does it add to the game?

Thinking about game mentioned on The Blood Zone - roaming around abandoned MMO 

Think in this mindframe???

**August 25**

Minor class cleanup (Crosshair)

Adding ReWired - Finish video! Integrate Movement  

**August 26**

Morning - Finished ReWire integration for player

More touch ups to do like proper mouse integration - map to touch controls, etc

**August 27**

**B**Building glitch animation scripts with unity

Joost Modifier - can do music timed or rnadom glitching on x,y,z like Joost Eggermnot

Building this out to have lerping - need to look into how to properply do this

DOTween??

**Weekend Break**

It was nice!

**August 30**

Trying to time the animations, difficulty defining the loop and timing. 

Keep failing, this is hard to do! Crashing system with current scripts. Need to look up animation scipts

[https://www.reddit.com/r/Unity3D/comments/feceog/dotween_float/](https://www.reddit.com/r/Unity3D/comments/feceog/dotween_float/)

Tween a float to get the motion needed?

or smooth stepping 

[https://www.youtube.com/watch?v=iWc7hKvWA5U](https://www.youtube.com/watch?v=iWc7hKvWA5U)

August 31

New implementation that seems to work

Able to adjust a wait duration

Extending to RandomGlitchSmoothAnim 

This would smoothly transition between wait durations (glitch duration)

Look over this and run

Still hitting issues! Not the right math

Hard coded a constant increase! 

Cool effects sort of! Lots to play with