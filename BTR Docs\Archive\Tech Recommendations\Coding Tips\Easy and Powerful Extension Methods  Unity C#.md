---
title: Easy and Powerful Extension Methods in Unity C#
tags: [Unity, C#, ExtensionMethods, CodeQuality]
date: 2025-01-20
---

# [[Easy and Powerful Extension Methods in Unity C#]]

## [[Introduction]]
[[Extension Methods]] in C# are a powerful way to keep code clean and readable. This tutorial demonstrates how to create and use extension methods in Unity.

## [[Basic Example: Vector3 Extension]]

```csharp
// Basic implementation
[[Vector3]] newPosition = new Vector3(
    transform.position.x,
    transform.position.y + 1,
    transform.position.z
);

// Less verbose version
transform.position += Vector3.up;
```

## [[Creating Extension Methods]]

Extension methods must be static methods within a static class. The first parameter is preceded by the `this` keyword:

```csharp
public static class Vector3Extensions
{
    public static Vector3 With(this Vector3 original,
        float? x = null, float? y = null, float? z = null)
    {
        return new Vector3(
            x ?? original.x,
            y ?? original.y,
            z ?? original.z
        );
    }
}
```

## [[Usage Example]]

```csharp
// Using the With extension method
transform.position = transform.position.With(y: 1);
```

## [[Adding Components Safely]]

A common extension method for [[GameObjects]] is `GetOrAddComponent`:

```csharp
public static T GetOrAddComponent<T>(this GameObject gameObject) where T : [[Component]]
{
    T component = gameObject.GetComponent<T>();
    if (!component)
        component = gameObject.AddComponent<T>();
    return component;
}
```

## [[Transform Extensions]]

Working with transforms can be made easier with extension methods:

```csharp
public static void DestroyChildren(this [[Transform]] transform)
{
    for (int i = transform.childCount - 1; i >= 0; i--)
    {
        Object.Destroy(transform.GetChild(i).gameObject);
    }
}

// GameObject version
public static void DestroyChildren(this GameObject gameObject)
{
    gameObject.transform.DestroyChildren();
}
```

## [[IEnumerable Extensions]]

Transforms implement IEnumerable, allowing for [[LINQ]] operations:

```csharp
public static IEnumerable<Transform> GetChildren(this Transform parent)
{
    foreach (Transform child in parent)
    {
        yield return child;
    }
}

// Usage
var activeChildren = parent.GetChildren().Where(t => t.gameObject.activeSelf);
```

## [[Conclusion]]

Extension methods can significantly improve code readability and maintainability. Some key points:
- Keep extension methods for a type in their own static class
- Use nullable parameters for flexible method signatures
- Create both [[GameObject]] and [[Transform]] versions of transform extensions
- Consider performance implications when working with [[IEnumerable]]

[View all extension methods in the repository](https://github.com/CodeMonkeyUnity/UnityExtensionMethods)