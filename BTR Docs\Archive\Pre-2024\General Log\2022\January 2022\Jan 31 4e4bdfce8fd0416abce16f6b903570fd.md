# Jan 31

Did a roundup of items done in December / January

Posted highlights of that to Slack channel

Adjusting Lifetime of bullets now due to issues with them staying alive forever

IMP:  Consider which constraints need to be set in the bool values of Projectile vs the method calls

Pause is working again! Due to Time Koreo stuff I expect

Error popping up here!

![Untitled](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled.png)

Maybe moving this locked config doesnt work, changed it back to Target phase

Now this is the issue

![Untitled](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled%201.png)

Something about this section is breaking - maybe related to lock on system working badly

Triggering new sounds

Need an event system based on dead enemies to trigger this? Wave Spawner not working or is unrealiable

Circle Enemy where bullets fly out of center?? cool particle effect as well