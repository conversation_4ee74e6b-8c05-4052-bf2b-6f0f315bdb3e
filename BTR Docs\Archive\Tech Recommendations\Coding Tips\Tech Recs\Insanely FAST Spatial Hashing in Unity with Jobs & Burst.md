---
title: Insanely FAST Spatial Hashing in Unity with <PERSON>s & Burst
tags: [<PERSON>, Performance, Jobs, Burst, SpatialHashing]
date: 2025-01-20
---

# [[Insanely FAST Spatial Hashing in Unity with Jobs & Burst]]

## [[Overview]]
Spatial hashing is a technique for efficiently finding objects in 3D space by partitioning space into a grid of discrete cells. This allows fast lookup and querying of objects based on their positions by mapping them to specific grid cells using a hash function.

## [[Implementation]]

### [[Core Components]]

#### Particle Struct
```csharp
public struct Particle
{
    public Vector3 position;
    public Vector3 velocity;
    public float radius;
}
```

#### Hash Calculation
```csharp
public static int CalculateHash(int x, int y, int z)
{
    unchecked
    {
        int hash = x * 73856093;
        hash ^= y * 19349663;
        hash ^= z * 83492791;
        return hash;
    }
}
```

#### Hash and Index Struct
```csharp
public struct HashAndIndex : IComparable<HashAndIndex>
{
    public int hash;
    public int index;
    
    public int CompareTo(HashAndIndex other)
    {
        return hash.CompareTo(other.hash);
    }
}
```

### [[Job System Implementation]]

#### Update Particles Job
```csharp
[BurstCompile]
struct UpdateParticlesJob : IJobParallelFor
{
    public NativeArray<Particle> particles;
    public Vector3 boundsMin;
    public Vector3 boundsMax;
    public float deltaTime;
    
    public void Execute(int index)
    {
        Particle particle = particles[index];
        particle.position += particle.velocity * deltaTime;
        
        // Handle bounds collision
        if (particle.position.x < boundsMin.x || particle.position.x > boundsMax.x)
            particle.velocity.x *= -1;
            
        particles[index] = particle;
    }
}
```

#### Query Job
```csharp
[BurstCompile]
struct QueryJob : IJob
{
    [ReadOnly] public NativeArray<Particle> particles;
    [ReadOnly] public NativeArray<HashAndIndex> hashAndIndices;
    public Vector3 queryPosition;
    public float queryRadius;
    public float cellSize;
    public NativeList<int> results;
    
    public void Execute()
    {
        float radiusSquared = queryRadius * queryRadius;
        
        // Calculate grid bounds
        Int3 minGrid = GetGridPosition(queryPosition - Vector3.one * queryRadius);
        Int3 maxGrid = GetGridPosition(queryPosition + Vector3.one * queryRadius);
        
        // Iterate through grid cells
        for (int x = minGrid.x; x <= maxGrid.x; x++)
        {
            for (int y = minGrid.y; y <= maxGrid.y; y++)
            {
                for (int z = minGrid.z; z <= maxGrid.z; z++)
                {
                    int hash = CalculateHash(x, y, z);
                    
                    // Binary search for particles in this cell
                    int startIndex = BinarySearch(hashAndIndices, hash);
                    
                    if (startIndex >= 0)
                    {
                        // Check particles in this cell
                        while (startIndex < hashAndIndices.Length && 
                               hashAndIndices[startIndex].hash == hash)
                        {
                            int particleIndex = hashAndIndices[startIndex].index;
                            Particle particle = particles[particleIndex];
                            
                            float distanceSquared = 
                                (particle.position - queryPosition).sqrMagnitude;
                                
                            if (distanceSquared <= radiusSquared)
                            {
                                results.Add(particleIndex);
                            }
                            
                            startIndex++;
                        }
                    }
                }
            }
        }
    }
}
```

## [[Best Practices]]
- Use [[NativeArrays]] for efficient memory access
- Implement proper cleanup with [[Dispose()]]
- Use [[BurstCompile]] for optimal performance
- Consider [[Batch Sizes]] when scheduling jobs
- Handle [[Domain Reloading]] properly
- Optimize [[Grid Cell Size]] for specific use cases

## [[Performance Considerations]]
- Test with different grid resolutions
- Profile memory usage with NativeContainers
- Consider job dependencies and scheduling
- Optimize hash function for specific data distributions
- Use parallel jobs where possible

## [[Applications]]
This technique is useful for:
- [[Real-time Strategy Games]]
- [[Physics Simulations]]
- [[Crowd Simulations]]
- [[Particle Systems]]
- Any system requiring efficient spatial queries

## [[Additional Resources]]
- [[Unity Documentation: Job System]]
- [[Burst Compiler Best Practices]]
- [[NativeContainer Performance Guide]]
- [Complete Code on GitHub](https://github.com/Unity-Technologies/SpatialHashingExample)
- [Spatial Hashing Research Paper](https://example.com/spatial-hashing-paper)