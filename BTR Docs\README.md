# BTR Documentation Hub

*Last Updated: 2025-07-18*
*Documentation Reorganization Complete*

## 🚀 Quick Start

### For Active Development
- **[[Current/README]]** - Current documentation index
- **[[Current/Architecture/System Overview]]** - System architecture
- **[[Current/Performance/Editor Performance Investigation Log]]** - Performance issues
- **[[Current/Bug Tracking/KnownIssues]]** - Known issues

### For Project Management
- **[[Project Management/2025/Daily Progress Journal Template]]** - Daily logging template
- **[[Project Management/2025/]]** - Current project logs

## 📁 Documentation Structure

### Current (Active Development)
```
Current/
├── Architecture/           # System architecture and design
├── Performance/           # Performance optimization guides
├── Bug Tracking/          # Known issues and resolutions
└── Development/           # Technical development guides
```

### Archive (Historical Reference)
```
Archive/
├── Pre-2024/             # Development logs and research (2020-2023)
├── Business/              # Funding and business documentation
├── Game Development/      # Game design and development history
└── Learning & Business/   # Research and learning materials
```

### Project Management (Ongoing)
```
Project Management/
├── 2025/                 # Current year development logs
├── 2024/                 # Previous year logs
├── 2023/                 # Historical logs
└── 2022/                 # Historical logs
```

## 🎯 Documentation Categories

### System Architecture
- **Core Architecture** - Central system design
- **Enemy System** - AI behaviors and combat
- **Player System** - Movement, health, combat
- **Projectile System** - High-performance projectile management
- **Audio System** - FMOD integration
- **UI System** - Interface management
- **VFX System** - Visual effects

### Performance & Optimization
- **Editor Performance** - Unity editor optimization
- **Runtime Performance** - Game performance optimization
- **Memory Management** - Object pooling and cleanup
- **Audio Optimization** - FMOD performance tuning

### Development Workflow
- **Bug Tracking** - Issue management and resolution
- **Code Standards** - Development guidelines
- **Testing** - Quality assurance procedures
- **Documentation** - This documentation system

## 🔧 Recent Reorganization (2025-07-18)

### What Changed
1. **Consolidated Architecture** - Single source of truth for system design
2. **Current vs Archive** - Clear separation of active vs historical documentation
3. **Fixed References** - Updated project name references throughout
4. **Removed Duplicates** - Eliminated redundant architecture documents
5. **Deep Archive** - Moved pre-2024 content to appropriate archives

### What Was Archived
- **Pre-2024 Development Logs** - Historical reference preserved
- **Original Game Design Docs** - "Beat Traveller" era documentation
- **Funding Documentation** - Grant applications and business materials
- **Old Research** - Audio research, GDC talks, academic materials
- **Deprecated Systems** - Outdated technical documentation

### What Remains Current
- **Architecture Documentation** - System design and patterns
- **Performance Guides** - Active optimization documentation
- **Bug Tracking** - Current issues and resolutions
- **2025 Development Logs** - Ongoing project management

## 📊 Documentation Statistics

- **Total Files**: 680 markdown files
- **Current Documentation**: ~140 files (20%)
- **Archived Content**: ~521 files (77%)
- **Deprecated (TrashBin)**: ~19 files (3%)

## 🚨 Important Notes

### For Developers
- Always check **Current/** directory first for active documentation
- Use **Archive/** for historical context only
- Update architecture docs when making system changes
- Follow the established tagging and linking conventions

### For Project Management
- Use **Project Management/2025/** for daily logs
- Follow the **Daily Progress Journal Template**
- Link to relevant technical documentation
- Update issue tracking regularly

## 🔗 External Links

- **Unity 6 Documentation** - Official Unity documentation
- **URP Documentation** - Universal Render Pipeline guides
- **FMOD Documentation** - Audio system integration
- **Repository** - Source code repository

## 📝 Maintenance

### Monthly Review
- Check for outdated current documentation
- Update architecture docs with system changes
- Archive completed project logs
- Clean up broken links

### Quarterly Cleanup
- Review archive organization
- Update documentation standards
- Consolidate redundant information
- Backup documentation system

---

*This documentation system uses Obsidian for knowledge management with proper linking, tagging, and organization. The vault is designed to support both active development and historical reference while maintaining clear separation between current and archived content.*