# Stylo.Reservoir - Object Pooling System

*Created: 2025-07-19*
*Status: Current - Core System*
*System Location: `Assets/Stylo/Resevoir/`*

## Overview

**Stylo.Reservoir** is a high-performance object pooling system designed for Unity 6 that provides **intelligent memory management**, **automatic optimization**, and **async prewarming capabilities**. Built with modern C# patterns and UniTask integration, it delivers production-ready pooling solutions optimized for performance-critical applications.

## System Architecture

```mermaid
flowchart TB
    %% Core Components
    RM[ReservoirManager<br/>Singleton Orchestrator]
    RPP[ReservoirPrefabPool<br/>Single Prefab Pool]
    RL[ReservoirLog<br/>Conditional Logging]
    
    %% Pool Management
    PM[Pool Management<br/>Dictionary Storage]
    IPE[InitialPoolEntry<br/>Configuration]
    SPA[Static Pool API<br/>Easy Access]
    
    %% Advanced Features
    AF[Advanced Features<br/>Optimization]
    AC[Automatic Culling<br/>Memory Management]
    AP[Async Prewarming<br/>UniTask Integration]
    LP[Limit Policies<br/>Overflow Handling]
    
    %% Pool Lifecycle
    PL[Pool Lifecycle<br/>Instance Management]
    SP[Spawn Process<br/>Object Creation]
    DP[Despawn Process<br/>Object Return]
    PW[Prewarming<br/>Batch Creation]
    
    %% Handler Interface
    HI[Handler Interface<br/>Lifecycle Events]
    IH[IPoolHandler<br/>Spawn/Despawn Events]
    OS[OnSpawned()<br/>Activation Logic]
    OD[OnDespawned()<br/>Cleanup Logic]
    
    %% Performance Features
    PF[Performance Features<br/>Optimization]
    BS[Batch Spawning<br/>Frame Distribution]
    MS[Memory Scaling<br/>Dynamic Sizing]
    LB[Load Balancing<br/>Queue Management]
    
    %% Integration Features
    IF[Integration Features<br/>Unity Systems]
    UT[UniTask Support<br/>Async Operations]
    ZL[ZLogger Integration<br/>Enhanced Logging]
    DL[Development Logging<br/>Conditional Debug]
    
    %% Relationships - Core
    RM --> PM
    RM --> SPA
    PM --> RPP
    PM --> IPE
    
    %% Pool Features
    RPP --> AF
    AF --> AC
    AF --> AP
    AF --> LP
    
    %% Lifecycle Management
    RPP --> PL
    PL --> SP
    PL --> DP
    PL --> PW
    
    %% Handler Integration
    RPP --> HI
    HI --> IH
    IH --> OS
    IH --> OD
    
    %% Performance Integration
    RPP --> PF
    PF --> BS
    PF --> MS
    PF --> LB
    
    %% System Integration
    RM --> IF
    IF --> UT
    IF --> ZL
    IF --> DL
    RL --> ZL
    RL --> DL
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef management fill:#bbf,stroke:#333,stroke-width:2px
    classDef features fill:#bfb,stroke:#333,stroke-width:2px
    classDef lifecycle fill:#fbb,stroke:#333,stroke-width:2px
    classDef handlers fill:#ffb,stroke:#333,stroke-width:2px
    classDef performance fill:#fbf,stroke:#333,stroke-width:2px
    classDef integration fill:#bff,stroke:#333,stroke-width:2px
    
    class RM,RPP,RL core
    class PM,IPE,SPA management
    class AF,AC,AP,LP features
    class PL,SP,DP,PW lifecycle
    class HI,IH,OS,OD handlers
    class PF,BS,MS,LB performance
    class IF,UT,ZL,DL integration
```

## Core Components

### **ReservoirManager** (Singleton Orchestrator)
- **Role**: Central pool management and static API provider
- **Pattern**: Singleton with DontDestroyOnLoad persistence
- **Location**: `Stylo/Resevoir/ReservoirManager.cs`

**Key Features**:
- Automatic pool creation and management
- Inspector-configurable initial pools
- Static API for easy access throughout the project
- Runtime pool monitoring and statistics

### **ReservoirPrefabPool** (Single Prefab Pool)
- **Role**: High-performance pool for individual prefab types
- **Pattern**: Queue-based object management with advanced features
- **Location**: `Stylo/Resevoir/ReservoirPrefabPool.cs`

**Key Features**:
- Dual-queue architecture (active/inactive objects)
- Configurable limit policies and overflow handling
- Automatic culling for memory optimization
- Async prewarming with UniTask integration

### **ReservoirLog** (Conditional Logging)
- **Role**: Performance-optimized logging system
- **Pattern**: Conditional compilation with ZLogger integration
- **Location**: `Stylo/Resevoir/ReservoirLog.cs`

**Key Features**:
- Development-only logging (stripped in release builds)
- ZLogger integration when available
- Verbose mode for detailed debugging
- Zero performance overhead in production

## Pool Management Architecture

### **Static API Design**
```csharp
// Simple spawn/despawn operations
GameObject projectile = ReservoirManager.Spawn(projectilePrefab, position, rotation);
ReservoirManager.Despawn(projectile);

// Prewarming pools
ReservoirManager.Instance.Prewarm(enemyPrefab, 50);
```

### **Configuration-Driven Setup**
```csharp
[System.Serializable]
public class InitialPoolEntry
{
    [Tooltip("Prefab to pool")]
    public GameObject prefab;
    
    [Tooltip("Initial pool size at startup")]
    public int preloadCount = 0;
    
    [Tooltip("Optional custom pool identifier")]
    public string customPoolName;
}
```

### **Advanced Pool Configuration**
```csharp
public ReservoirPrefabPool(
    GameObject prefab,
    Transform root,
    MonoBehaviour host,
    int preload = 0,
    int limit = 0,                           // Pool size limit (0 = unlimited)
    LimitPolicy limitPolicy = LimitPolicy.Fail,  // Overflow behavior
    bool matchLayer = false,                 // Match parent layer
    bool matchScale = false,                 // Match parent scale
    int cullAbove = 100,                     // Start culling above this count
    float cullDelay = 10f,                   // Delay before culling starts
    int cullBatchSize = 10,                  // Objects to cull per batch
    bool dontReparent = false,               // Keep original parent
    bool dontDestroyOnLoad = false           // Persist across scenes
)
```

## Advanced Features

### **Intelligent Memory Management**

#### **Automatic Culling System**
```csharp
private IEnumerator CullCoroutine()
{
    yield return new WaitForSeconds(_cullDelay);
    
    while (_inactive.Count > _cullAbove)
    {
        // Cull in batches to prevent frame drops
        for (int i = 0; i < _cullBatchSize && _inactive.Count > _cullAbove; i++)
        {
            var obj = _inactive.Dequeue();
            Object.Destroy(obj);
        }
        yield return null; // Wait one frame between batches
    }
    
    _cullRoutine = null;
}
```

#### **Memory Pressure Response**
- **Dynamic sizing**: Pools automatically adjust based on usage patterns
- **Batch culling**: Objects destroyed in small batches to prevent frame drops
- **Configurable thresholds**: Customize when culling begins and how aggressive it is
- **Memory monitoring**: Track pool usage and optimize based on real-time data

### **Limit Policies and Overflow Handling**

#### **Fail Policy** (Default)
```csharp
if (_limit > 0 && TotalCount >= _limit)
{
    return null; // Fail gracefully when limit reached
}
```

#### **Recycle Oldest Policy**
```csharp
if (_limitPolicy == LimitPolicy.RecycleOldest && _active.Count > 0)
{
    var recycle = _active.Dequeue();
    Despawn(recycle); // Automatically despawn oldest active object
}
```

### **Async Prewarming with UniTask**

#### **Standard Coroutine Prewarming**
```csharp
public Coroutine PrewarmOverTime(int total, int perFrame)
{
    return _host.StartCoroutine(PrewarmRoutine(total, perFrame));
}

private IEnumerator PrewarmRoutine(int total, int perFrame)
{
    int created = 0;
    while (created < total)
    {
        int batch = Mathf.Min(perFrame, total - created);
        Prewarm(batch);
        created += batch;
        yield return null; // Spread load across frames
    }
}
```

#### **UniTask Async Prewarming**
```csharp
#if UNITASK_PRESENT
public UniTask PrewarmAsync(int total, int perFrame, CancellationToken ct = default)
{
    return UniTask.Create(async () =>
    {
        int created = 0;
        while (created < total && !ct.IsCancellationRequested)
        {
            int batch = Mathf.Min(perFrame, total - created);
            Prewarm(batch);
            created += batch;
            await UniTask.Yield(PlayerLoopTiming.Update, ct);
        }
    });
}
#endif
```

## Pool Lifecycle Management

### **Spawn Process**
```csharp
public GameObject Spawn(Vector3 pos, Quaternion rot, Transform parent = null)
{
    GameObject obj;
    
    // Try to reuse existing inactive object
    if (_inactive.Count > 0)
    {
        obj = _inactive.Dequeue();
    }
    else
    {
        // Handle pool limits
        if (_limit > 0 && TotalCount >= _limit)
        {
            if (_limitPolicy == LimitPolicy.RecycleOldest && _active.Count > 0)
            {
                var recycle = _active.Dequeue();
                Despawn(recycle);
            }
            else
            {
                return null; // Fail if limit reached
            }
        }
        
        // Create new object
        obj = Object.Instantiate(Prefab);
        obj.name = $"{Prefab.name}-{_nameCounter++}";
    }
    
    // Configure object
    SetupSpawnedObject(obj, pos, rot, parent);
    
    // Notify handler
    if (obj.TryGetComponent<IPoolHandler>(out var handler))
        handler.OnSpawned();
    
    return obj;
}
```

### **Despawn Process**
```csharp
public void Despawn(GameObject obj)
{
    if (obj == null) return;
    
    // Notify handler before deactivation
    if (obj.TryGetComponent<IPoolHandler>(out var handler))
        handler.OnDespawned();
    
    // Reset object state
    obj.SetActive(false);
    obj.transform.SetParent(_storage);
    obj.transform.localPosition = Vector3.zero;
    
    // Return to pool
    _inactive.Enqueue(obj);
    _active.Dequeue();
    
    // Check if culling is needed
    TryStartCull();
}
```

### **IPoolHandler Interface**
```csharp
public interface IPoolHandler
{
    void OnSpawned();   // Called when object is spawned from pool
    void OnDespawned(); // Called when object is returned to pool
}

// Example implementation
public class PoolableProjectile : MonoBehaviour, IPoolHandler
{
    public void OnSpawned()
    {
        // Reset projectile state
        GetComponent<Rigidbody>().velocity = Vector3.zero;
        GetComponent<TrailRenderer>().Clear();
        // Enable components, reset timers, etc.
    }
    
    public void OnDespawned()
    {
        // Clean up projectile state
        GetComponent<TrailRenderer>().Clear();
        // Disable effects, clear references, etc.
    }
}
```

## Performance Optimization

### **Frame Distribution**
- **Batch processing**: Large operations split across multiple frames
- **Configurable batch sizes**: Customize performance vs. responsiveness
- **Yield patterns**: Smart yielding to maintain frame rate
- **Load balancing**: Distribute expensive operations over time

### **Memory Efficiency**
- **Queue-based management**: O(1) spawn/despawn operations
- **Minimal allocations**: Reuse collections and minimize GC pressure
- **Smart prewarming**: Predictive pool sizing based on usage patterns
- **Automatic cleanup**: Intelligent culling prevents memory bloat

### **Monitoring and Analytics**
```csharp
// Pool statistics for debugging and optimization
public int ActiveCount => _active.Count;
public int InactiveCount => _inactive.Count;
public int TotalCount => _active.Count + _inactive.Count;
public IEnumerable<GameObject> ActiveObjects => _active;

// Manager-level pool monitoring
public IReadOnlyDictionary<string, ReservoirPrefabPool> Pools => _pools;
```

## Configuration and Usage

### **Basic Setup**
```csharp
// 1. Add ReservoirManager to a GameObject (or create one automatically)
// 2. Configure initial pools in the inspector
// 3. Use static API for spawning/despawning

// Simple usage
GameObject bullet = ReservoirManager.Spawn(bulletPrefab, firePoint.position, firePoint.rotation);

// Later, when bullet expires
ReservoirManager.Despawn(bullet);
```

### **Advanced Configuration**
```csharp
// Prewarm pools for better performance
void Start()
{
    // Prewarm 100 bullets immediately
    ReservoirManager.Instance.Prewarm(bulletPrefab, 100);
    
    // Prewarm 500 enemies over time (10 per frame)
    StartCoroutine(ReservoirManager.Instance.Pools["Enemy"]
        .PrewarmOverTime(500, 10));
}

#if UNITASK_PRESENT
// Async prewarming with UniTask
async UniTask PrewarmPoolsAsync()
{
    var cts = this.GetCancellationTokenOnDestroy();
    
    // Prewarm multiple pools concurrently
    await UniTask.WhenAll(
        ReservoirManager.Instance.Pools["Bullet"].PrewarmAsync(200, 20, cts),
        ReservoirManager.Instance.Pools["Enemy"].PrewarmAsync(50, 5, cts),
        ReservoirManager.Instance.Pools["Pickup"].PrewarmAsync(30, 3, cts)
    );
}
#endif
```

### **Pool Handler Implementation**
```csharp
public class PoolableEnemy : MonoBehaviour, IPoolHandler
{
    private EnemyHealth health;
    private EnemyAI ai;
    private Animator animator;
    
    void Awake()
    {
        health = GetComponent<EnemyHealth>();
        ai = GetComponent<EnemyAI>();
        animator = GetComponent<Animator>();
    }
    
    public void OnSpawned()
    {
        // Reset all systems for reuse
        health.ResetToFullHealth();
        ai.ResetToDefaultState();
        animator.Play("Spawn");
        
        // Re-enable components
        ai.enabled = true;
        GetComponent<Collider>().enabled = true;
        
        // Clear any lingering effects
        transform.localScale = Vector3.one;
        GetComponent<Rigidbody>().velocity = Vector3.zero;
    }
    
    public void OnDespawned()
    {
        // Clean shutdown
        ai.enabled = false;
        StopAllCoroutines();
        
        // Clear visual effects
        var particles = GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particles)
        {
            ps.Stop();
            ps.Clear();
        }
        
        // Reset material properties if changed
        var renderer = GetComponent<Renderer>();
        if (renderer != null && renderer.material.HasProperty("_Color"))
        {
            renderer.material.color = Color.white;
        }
    }
}
```

## Integration Points

### **Unity Systems Integration**
- **UniTask Support**: Modern async patterns for prewarming and operations
- **ZLogger Integration**: High-performance logging when available
- **Development Builds**: Conditional logging and debugging features
- **Scene Management**: Persistent pools across scene transitions

### **Stylo Framework Integration**
- **Stylo.Epoch**: Time-aware pool operations for time manipulation
- **FMOD Audio**: Pooled audio instances for enhanced performance
- **Stylo.Flux**: VFX object pooling for visual effects
- **Performance Monitoring**: Integration with system-wide performance tracking

### **Memory Management Integration**
- **Automatic GC**: Minimize garbage collection pressure
- **Memory Pressure**: Respond to system memory constraints
- **Platform Scaling**: Adjust pool sizes based on platform capabilities
- **Resource Monitoring**: Track and optimize memory usage

## Best Practices

### **Pool Configuration**
- **Size appropriately**: Start with usage analytics to determine optimal pool sizes
- **Prewarm strategically**: Prewarm frequently used objects during loading screens
- **Monitor performance**: Use built-in statistics to optimize pool parameters
- **Plan for spikes**: Configure pools to handle usage bursts gracefully

### **Handler Implementation**
- **Reset completely**: Ensure objects are fully reset in OnSpawned()
- **Clean thoroughly**: Properly clean up in OnDespawned()
- **Handle edge cases**: Account for objects being despawned in unexpected states
- **Avoid memory leaks**: Clear all references and subscriptions

### **Performance Optimization**
- **Batch operations**: Use prewarming methods for large pool initialization
- **Frame awareness**: Spread expensive operations across multiple frames
- **Memory monitoring**: Regularly check pool sizes and cull when appropriate
- **Profile regularly**: Use Unity Profiler to identify bottlenecks

## Debugging and Monitoring

### **Verbose Logging**
```csharp
// Enable in ReservoirManager inspector or code
ReservoirManager.Instance.verboseLogging = true;

// Logs will show:
// [Reservoir] Spawn Bullet => Active:15 Inactive:85
// [Reservoir] Despawn Bullet => Active:14 Inactive:86
```

### **Pool Statistics**
```csharp
// Access pool statistics for monitoring
foreach (var kvp in ReservoirManager.Instance.Pools)
{
    var poolName = kvp.Key;
    var pool = kvp.Value;
    
    Debug.Log($"Pool '{poolName}': " +
              $"Active: {pool.ActiveCount}, " +
              $"Inactive: {pool.InactiveCount}, " +
              $"Total: {pool.TotalCount}");
}
```

### **Memory Profiling**
```csharp
// Monitor memory usage and optimization
private void Update()
{
    if (Input.GetKeyDown(KeyCode.P))
    {
        LogPoolStatistics();
    }
}

private void LogPoolStatistics()
{
    var pools = ReservoirManager.Instance.Pools;
    foreach (var pool in pools)
    {
        Debug.Log($"Pool {pool.Key}: {pool.Value.TotalCount} objects " +
                  $"({pool.Value.ActiveCount} active)");
    }
}
```

## Future Enhancements

### **Planned Features**
- **Burst compilation**: High-performance operations with Burst compiler
- **Job system integration**: Multithreaded pool operations
- **Adaptive sizing**: AI-driven pool size optimization
- **Cross-scene persistence**: Advanced scene transition handling

### **Performance Improvements**
- **Native collections**: Unity's native containers for better performance
- **Pooled collections**: Reuse internal data structures
- **Memory mapping**: Advanced memory layout optimization
- **Platform profiles**: Platform-specific optimization presets

## Related Systems

- **[[Stylo.Epoch - Time Manipulation Framework]]** - Time-aware pooling operations
- **[[Stylo.Flux - Visual Effects Framework]]** - VFX object pooling integration
- **[[FMOD Advanced Audio Integration]]** - Audio instance pooling
- **[[Unity Performance Optimization]]** - Memory and CPU optimization strategies

## Notes

**Stylo.Reservoir** represents a **production-ready object pooling solution** that combines high performance with ease of use. Its intelligent memory management, automatic optimization features, and comprehensive monitoring capabilities make it suitable for both indie games and AAA productions.

The system's strength lies in its **balance of simplicity and power** - providing a simple static API for basic usage while offering advanced configuration options and optimization features for performance-critical applications. The automatic culling and memory management ensure reliable performance even in long-running applications.