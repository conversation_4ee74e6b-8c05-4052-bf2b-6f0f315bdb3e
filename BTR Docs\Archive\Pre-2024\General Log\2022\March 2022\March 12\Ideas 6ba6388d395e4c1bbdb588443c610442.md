# Ideas

Looking at old docs

- [x]  Starfox Movement - Mix and Jam [https://www.youtube.com/watch?v=yuQXeaYBuuM](https://www.youtube.com/watch?v=yuQXeaYBuuM)
    - [ ]  Fix Camera to allow slight movement like in this example? Also fluid Character movement across screen like this example?
- [ ]  Asteroid belt of projectiles that you can grab
- [x]  Dead Eye Lock on  - Mix and Jam [https://www.youtube.com/watch?v=jPnMVeWnZLc](https://www.youtube.com/watch?v=jPnMVeWnZLc)
- [x]  Ribbon trail on particles effects for creating lines
- [x]  Change particle speed when locked?
- [x]  At the end of odd grouping of locked on targets, play a tag to even it out!
    - [ ]  Evolve approach to tags a bit more?
- [ ]  Try Flat pack to achieve Channel 21-like shading and colours
- [ ]  Change Camera perspective in different level sections
- [ ]  exploring possibilities with joost shader / other amplify shader possibilties
- [ ]  Look into animation Rigging options
- [ ]  Mario Galaxy gravity - useful in some enemy type?

[https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/](https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/)

- [ ]  Score is a Timer - you are buying yourself time by getting enemies - HEALTH
- [ ]  select targets - freeze time lets you move their position - targets explode or chain with other enemies - No dolly, your location changes based on moving across selected targets in time.
- [ ]  If moving selected objects while time is frozen, lock on is disabled? Only able to move object during the time freezes. this would make time freeze mechanic used quiet frequently. Also makes me think - how to do this in 3d? visually hard to read. sort of like super hyper cube
- [ ]  Slowly emit shapes that can be drawn through lock on. emitted through bursts.  completing shape does something? extra points?
- [ ]  MATCH 2 / 3 style gameplay? Lock on / colours mean anything?
- [ ]  GROWING SQUARES LEVEL - have things grow - camera shake when enemies beaten

Move to another set of growing square and enemies. 

- [ ]  Alternate cross hairs per target / area?? Are there **combos** per enemy?
- [ ]  Particle Spawner releases different patterns that can be drawn?
- [ ]  Draw shapes with target selections - could this be a shield to protect against an enemy?
- [ ]  Should I have an open world game area? You latch on to rail sections? Jet streams?
- [ ]  Voiceover with lip sync? evangelion face in horizon - looming head - type of thing
- [ ]  Fargo dePalma multiple camera angles - could use splitting the screen effectively like this?
- [ ]  Look at Patapon / Vib Ribbon
- [ ]  Lumines - integrate these ideas?
- [ ]  Intelligent Qube - [https://www.youtube.com/watch?v=c9bilp-9HVY](https://www.youtube.com/watch?v=c9bilp-9HVY)
- [ ]  What can be learned from Every Extend Extra?
- [ ]  Music for 18 Musicians - In/Out of Sync elements
- [ ]  Evangelion UI [https://www.reddit.com/r/evangelion/comments/nczhg8/i_rescored_evangelion_ui_with_my_take_on_the/](https://www.reddit.com/r/evangelion/comments/nczhg8/i_rescored_evangelion_ui_with_my_take_on_the/)
- [ ]  I am sitting in a room
- [ ]  Rotating bullets to next view - Do this or Drop it? Is Lock On better?
- [ ]  Rubik’s cube but you’re the cube
- [ ]  lock to targets - generate mesh from locations - we have a shape now - assign material?
- [ ]  Maybe think backwards - what should happen - what sounds would then coincide with that?
- [ ]  Puzzle Game Thinking
    
    [Mechanics](Ideas%206ba6388d395e4c1bbdb588443c610442/Mechanics%204f1c116e937e4517bb7af82809b3243a.md)