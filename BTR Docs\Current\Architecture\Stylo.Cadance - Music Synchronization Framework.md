# Stylo.Cadance - Music Synchronization Framework

*Created: 2025-07-18*
*Status: Current - Core System*
*System Location: `Assets/Stylo/Cadance/`*

## Overview

**Stylo.Cadance** is a comprehensive music synchronization framework designed as a **complete replacement for Koreographer**. It provides **sample-accurate timing**, **FMOD Studio integration**, and **advanced music-synchronized gameplay mechanics**. The system emphasizes performance, extensibility, and seamless migration while offering enhanced features for professional music-driven game development.

## System Architecture

```mermaid
flowchart TB
    %% Core System
    CAD[Cadance<br/>Core Singleton]
    CER[CadanceEventRegistry<br/>Event Management]
    CE[CadanceEvent<br/>Sample-Accurate Events]
    
    %% Asset System
    CA[CadanceAsset<br/>ScriptableObject]
    CTB[CadanceTrackBase<br/>Abstract Track]
    CT[CadanceTrack<br/>Concrete Track]
    TS[TempoSection<br/>BPM Management]
    
    %% FMOD Integration
    FCM[FMODCadanceManager<br/>FMOD Coordinator]
    FEIT[FMODEventInstanceTracker<br/>Event Tracking]
    FCP[FMODCadancePlayer<br/>Player Implementation]
    
    %% Event System
    EO[EventObj<br/>Event Dispatcher]
    PL[Payload System<br/>Data Types]
    DS[DeltaSlice<br/>Timing Precision]
    
    %% Scene Management
    CSM[CadanceSceneManager<br/>Scene Coordination]
    CEE[CadancedEventEmitter<br/>Component System]
    
    %% Beat Detection
    CBD[CadanceBeatDetector<br/>Rhythm Analysis]
    BI[BeatInfo<br/>Timing Data]
    
    %% Performance & Migration
    CPM[CadancePerformanceMonitor<br/>Optimization]
    CMH[CadanceMigrationHelper<br/>Koreographer Migration]
    CRC[CadanceRuntimeConfig<br/>Configuration]
    
    %% Core Relationships
    CAD --> CER
    CAD --> CE
    CER --> EO
    
    %% Asset Flow
    CA --> CT
    CT --> CTB
    CT --> TS
    CT --> CE
    
    %% FMOD Integration
    FCM --> FEIT
    FCM --> FCP
    FEIT --> CE
    FCP --> DS
    
    %% Scene System
    CSM --> CEE
    CSM --> FCM
    CEE --> CE
    
    %% Beat Detection
    CBD --> BI
    CBD --> CE
    
    %% Support Systems
    CPM --> CAD
    CMH --> CAD
    CRC --> CAD
    
    %% Event Flow
    CE --> PL
    CE --> DS
    EO --> PL
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef asset fill:#bbf,stroke:#333,stroke-width:2px
    classDef fmod fill:#bfb,stroke:#333,stroke-width:2px
    classDef event fill:#fbb,stroke:#333,stroke-width:2px
    classDef scene fill:#ffb,stroke:#333,stroke-width:2px
    classDef support fill:#fbf,stroke:#333,stroke-width:2px
    
    class CAD,CER,CE core
    class CA,CTB,CT,TS asset
    class FCM,FEIT,FCP fmod
    class EO,PL,DS event
    class CSM,CEE,CBD,BI scene
    class CPM,CMH,CRC support
```

## Core Components

### **Cadance** (Core Singleton)
- **Role**: Central coordination system for all music synchronization
- **Pattern**: Singleton with domain reload handling
- **Location**: `Stylo/Cadance/Core/Runtime/Cadance.cs`
- **Execution Order**: -200 (ensures initialization before other systems)

**Key Features**:
- Central coordination system for all music synchronization
- Backward compatibility with Koreographer APIs (`LoadKoreography()`, `LoadFMODKoreographySet()`)
- Sample-accurate timing with microsecond precision
- Comprehensive event registration and dispatch system
- Performance monitoring and optimization

### **CadanceEventRegistry** (Event Management)
- **Role**: Global event registration and validation system
- **Pattern**: Centralized registry with type safety
- **Location**: `Stylo/Cadance/Core/Runtime/CadanceEventRegistry.cs`

**Key Features**:
- Global event registration and validation
- Thread-safe event management
- Type-safe event binding with payload validation
- Automatic cleanup and memory management
- Error handling and graceful degradation

### **CadanceEvent** (Sample-Accurate Events)
- **Role**: Core event class with sample-accurate timing
- **Pattern**: Immutable event data with full payload support
- **Location**: `Stylo/Cadance/Core/Runtime/CadanceEvent.cs`

```csharp
public class CadanceEvent
{
    public int SampleTime { get; }           // Sample-accurate timing
    public string EventID { get; }           // Event identifier
    public IPayload Payload { get; }         // Event data
    public float StartSample { get; }        // Event start position
    public float EndSample { get; }          // Event end position (for duration events)
}
```

## Asset System

### **CadanceAsset** (ScriptableObject)
- **Role**: Container for tracks, tempo sections, and metadata
- **Pattern**: ScriptableObject-based asset management
- **Location**: `Stylo/Cadance/Core/Runtime/CadanceAsset.cs`

**Key Features**:
- ScriptableObject containing tracks, tempo sections, and metadata
- Version control and asset validation
- Editor integration with timeline visualization
- Migration utilities for Koreographer assets

### **CadanceTrack** (Event Container)
- **Role**: Concrete implementation supporting all payload types
- **Pattern**: Generic track system with type safety
- **Location**: `Stylo/Cadance/Core/Runtime/CadanceTrack.cs`

**Key Features**:
- Event management with sample-accurate positioning
- Support for all payload types
- Timeline editing and visualization
- Batch operations for performance

### **TempoSection** (BPM Management)
- **Role**: Handles dynamic BPM changes and timing calculations
- **Pattern**: Time-based section system
- **Location**: `Stylo/Cadance/Core/Runtime/TempoSection.cs`

```csharp
public class TempoSection
{
    public float StartTime { get; }          // Section start time
    public float BPM { get; }                // Beats per minute
    public int BeatsPerMeasure { get; }      // Time signature numerator
    public int NoteValue { get; }            // Time signature denominator
}
```

## FMOD Integration

### **FMODCadanceManager** (FMOD Coordinator)
- **Role**: Comprehensive FMOD Studio integration
- **Pattern**: Manager pattern with event tracking
- **Location**: `Stylo/Cadance/Integrations/FMOD Studio/Runtime/FMODCadanceManager.cs`

**Key Features**:
- Event instance tracking with sample-accurate timing
- Thread-safe callbacks for audio thread safety
- Automatic event registration and discovery
- Resource management with proper cleanup
- Performance optimization through caching

### **FMODEventInstanceTracker** (Event Tracking)
- **Role**: Individual FMOD event tracking and synchronization
- **Pattern**: Per-instance tracking with state management
- **Location**: `Stylo/Cadance/Integrations/FMOD Studio/Runtime/FMODEventInstanceTracker.cs`

**Key Features**:
- Sample position monitoring and conversion
- Timeline position tracking for synchronization
- Event triggering based on FMOD playback state
- Performance optimization through position caching

### **FMODCadancePlayer** (Player Implementation)
- **Role**: Full ICadancePlayer implementation for FMOD
- **Pattern**: Player interface implementation
- **Location**: `Stylo/Cadance/Integrations/FMOD Studio/Runtime/FMODCadancePlayer.cs`

**Key Features**:
- FMOD event playback control
- Automatic sound detection and integration
- Thread-safe callback processing
- Real-time event processing at 50Hz update rate

## Event System

### **Payload System**
Comprehensive payload system supporting all Koreographer payload types:

**Basic Payloads**:
```csharp
// Basic data types with implicit conversion
IntPayload intData = 42;
FloatPayload floatData = 3.14f;
BoolPayload boolData = true;
TextPayload textData = "Hello World";
```

**Advanced Payloads**:
```csharp
// Advanced data types for complex events
SpectrumPayload spectrum;     // FFT frequency spectrum data
RMSPayload rms;              // Root Mean Square audio analysis
CurvePayload curve;          // Animation curve data
GradientPayload gradient;    // Color gradient data
AssetPayload asset;          // Unity object references
```

### **Event Registration**
```csharp
// Simple event registration
Cadance.Instance.RegisterForEvents("BeatEvent", OnBeatEvent);

// Time-aware event registration with delta slice
Cadance.Instance.RegisterForEventsWithTime("TimedEvent", OnTimedEvent);

// Unregistration
Cadance.Instance.UnregisterForEvents("BeatEvent", OnBeatEvent);
```

### **DeltaSlice Processing**
- **Role**: Represents time slices between update frames for precise audio processing
- **Pattern**: Immutable time slice with interpolation support
- **Location**: `Stylo/Cadance/Core/Runtime/DeltaSlice.cs`

```csharp
public struct DeltaSlice
{
    public int OffsetSample { get; }         // Slice start offset
    public int LengthSamples { get; }        // Slice duration in samples
    public float DeltaTime { get; }          // Frame delta time
    
    // Methods for precise timing calculations
    public bool ContainsSample(int sample);
    public float GetInterpolatedTime(int sample);
}
```

## Scene Management

### **CadanceSceneManager** (Scene Coordination)
- **Role**: Scene-level music synchronization coordination
- **Pattern**: Singleton with scene persistence options
- **Location**: `Stylo/Cadance/Core/Runtime/CadanceSceneManager.cs`

**Key Features**:
- Singleton pattern with automatic instance creation
- Configurable persistence across scene loads
- Automatic FMOD event detection and registration
- Runtime configuration application
- Centralized play/stop control for FMOD events

### **CadancedEventEmitter** (Component System)
- **Role**: Component-based event emission system
- **Pattern**: MonoBehaviour component with automatic registration
- **Location**: `Stylo/Cadance/Core/Runtime/CadancedEventEmitter.cs`

**Key Features**:
- Automatic registration with scene manager
- Event emission with payload support
- Visual debugging and editor integration
- Performance monitoring integration

## Beat Detection System

### **CadanceBeatDetector** (Rhythm Analysis)
- **Role**: Advanced rhythm analysis and beat detection
- **Pattern**: Component-based rhythm analysis
- **Location**: `Stylo/Cadance/Integrations/FMOD Studio/Runtime/CadanceBeatDetector.cs`

**Key Features**:
- Configurable beat patterns with accented beats
- Multiple pattern support with runtime switching
- Real-time BPM estimation from timing data
- Beat window detection for rhythm gaming
- Rhythm input scoring (perfect/good/ok/miss)

```csharp
// Beat pattern configuration
[System.Serializable]
public class BeatPattern
{
    public string name;
    public int beatsPerMeasure = 4;
    public int subdivisions = 1;
    public bool[] accentedBeats;
}
```

### **BeatInfo** (Timing Data)
```csharp
public class BeatInfo
{
    public int BeatNumber { get; }           // Current beat index
    public float BeatTime { get; }           // Beat position in song
    public float BPM { get; }                // Current BPM
    public float Percentage { get; }         // Position within beat (0-1)
    public bool IsAccented { get; }          // Is this an accented beat
    public int MeasureNumber { get; }        // Current measure
}
```

## Timing and Synchronization

### **Sample-Accurate Timing**
```csharp
// Beat time APIs
float beatTime = Cadance.Instance.GetBeatTime();
float beatPercentage = Cadance.Instance.GetBeatPercentage();
float bpm = Cadance.Instance.GetBPMForClip();
int samplesPerBeat = Cadance.Instance.GetSamplesPerBeat();
```

### **Event Timing**
- Sample-based event positioning for microsecond accuracy
- Delayed event processing with configurable delay queues
- Real-time processing with delta slice batching
- Automatic sync compensation for audio latency

## Performance Optimization

### **CadancePerformanceMonitor** (Optimization)
- **Role**: Real-time performance metrics and optimization
- **Pattern**: Monitoring system with configurable thresholds
- **Location**: `Stylo/Cadance/Core/Runtime/CadancePerformanceMonitor.cs`

**Key Features**:
- Real-time performance metrics collection
- Event processing time tracking with rolling averages
- Performance warning system with configurable thresholds
- Memory-efficient sample collection with fixed-size buffers

### **Runtime Optimizations**
- Efficient event lookup with Dictionary-based systems
- Minimal garbage collection through object pooling patterns
- Cached sample rate and tempo calculations
- Batched event processing to reduce frame-rate impact

### **Configuration-Driven Performance**
```csharp
[System.Serializable]
public class CadanceRuntimeConfig
{
    public int maxEventsPerFrame = 50;       // Event processing limit
    public int bufferSize = 1024;            // Audio buffer size
    public bool enableBatchProcessing = true; // Batch processing
    public float performanceThreshold = 5.0f; // Warning threshold (ms)
}
```

## Koreographer Migration

### **Migration Support**
The framework provides extensive migration support:

**API Compatibility**:
```csharp
// Legacy Koreographer code still works
Koreographer.Instance.RegisterForEvents("Event", callback);

// Modern Cadance equivalent
Cadance.Instance.RegisterForEvents("Event", callback);
```

### **CadanceMigrationHelper** (Migration Tools)
- **Role**: Automated component and asset conversion
- **Pattern**: Static utility class with validation
- **Location**: `Stylo/Cadance/Migration/CadanceMigrationHelper.cs`

**Key Features**:
- `ConvertKoreographyToCadance()` for automatic asset conversion
- Legacy method signatures maintained where possible
- Asset conversion tools with validation
- Component migration verification

## Configuration and Usage

### **Basic Setup**
```csharp
// Initialize Cadance system
CadanceSceneManager sceneManager = CadanceSceneManager.Instance;
sceneManager.autoDetectEvents = true;
sceneManager.persistAcrossScenes = false;

// Register for music events
Cadance.Instance.RegisterForEvents("Beat", OnBeat);
Cadance.Instance.RegisterForEvents("Measure", OnMeasure);
```

### **FMOD Integration Setup**
```csharp
// Setup FMOD integration
FMODCadanceManager fmodManager = FMODCadanceManager.Instance;
fmodManager.Initialize();

// Play FMOD event with synchronization
EventInstance musicEvent = RuntimeManager.CreateInstance("event:/Music/MainTheme");
fmodManager.RegisterEventInstance(musicEvent);
musicEvent.start();
```

### **Beat Detection Setup**
```csharp
// Configure beat detector
CadanceBeatDetector beatDetector = GetComponent<CadanceBeatDetector>();
beatDetector.beatsPerMeasure = 4;
beatDetector.bpm = 120f;

// Register for beat events
beatDetector.OnBeat.AddListener(OnBeatDetected);
beatDetector.OnMeasure.AddListener(OnMeasureDetected);
```

## Advanced Features

### **Real-time Audio Analysis**
- FFT spectrum analysis integration
- RMS level monitoring for dynamic response
- Frequency band analysis for selective triggering
- Real-time audio feature extraction

### **Thread-Safe Architecture**
- Proper FMOD callback handling on audio thread
- Lock-free data structures where possible
- Safe cross-thread communication patterns
- Deadlock prevention mechanisms

### **Editor Integration**
- Timeline editing and visualization tools
- Real-time performance monitoring in editor
- Asset conversion and validation utilities
- Comprehensive property drawers for custom types

## Best Practices

### **Performance**
- Use appropriate buffer sizes for target latency
- Monitor performance metrics during development
- Batch events when possible for efficiency
- Configure event limits based on target platform

### **Architecture**
- Register events during initialization
- Use payload system for complex event data
- Implement proper cleanup for dynamic objects
- Design events for sample-accurate timing

### **Integration**
- Test music synchronization across different BPMs
- Validate event timing with audio analysis tools
- Implement fallback behaviors for missing events
- Use beat detection for rhythm-based gameplay

## Related Systems

- **[[Stylo.Epoch - Time Manipulation Framework]]** - Time effects integration
- **[[Advanced FMOD Integration]]** - Audio system coordination
- **[[Enemy System Architecture]]** - Music-synchronized enemy behaviors
- **[[VFX System Architecture]]** - Music-synchronized visual effects

## Notes

Stylo.Cadance represents a **comprehensive, production-ready music synchronization framework** that provides 100% feature parity with Koreographer while adding significant enhancements. The system demonstrates excellent software engineering practices with proper separation of concerns, comprehensive error handling, and extensive tooling support.

The framework's strength lies in its **sample-accurate timing**, **comprehensive FMOD integration**, and **robust event system**, making it suitable for professional music-driven game development. The extensive migration support ensures seamless transition from Koreographer-based projects.