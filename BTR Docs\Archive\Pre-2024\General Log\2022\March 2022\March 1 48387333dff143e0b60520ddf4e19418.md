# March 1

FMOD Tutorials

Transition Regions and Transition Markers in old tutorials

Does it make sense to just use Magnet regions? Can move forwards and backwards with them

Do all these separated audio clips mean a lot of work for Koreographer implementation?

Double click logic tracks to bring in faces etc for better transitions - can add new sounds this way too - like stingers

Not looping cleanly - can fake this with a Transition Marker that pulls you back? Is this a bad practice?

Investigate transitioning between Magnet Regions - Can this happen smoothly?

What are FMOD Playlists?

### [**Event workflow**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-200.html#event-workflow)

New features have been added to assist in keeping events tidy. Firstly, it is now possible to name [**loop regions**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#loop-region). This allows you to transition directly to a loop region without needing to add a [**destination marker**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#destination-marker).

### [**Action sheets**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-201.html#action-sheets)

Action sheets provide a new, simplified way to trigger [**instruments**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#instrument). Instruments placed on an action sheet are triggered when an [**event**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#event) is played. Action sheets can be set to play instruments in a consecutive or concurrent fashion. Instruments on action sheets are always routed to the event's [**master track**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#master-track).

### [**Output Ports**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-202.html#output-ports)

Sending audio to auxiliary output ports has traditionally been a programmer task, but now it's part of the sound designer workflow within FMOD Studio.

With ports, you can make use of your target platforms' features for handling online voice chat and in-game music, route sound to individual controller and headset speakers, control controller vibration, and more.

Looking over FMOD 2.0 + Additions - Things to Investigate

- Labeled Parameters
- Global Parameters to effect many event instances
- Built-in Speed Parameter
- Flexible Paramters - One parameter changes another parameter
- Nested Multi-Instruments
- Command Instruments - lots to this i think!!

Trying implemnetation with Koreographer

**Can use code in script  FMOD Studio Parameter Trigger as basis for swithcing sections**

Set player to Trigger - does this break anything? Seems ok!

Now need to make sure locking and shooting work fine