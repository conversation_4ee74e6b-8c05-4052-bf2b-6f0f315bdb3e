# April 6

**Need to go through all Electronic Music Notes for ideas and harmonic concept thoughts**

Looking at Wave Spawner Code example to implement - laid this out yesterday

Removed Spawnable Identity script as I think it just gets added when Enemy is spawned

Sen<PERSON> email for help, but just destroying the game object for now

Unsure how I integrated pooling, need to look into this more!

Look into this, seems I talked to help and implemented my own script which overrides OnUltimateSpawnerInstantiate using HandleSpawnerInstantiate 

Maybe do the same with public static Action<Object> OnUltimateSpawnerDestroy; ?

Sent email to dev, will see if I hear back tomorrow!

May need to load from yesterday - any changes made today that were important?

Weird issues with A* that might just be fixed by going back a version - that takes a while sometimes though!

Doing that overnight

Random Flow for Blender - thought occurs, all these metallic textures and materials i see pop up for sci fi stuff. Play around with them! See what kind of things I can create. Could possibly bring a bit more of a textured look to characters and things like Tunic does