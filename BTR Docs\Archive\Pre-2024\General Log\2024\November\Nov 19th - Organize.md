# Nov 19th - Organize

- Fix alternate loop firing (CheckMusicState)
    - Just know the lock state - no need to talk to FMOD - its not central!
        - but how do we know the repeat time? integrate fmod callback system.
        - markers that delineate loop point + knowing if we are in lock state
- Projectile Collision layers
    - Need to define these and their behaviour
    - Code currently commented out for deactivate/return to pool on collision

Fix broken effects

- visual mainly - better replacements?
    - Scene start effects
    - Ricochet effect
    - Glitch times efectsd

Starting Sound broken

- Likely need better implementation of this occuring

Better visibility on Incoming Projectiles that are about to hit player

- Not really sure what to do here??

Shooting rate of static enemy shooting projectiles may be glitchy

- two at once, etc. May need to refine how this works

VFX SDF not working properly for all meshes

- need to rebuild this so it works or take another approach

VSYNC forces a set framerate (60fps?) this needs to be fixed or investigated

General FMOD implementation optimizations

- Need to keep track, possibly funnel these into an FMOD Manager to track instances
- Really don’t need a lot of instances of many things, we’re trying to reduce this

Need to fix lighting in several stages

Need to finish the explode enemy

Need to debug all audio cutting out but then returning

- Whats causing that to happen?

Streamline renderers, such as renderontop etc

May need to investigate performance impact of Enemy AI

Should projectile system be made into ECS? Unsure. Recommendations are:

- Move physics calculations to jobs using Unity.Mathematics
- Implement spatial partitioning for collision detection (as mentioned in "Insanely FAST Spatial Hashing" document)
- Use object pooling (which you've already started with ProjectilePoolType)
- Batch similar operations together

Specific implementation

1. Create a ProjectileUpdateJob for movement calculations
2. Use NativeArrays for storing position/rotation data
3. Keep MonoBehaviour for component references and Chronos integration
4. Use IJobParallelFor for processing multiple projectiles simultaneously

**Recommendation:** Start with a hybrid approach:

1. Keep your current MonoBehaviour structure for Chronos integration
2. Move performance-critical calculations (movement, targeting) to Jobs
3. Use Burst compilation for these jobs
4. Implement spatial partitioning for collision detection
5. Consider batching similar operations

Reduce number of shaders in use to optimize build in general

Fun Ideas

- FMOD plugin development
- GPU Instancer Pro for asteroids or other objects
- Implement bit crusher better to exclude elements
- Item Pickups
    - Dodge to grab?
- Final Boss!!
- Do I want a new highlight / outline solution?
- Idea
- Reticle speed slows down for easier aiming
    - This comes from Don pachi slow down with big laser vs regular shots and you move faster
- Thinks of the reticle as a shmup ship? In this context, what can be done differently?
- Use a lock on like layer section with wild missile curvy lasers as fx? Have some projectile fx packs that would work well for this I think
    - See also zero ranger / crimson clover
- Maybe implement the power up after shots mechanic that kill knight uses? Think about it or similar things