# March 11

Looking at state machines to clean up my code 

[https://www.youtube.com/watch?v=G1bd75R10m4&t=2s](https://www.youtube.com/watch?v=G1bd75R10m4&t=2s)

Not sure if relevant to what I need right now

Doing inventory of March issues and things done 

Listening to Infallible Code Stream while working on a few things!

---

**Grants**

[https://www.arts.on.ca/grants/media-artists-creation-projects](https://www.arts.on.ca/grants/media-artists-creation-projects)

April 7th Deadline!

[https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs](https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs)

May 4th!

---

**Things to Implement**

Stamina Bar for Rewinding

[https://www.youtube.com/watch?v=sUvwKH7qyQQ](https://www.youtube.com/watch?v=sUvwKH7qyQQ)

Another example [https://www.youtube.com/watch?v=Fs2YCoamO_U](https://www.youtube.com/watch?v=Fs2YCoamO_U)

Paramemter: User Labeled instead of using values

Collision to change song section not reliable - Alternative Methods

- Waves of Enemies killed
- Time Passed
- Kill certain enemies?

Magnet Regions - Can transition areas be used with this?

State Machines - valuable to my framework?

Too Many If statements! 

Also - Command Patterns - why use?

[https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s](https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s)

Projectiles that are launchable VS unlaunchable

What is defense for unlaunchable Projectile? Shield? Blast radius?

---

Book rec: [Playful Production](https://www.amazon.ca/Playful-Production-Process-Designers-Everyone/dp/0262045516/ref=sr_1_1?crid=3SYVFO1Y0ORKR&keywords=playful+production&qid=1647020200&sprefix=playful+production%2Caps%2C76&sr=8-1) 

Adding trail effect to standard bullets

Disabled LineRenderer and TrailRenderer

Changing Music Section changes from number based to names in FMOD - DONE

Trigger Enter/Exit not reliable for changing sections though - sometimes doesn’t work

Bullets are coming out of both ends of enemy? Not sure, possibly collision issue with bullets

Investigating this - looking at Projectile collisions

FOVSensor from SensorToolkit - use for Enemy Lock on instead??

Fleshing out Unlaunchable bullet more

Prefab Variant of regular bullets - changes are refelcted - working nicely!

It’s red and it works! Switched them to half speed of launchable projectiles

How should I stop/defend against these? 

- Time Rewind effects it? BEST and cool musical FX
- Shield blast?

Added this! Destorys Unlaunchable bullets - balancing issues important to implement

Need Shield FX to coincide with this

TEMPORAL BLAST

Looking through Ultimate VFX - some options

pf_vfx-ult_demo_psys_oneshot_turbulentImpact - using this!!!!!!

elements of pf_vfx-ult_demo_psys_oneshot_solar

elements of pf_vfx-ult_demo_psys_oneshot_organica

Looking through various FX packs for something good

Possibly HOLOFX pack for shield?

Could use more but have a particle system in place for now

Uni Bullet Hell for interesting patterns of bullets?

Do I need launched bullets on radar? maybe dull them instead of black?

Changed to faded! 

Character / Aniamtion broke! No idea how LOL - need to fix

Editing Radar to look nicer - now it looks nicer!

Adding Stamina Bar - it works! Need refinement though

Need to make sure it fits to the musical timeline as well

Good menu tutorial? 

[https://www.youtube.com/watch?v=Cq_Nnw_LwnI](https://www.youtube.com/watch?v=Cq_Nnw_LwnI)

Looking at cross hair for Reticle aiming - downloading some stuff to work with

Added cross hair - not sure I like it. Maybe needs to be actually ON the 3d reticle

Things to figure out

- Graphical Style
- Enemies shooting two bullets
- UltimateWaveSpawner issues
    - Can use OnWaveEnded event for changing sections

IDEA: Multiple Dolly paths with animation for transition between them?

Testing if OnWaveEnded actually works with stopping music

Wave not ending!! not sure why

Did not have “SpawnableIdentity” Class on enemies - lets wave spawner know enemies are dead

Still didn’t work - but seems like progress! This class references NavMeshAgent though

Need to look into this future, what’s need or not - Read manual!

ALSO - revisit core of what you’re trying to do - early documents and ideas

FIND that AGAIN

Think like thumper - lock on and fire to get past target - like Aaero sort of too

Think of the action in Rez

Lock on to many targets - then fire

BT is lock on to many bullets - fire at something - more possible reactions

Think Olli Olli world? Is quickly restarting a good thing to try?