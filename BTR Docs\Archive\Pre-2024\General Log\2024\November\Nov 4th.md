# Nov. 4th

Was trying to build an ECS effects system to replace particles, unsuccessful. Need to look into this further. 

Can i build exactly this?

https://antoinefougea.com/posts/unity-ecs-exploration-making-a-particle-system-part-1/

May be unnessecary - will a efficient use of VFX Graph solve this problem? 

IDEA -  one instance triggered by an event - walk me through how to set this up - and what's the most performant approach. it only needs one trigger, it just needs that to happen at the 300 enemies locations. 300 is not a set number as well, just an example

enemy locations are static, so can be registered in editor / before scene actually starts. 

EVEN BETTER - try the sparks manager script with sparks effect shader - most efficient?

```csharp

public void OnShoot()
{
  SparkManager.Instance.TriggerSpark(muzzlePoint.position);
}
```

Also look at Digital Layer Effect to replace the VFX Graph on each mesh - could be more efficient!

Also look at FMOD plugin development - compiling and putting them in the software

Projectile system could be moved to ECS, but the trick part appears to be Chronos interaction. Would need to make an ECS based system that tracks similar things, and then a translation layer between that and chronos, that properly keeps projectiles in line with Chronos actions. 

- what is the performance gain here? if significant, may be worth doing

Particle Systems and Physics conversions recommended - is this significant? performance gains through pursuing ECS versions of these?

try Mirage Impostors system on objects - seems better than others.