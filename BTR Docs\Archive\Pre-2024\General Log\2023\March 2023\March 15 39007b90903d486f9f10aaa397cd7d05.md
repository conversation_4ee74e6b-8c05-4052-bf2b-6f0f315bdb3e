# March 15

HDR screws up space time graphics assets, disabling HDR to see what I can do

Tried some models in blender, thinking on boss configurations

Made refactor and cleanup updates to Crosshair, Enemy Basic Setup, Shooter Movement and Projectile setup scripts. 

Added Unity built-in distance fog to have things fade off into distance better 

Added feature to slow down objects / time ! It’s working!!

Tried doing some UI things and no luck, need to dig in deep to setup I think

Do what I had previously done

Used Layer Mask to make sure Enemy lock on is not blocked by anything else, only see’s enemy layer. 

Should I do this for projectiles as well?

May be worth changing lock on / shoot sounds. Hi hats like Rez? Percussive elements? Have a rhythm track of hi hat drop out when locking on / firing, replaced with player behaviour?

Some Object Particle Spawner delays occuring - pooling issues?

 

![Untitled](March%2015%2039007b90903d486f9f10aaa397cd7d05/Untitled.png)