using UnityEngine;
using BTR;
using BTR.Projectiles;

/// <summary>
/// Checks if all required GameObjects and components are present in the scene
/// and provides automatic setup for missing components.
/// </summary>
public class SceneSetupChecker : MonoBehaviour
{
    [Header("Auto Setup")]
    [SerializeField] private bool autoSetupMissingComponents = true;
    [SerializeField] private bool enableDebugLogging = true;
    
    [Head<PERSON>("Required GameObjects")]
    [SerializeField] private GameObject projectileManagerPrefab;
    [SerializeField] private GameObject projectileSpawnerPrefab;
    [SerializeField] private GameObject projectilePoolPrefab;
    
    private void Start()
    {
        CheckSceneSetup();
    }
    
    [ContextMenu("Check Scene Setup")]
    public void CheckSceneSetup()
    {
        Debug.Log("🔍 === SCENE SETUP CHECKER ===");
        
        bool allComponentsPresent = true;
        
        // Check ProjectileManager
        if (ProjectileManager.Instance == null)
        {
            Debug.LogError("❌ ProjectileManager.Instance is NULL!");
            allComponentsPresent = false;
            
            if (autoSetupMissingComponents)
            {
                SetupProjectileManager();
            }
        }
        else
        {
            Debug.Log("✅ ProjectileManager found");
        }
        
        // Check ProjectileSpawner
        if (ProjectileSpawner.Instance == null)
        {
            Debug.LogError("❌ ProjectileSpawner.Instance is NULL!");
            allComponentsPresent = false;
            
            if (autoSetupMissingComponents)
            {
                SetupProjectileSpawner();
            }
        }
        else
        {
            Debug.Log("✅ ProjectileSpawner found");
        }
        
        // Check ProjectilePool
        if (ProjectilePool.Instance == null)
        {
            Debug.LogError("❌ ProjectilePool.Instance is NULL!");
            allComponentsPresent = false;
            
            if (autoSetupMissingComponents)
            {
                SetupProjectilePool();
            }
        }
        else
        {
            Debug.Log("✅ ProjectilePool found");
        }
        
        // Check ProjectileEffectManager
        if (ProjectileEffectManager.Instance == null)
        {
            Debug.LogError("❌ ProjectileEffectManager.Instance is NULL!");
            allComponentsPresent = false;
            
            if (autoSetupMissingComponents)
            {
                SetupProjectileEffectManager();
            }
        }
        else
        {
            Debug.Log("✅ ProjectileEffectManager found");
        }
        
        // Check CrosshairCore
        var crosshairCore = FindFirstObjectByType<CrosshairCore>();
        if (crosshairCore == null)
        {
            Debug.LogError("❌ CrosshairCore not found in scene!");
            allComponentsPresent = false;
        }
        else
        {
            Debug.Log("✅ CrosshairCore found");
            
            // Check if it has required components
            var playerLocking = crosshairCore.GetComponent<PlayerLocking>();
            var playerShooting = crosshairCore.GetComponent<PlayerShooting>();
            
            if (playerLocking == null)
            {
                Debug.LogError("❌ PlayerLocking component missing from CrosshairCore!");
                allComponentsPresent = false;
            }
            else
            {
                Debug.Log("✅ PlayerLocking found");
            }
            
            if (playerShooting == null)
            {
                Debug.LogError("❌ PlayerShooting component missing from CrosshairCore!");
                allComponentsPresent = false;
            }
            else
            {
                Debug.Log("✅ PlayerShooting found");
            }
        }
        
        if (allComponentsPresent)
        {
            Debug.Log("🟢 ALL REQUIRED COMPONENTS PRESENT!");
        }
        else
        {
            Debug.LogError("🔴 MISSING COMPONENTS DETECTED!");
        }
    }
    
    private void SetupProjectileManager()
    {
        Debug.Log("🛠️ Setting up ProjectileManager...");
        
        GameObject managerGO = GameObject.Find("ProjectileManager");
        if (managerGO == null)
        {
            if (projectileManagerPrefab != null)
            {
                managerGO = Instantiate(projectileManagerPrefab);
                managerGO.name = "ProjectileManager";
            }
            else
            {
                managerGO = new GameObject("ProjectileManager");
                managerGO.AddComponent<ProjectileManager>();
            }
            
            Debug.Log("✅ ProjectileManager GameObject created");
        }
        else
        {
            // Add component if missing
            if (managerGO.GetComponent<ProjectileManager>() == null)
            {
                managerGO.AddComponent<ProjectileManager>();
                Debug.Log("✅ ProjectileManager component added");
            }
        }
    }
    
    private void SetupProjectileSpawner()
    {
        Debug.Log("🛠️ Setting up ProjectileSpawner...");
        
        GameObject spawnerGO = GameObject.Find("ProjectileSpawner");
        if (spawnerGO == null)
        {
            if (projectileSpawnerPrefab != null)
            {
                spawnerGO = Instantiate(projectileSpawnerPrefab);
                spawnerGO.name = "ProjectileSpawner";
            }
            else
            {
                spawnerGO = new GameObject("ProjectileSpawner");
                spawnerGO.AddComponent<ProjectileSpawner>();
            }
            
            Debug.Log("✅ ProjectileSpawner GameObject created");
        }
        else
        {
            // Add component if missing
            if (spawnerGO.GetComponent<ProjectileSpawner>() == null)
            {
                spawnerGO.AddComponent<ProjectileSpawner>();
                Debug.Log("✅ ProjectileSpawner component added");
            }
        }
    }
    
    private void SetupProjectilePool()
    {
        Debug.Log("🛠️ Setting up ProjectilePool...");
        
        GameObject poolGO = GameObject.Find("ProjectilePool");
        if (poolGO == null)
        {
            if (projectilePoolPrefab != null)
            {
                poolGO = Instantiate(projectilePoolPrefab);
                poolGO.name = "ProjectilePool";
            }
            else
            {
                poolGO = new GameObject("ProjectilePool");
                poolGO.AddComponent<ProjectilePool>();
            }
            
            Debug.Log("✅ ProjectilePool GameObject created");
        }
        else
        {
            // Add component if missing
            if (poolGO.GetComponent<ProjectilePool>() == null)
            {
                poolGO.AddComponent<ProjectilePool>();
                Debug.Log("✅ ProjectilePool component added");
            }
        }
    }
    
    private void SetupProjectileEffectManager()
    {
        Debug.Log("🛠️ Setting up ProjectileEffectManager...");
        
        GameObject effectGO = GameObject.Find("ProjectileEffectManager");
        if (effectGO == null)
        {
            effectGO = new GameObject("ProjectileEffectManager");
            effectGO.AddComponent<ProjectileEffectManager>();
            Debug.Log("✅ ProjectileEffectManager GameObject created");
        }
        else
        {
            // Add component if missing
            if (effectGO.GetComponent<ProjectileEffectManager>() == null)
            {
                effectGO.AddComponent<ProjectileEffectManager>();
                Debug.Log("✅ ProjectileEffectManager component added");
            }
        }
    }
    
    [ContextMenu("Create All Missing Components")]
    public void CreateAllMissingComponents()
    {
        autoSetupMissingComponents = true;
        CheckSceneSetup();
    }
    
    [ContextMenu("List All GameObjects in Scene")]
    public void ListAllGameObjects()
    {
        Debug.Log("📋 All GameObjects in Scene:");
        
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.transform.parent == null) // Only root objects
            {
                Debug.Log($"  - {obj.name}");
                
                // Check for projectile-related components
                var components = obj.GetComponents<MonoBehaviour>();
                foreach (var comp in components)
                {
                    if (comp != null && comp.GetType().Namespace != null && 
                        (comp.GetType().Namespace.Contains("BTR") || comp.GetType().Namespace.Contains("Projectile")))
                    {
                        Debug.Log($"    └─ {comp.GetType().Name}");
                    }
                }
            }
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔧 SCENE SETUP CHECKER", GUI.skin.label);
        
        if (GUILayout.Button("🔍 CHECK SCENE SETUP"))
        {
            CheckSceneSetup();
        }
        
        if (GUILayout.Button("🛠️ CREATE MISSING COMPONENTS"))
        {
            CreateAllMissingComponents();
        }
        
        if (GUILayout.Button("📋 LIST ALL GAMEOBJECTS"))
        {
            ListAllGameObjects();
        }
        
        GUILayout.Space(10);
        
        GUILayout.Label("Auto Setup:");
        autoSetupMissingComponents = GUILayout.Toggle(autoSetupMissingComponents, "Auto Setup Missing");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
