# November

**Nov. 6**

Upgraded to newest 2020 LTS

Had to remove <PERSON><PERSON> / <PERSON><PERSON> LOD

**Nov. 10**

Didn't I add a pause button? What is it / why isn't it working??

Would like to change rotation - make it more elegant!

Went back to DOTWEEN Method, but using adjustments on currentY not to exceed 360

Seems to work? But final rotation is never precise 0, 180, 90, etc.

Need to fix that in long run!

Working on Lock on with rotation as well - checking it works properly

Need to write up a breakdown of Enemy Setup

Need to do so for music and other pipelines as well

Using Greybox - Player Movement Testing to try out lock on and rotation

Looked through some Joost Building scripts for building movements

Nov. 11

Worked on some enemies and arrangements mostly

Upload probably started to occur as well

Nov. 12

Implement a rough version of character into Dreamteck - Forever - Collider Run

Looks promising

Nov. 13

Looking for into Forever implementation

Can use .NET seed to have consistent randomization - could I use this?

Public Follow method in Collider Run can be used to start movement

 Nov. 14

Played with some of the Collider Run, using bullets doesnt seem to go anywhere

Making some enemy types, need to try integrating them into a level format

Thinking about best way to construct their movement

Wave system instantiates them

They move toward the player

They then move in a particular pattern form ?

Happens X times before they fly away

Try Behaviour Designer + other integrations

Nov. 15

Behaviour Designer - Tactical - Attack for basic instantiate / attack

Nov. 19

Trying to bring A* into the game - some success. Need a proper full tutorial on integration

Moving example prefabs brought in can work but Im not sure WHY its working lol

seems like it can move in 3d to some degree?

get this working then get Behaviour Designer involved as well

Nov. 20

Got the A* working - looking to integrate with Behaviour Designer

Working for some integrations! Need to plan out this whole thing

Not moving with scene like it should

Need to write out full enemy tech stack

UltimateWaveSpawner putting out enemies

Behavior Designer + A* Pathfinder handle movement

Behavior Designer / Sensor Toolkit for attacking?

Nov. 21

Behavior Designer tutorials and different integrations

It's coming along! Also posted to Slack!

SWS throwing lots of errors in project - DoTWEEN, and others

Need to find the fix for that

**Nov. 28th**

Figured out SWS / DOTween / BD errors thrown

Working on integration path

Brought prefab ships into project

Need Ultiamte SPawner to spawn these as children on A* nav mesh

Add script to object that make them child of proper object?

Trying this

MakeChildOfEnemyPlane script on enemies

Maybe need to add local space after it becomes child?

Not sure why local space is having issues besides the obvious (looks like its not on the mesh)

- ask how to spawn things on a moving mesh from A* folks?

ERROR when NOT Moving as well, maybe moving isnt the issue? **look up A* things for spawn points**

**Nov. 29**

An enemy spawned as child of nothing works fine on nav mesh - previous thoughts are not the issue?

Error

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.Start () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:45)

Might need pooling system for this to work? LOOKS RIGHT!

Use a wave spawner with pooling

New Pooling system in Unity - worth trying? Need to switch versions.....

[https://thegamedev.guru/unity-cpu-performance/object-pooling/#how-to-use-the-new-object-pooling-api-in-unity-2021](https://thegamedev.guru/unity-cpu-performance/object-pooling/#how-to-use-the-new-object-pooling-api-in-unity-2021)

or do basic pooling tutorial

[https://learn.unity.com/tutorial/introduction-to-object-pooling#5ff8d015edbc2a002063971c](https://learn.unity.com/tutorial/introduction-to-object-pooling#5ff8d015edbc2a002063971c)

Then tie the ultimate spawner instantiate to my system

Also see support email response