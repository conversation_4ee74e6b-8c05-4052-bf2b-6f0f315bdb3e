# Jan 29

Outline of key things to fix in Level Test 4 / in General 

Not in order of importance

- Chronos time control no longer working
- Bullets dont hits targets / loop in circles
- <PERSON><PERSON><PERSON> in Crosshair class break everything - tied to <PERSON><PERSON><PERSON>
    - Unregister for events necessary?
- Wave Spawner not working correctly
    - Tie this to muscial cue / viz fx once working
- Bullet visibility still a big issue
- Locking on to enemy is still difficult
    - <PERSON> needs to see throguh certain objects?
    - Set Reticle to be flat and not curve with camera?
- A* Graph or enemies have problems when moving
    - Post on forum about this?