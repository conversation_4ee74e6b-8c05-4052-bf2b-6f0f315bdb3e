# Pitching / Concept

Problem I am solving - comparitive to HOLE i nthe market - INNOVATION?

Bringing the joy of play and interactivity to the power electronic / dance music.

Working in Pop music in the east coast - Folk music, Indie Synth Pop, Rock

Further interested in Experimental music / Innovation - So much more to our interactions with music then guitar / bass / drums

Lots of working in experimental music - but audience is niche

In film, can expand out more. Soundtrack work often more experimental 

But film is static

What if we could interact with that?

Music is a narrative

Interactive music is a guided narrative - this is the key people miss

Just giving them the sounds is not enough

What is a verse in a level? What is a chorus in a level?

<PERSON><PERSON> focuses on Synesthesia - multi sensory experience

Thumper 

**Find your message**

[https://www.youtube.com/watch?v=vH40oLUJ-d8&list=WL&index=2&t=4s](https://www.youtube.com/watch?v=vH40oLUJ-d8&list=WL&index=2&t=4s)

Exercise:

What are the emotions you want the player to feel?

- fun, nostaligic - too generic and shallow
- skeptical of people around you, infuriated, alive, - use richer desc. like this

How would you describe the experience of playing the game?

- first feeling of being in love, awkwardness of being a teenage, reminded of regrets - rich desc.s

What makes your game different, REALLY?

- what did you create that is totally uniquely yours? SO important

Can you describe your game in three words

- dont use 3d, 3d, satifying, fun, adventure, rougelike, unique

What are you most proud of about the game you're making?

- good jumping off point for what the message is?

Why were you motivated to make this game in the first place?

Keep asking "Why?"

- keep asking about each layer or element - why is that interesting?

IN A TRAILER

![Untitled](Pitching%20Concept%207d6629f26be94352858551d9983d2f98/Untitled.png)

**TALK TAKEAWAYS**

Messaging is a key part of the process

- one of the first things you do

Messaging simplifies by providing constraints and focus

- makes everything else easier too!

Always refer back to your message, it is your guide

Workshop your message!

Show people video of your game early!

- will show if people understand what is going on!

Coordinate your message and your videos! More impactful together 

Framework

**What is the product?**
Start Strong
Show the potential of your passion
You have 30 seconds to do that

**What is the problem?**
What is the unwelcome or harmful or frustrating
situation that you want to correct or address
with your online experience?

**What’s the solution?**

What is your vision and value proposition? (What
should customers believe about what will be
experienced or delivered?)
Why is it right?

**Pitch Perfect Checklist**

1. **Project**
    1.  Potential and Passion - roughly 30 seconds - passion - why you're obsessed
    2. What does this mean exactly? Example? VS the next section
    3. I am developing a currently untitled rhythm action game focused on audio-reactive visuals and movement. Bridging the aesthetics of VJ culture and 80s/90s CG with video games, players will traverse ancient cyberspace to discover worlds once imagined to be our digital futures. Through the framework of retro visions of technology, I want the player to be able to examine their own relationship with technology and how they envision the future. By using action gameplay strongly tied to musical rhythms, players will experience a fast-paced flow state and deeper immersion. Level pacing that coincides with the structures and improvisation of DJ mixing or electronic music will create an interactive dance club rush of highs and lows.
    
    Working on a game that transports players into frenetic, musical flow state action. 
    
    Power of dancing in a nightclub with the fun of musical discovery and performance
    
    Working on a rhythmic action game where you navigate  
    
    You may have experienced a flow state at some point — that sense of fluidity between your body and mind, where you are totally absorbed by and deeply focused on something, beyond the point of distraction. Time feels like it has slowed down. Your senses are heightened. You are at one with the task at hand, as action and awareness sync to create an effortless momentum. Some people describe this feeling as being “in the zone.” This is the flow state and it’s accessible to everyone, whether you’re engaged in a physical activity, a creative pursuit, or even a simple day-to-day task
    
2. **Purpose** 
    1. 140 characters or a dozen words - the purpose of the project in relation to why youre here today 
    2. The pandemic has made a huge impact on the music industry - No touring, 
        
        We're seeing that come back slowly, but in depths of this, wondered about interactions with music
        
        No live bands, no dance clubs, limited ways to interact with music by yourself
        
        Pandemic disrupted our lives - Threw off a collective flow state, on a low level
        
        We want to feel centered - in control and in tune with the world around us
        
        Rhythm and dance music - deeply felt - personal experience - but linear
        
        What if we can control, compose, our actions were deeply intertwined with music?
        
        Play/Creativity mixed with music
        
        Am I just making the case for video games in general? Why music / video intertwined?
        
    3. TATE: how music is difficult to approach and people get locked out, but all humans make music across cultures, but sometimes we feel like we’re not allowed to participate, but the game puts you right into the play of music which is the most human part of music.
    In this case, the big context is that “lots of people are locked out of music, but it’s so inherently human” (and I want to fix that)
    
3. **The team** 
    1. What’s your cred?
    2. Solo dev, how to fashion? 
        1. Highlight my skills - point to people with other skills for several key parts
    
    1. Performing and producing professionally music for 10 years
    2. Computer Science education mixed with musical knowledge - understand the systems behind musical structure and can see how to integrate them 
    3. Design education - understanding of design systems and coherence to bring the project together
    
4. **The Problem** 
    1. Solve something real - What is the unwelcome or harmful or frustrating
    situation that you want to correct or address with your online experience
    2. Music is powerful, deeply felt, but often straightforward experience
        
        linear experience - want to bring the play i feel to
        
        Dance music - energy
        
        Actions - context of the game world and the musical interaction matching up
        
        Power of linear form with element of play and creativity rhythmically
        
        Systems around music and systems around game design very fruitful for creating deeper emotional connection  and and creativity
        
        Instrument and jamming in different then narrative and pumping flow of a game, introduce stakes
        
        Interaction and deepen the experience we have with music
        
    
    c.  Music is often straightforward experience - linear, even in gameplay 
    
    Bring a sense of play and composition to the energy of dance music?
    
    There is a language to music to be explored through game design and interacitvity
    
5. **The Solution** 
    1. What and how is this the best? What is your vision and value proposition? (What
    should customers believe about what will be experienced or delivered?) Why is it right?
    2.  
6. **Why now?** 
    1. Timing validation and competitive judgment
    2. How does your offering coincide with market dynamics of today? What is going on in your realm that makes this so perfectly timed? Why is no one else doing this? Why can’t anyone else do this? BRAG lol
    3. How will the world change with your product in it?
    4. Reference competitive products or landscape here? More general?
    5. OPPORTUNITY - Music games and Innovative iterations on the formula are bigger then ever
    6. It's not just guitar hero / rock band anymore 
    7. Beat Saber, Thumper, Rhythm Doctor, Crypt of the Necrodancer (and it's Nintendo / Zelda collab)
7. **Business Model** 
    1. What have you done so far and what will you do to drive attention?
    2. Who is your ideal customer? What will they be doing with your product and for how long, hopefully? (opportunity) **Who understands what?** What is your market size?
    3. How much to be said in a pitch meeting here? Defining Concept / Prototyping at this stage
8. **Numbers** 
    1. 3-5 years of stuff: projections, expenses, income
    2. How to navigate this at this stage?
9. **Return** 
    1. Expectations
        1. For grants - return for them would be profile
        2. IMPACT - who is their constituency
10. **The ask** 
    1. What do you want? What will you do with it? Did you ask?

Tate Advice on problem / concept

I feel yours about how music is difficult to approach and people get locked out, but all humans make music across cultures, but sometimes we feel like we’re not allowed to participate, but the game puts you right into the play of music which is the most human part of music.
In this case, the big context is that “lots of people are locked out of music, but it’s so inherently human” (and I want to fix that)

Who’s starting the start-up?
¡Why are you solving the problem?
¡Why are you approaching it this
way?
Why are you inspired to pursue
this?

What should the client or audience expect from all the work you’re doing?
Five points for five years:
- Product growth
- Revenue
- Cost
- Margin
- Growth in customer base (acquisition)

It should all lead to…
Example: In five years, one million people will be at home horticulturalists and no longer strapped to cacti or fake trees. Oxygen levels will go up. Stress levels will go down. Greener spaces in green 
space.

Know your model
- What is your business or revenue model?
- Who is paying the bills?
- What is the competitive landscape?
- Are you a premium or budget product?
- Who else is doing what you’re doing? How are you doing it better, then?
- Who is close enough that they might grab your territory before you?
- How does the market validate what you are doing?

Attention! Attention!
- What is your marketing and sales plan?
-  How will you reach your target?
- How are you different from your competitors in this area? (crossover from competitive 
landscape)

Money, Money, Money
- Where are you now?
- What is your sales forecast? Why?
- What is your customer count?
- Expenses? Profits?
- 3 years

So what’s the ask?
- What do you want from the panel?
- Why?
- What might be their return or “exit strategy?”?
- How much (money or help) for a slower return?
- How much (money or help) for a fast return?

Return
- How will the publisher see return?
- How will this be a profitable investment of time or money for them?
- What is your shared future?

When will we see a return?

- There is nothing wrong with preparing more than one scenario:
- Conservative
   - Estimate costs and revenues
  -  Calculate when revenues will exceed costs
-  Aggressive
   - Fast market penetration due to speed of innovation

Proposed next steps
- What happens now?
- How?

If I leave you with nothing 
else….
- Let me leave you with this
- What you ended up learning or enjoying about this project
- A major observation

To pitch effectively you need to:
• Tell a story
• Create simplicity out of complexity
• Prove validity and credibility
• Instill faith in something yet to be invented
• Make numbers and data interestin

**Extra Session Questions**

**Rapid Fire Contents?**

for comfort

**Slow Flo Contents (Entire Pitch Checklist?)**

actually taking feedback and incorporating it

Katie introduced herself in this sessions as well

Adam Henderson / Agusia has done how to run a business course at Brock

- similar to this

Mike Waldron - had done innovate Ottawa - 10 weeks - similar structure - more just apps and businesses wants to be uber 

Dont necessarily need to follow this order

Ask is at the end but could be in beginnign as well!

ITs about how they would feel giving you their money 

WHy now is SO important!!

opportunistic but IMPORTANT