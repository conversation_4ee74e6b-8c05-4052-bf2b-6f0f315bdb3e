%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8478557228505220914
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 1
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.528
  Downscaling:
    m_OverrideState: 1
    m_Value: 7
  BlockSize:
    m_OverrideState: 1
    m_Value: 2
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0.237
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 2.38
  KeyframeResetRate:
    m_OverrideState: 1
    m_Value: 0
  MotionVectorCorruption:
    m_OverrideState: 1
    m_Value: 0
  ErrorAccumulation:
    m_OverrideState: 1
    m_Value: 0
  DCTCorruption:
    m_OverrideState: 1
    m_Value: 0
  ChromaCorruption:
    m_OverrideState: 1
    m_Value: 0
  MotionAmplification:
    m_OverrideState: 0
    m_Value: 3
  MotionThreshold:
    m_OverrideState: 0
    m_Value: 0.001
  CameraObjectMotionBalance:
    m_OverrideState: 0
    m_Value: 0.3
  MotionSmoothing:
    m_OverrideState: 0
    m_Value: 0.1
  TrailIntensity:
    m_OverrideState: 0
    m_Value: 2
  TrailSmoothness:
    m_OverrideState: 0
    m_Value: 0
  TrailPersistence:
    m_OverrideState: 0
    m_Value: 0.8
  FlowSpread:
    m_OverrideState: 0
    m_Value: 2
  CorruptionMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  GlitchTransition:
    m_OverrideState: 0
    m_Value: 0
  FeedbackIntensity:
    m_OverrideState: 0
    m_Value: 0
  MultiScaleCorruption:
    m_OverrideState: 0
    m_Value: 0
  JPEGQuality:
    m_OverrideState: 1
    m_Value: 100
  LuminanceQuantization:
    m_OverrideState: 1
    m_Value: 0
  ChrominanceQuantization:
    m_OverrideState: 1
    m_Value: 0
  ChromaSubsampling:
    m_OverrideState: 1
    m_Value: 0
  RingingArtifacts:
    m_OverrideState: 1
    m_Value: 0
  MosquitoNoise:
    m_OverrideState: 1
    m_Value: 0
  EdgeSensitivity:
    m_OverrideState: 1
    m_Value: 0.5
  NoiseTransparency:
    m_OverrideState: 0
    m_Value: 0.05
  MaxNoiseBrightness:
    m_OverrideState: 0
    m_Value: 0.9
  BrightnessThreshold:
    m_OverrideState: 0
    m_Value: 0.7
  BrightAreaMasking:
    m_OverrideState: 0
    m_Value: 0.8
  Oversharpening:
    m_OverrideState: 0
    m_Value: 0.738
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
  DebugCompressionArtifacts:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-8146237820468282394
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 0
  preset:
    m_OverrideState: 1
    m_Value: 3
  mode:
    m_OverrideState: 0
    m_Value: 1
  renderingPath:
    m_OverrideState: 0
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 3
  deinterleaving:
    m_OverrideState: 0
    m_Value: 0
  resolution:
    m_OverrideState: 0
    m_Value: 0
  noiseType:
    m_OverrideState: 0
    m_Value: 0
  debugMode:
    m_OverrideState: 0
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 1
  maxRadiusPixels:
    m_OverrideState: 0
    m_Value: 256
  bias:
    m_OverrideState: 0
    m_Value: 0.05
  intensity:
    m_OverrideState: 0
    m_Value: 1
  useMultiBounce:
    m_OverrideState: 0
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 0
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 0
    m_Value: 0
  maxDistance:
    m_OverrideState: 0
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 0
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 0
    m_Value: 2
  baseColor:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 0
    m_Value: 0
  varianceClipping:
    m_OverrideState: 0
    m_Value: 1
  blurType:
    m_OverrideState: 0
    m_Value: 1
  sharpness:
    m_OverrideState: 0
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 0
    m_Value: 0
  saturation:
    m_OverrideState: 0
    m_Value: 4
  brightnessMask:
    m_OverrideState: 0
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &-7940629632404280864
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 558a8e2b6826cf840aae193990ba9f2e, type: 3}
  m_Name: ShadowsMidtonesHighlights
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {x: 0.9856421, y: 0.85393196, z: 1, w: -0.15041965}
  midtones:
    m_OverrideState: 1
    m_Value: {x: 1, y: 0.9992213, z: 0.98734874, w: 0.14121959}
  highlights:
    m_OverrideState: 1
    m_Value: {x: 0.985553, y: 0.9677293, z: 1, w: 0.27450806}
  shadowsStart:
    m_OverrideState: 0
    m_Value: 0
  shadowsEnd:
    m_OverrideState: 0
    m_Value: 0.3
  highlightsStart:
    m_OverrideState: 0
    m_Value: 0.55
  highlightsEnd:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-7265451847167681544
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  gaussianStart:
    m_OverrideState: 0
    m_Value: 10
  gaussianEnd:
    m_OverrideState: 0
    m_Value: 30
  gaussianMaxRadius:
    m_OverrideState: 0
    m_Value: 1
  highQualitySampling:
    m_OverrideState: 0
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 21.37
  aperture:
    m_OverrideState: 1
    m_Value: 2.8
  focalLength:
    m_OverrideState: 1
    m_Value: 49
  bladeCount:
    m_OverrideState: 0
    m_Value: 8
  bladeCurvature:
    m_OverrideState: 0
    m_Value: 1
  bladeRotation:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-6806436588196937261
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b447969f02741db89b76840f68e7812, type: 3}
  m_Name: DistortVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0.15
  m_Value:
    m_OverrideState: 1
    m_Value: 0.013
  m_Tiling:
    m_OverrideState: 1
    m_Value: 0.787
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Motion:
    m_OverrideState: 1
    m_Value: 0.037
--- !u!114 &-6290540818034801327
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 1
  blueShadows:
    m_OverrideState: 1
    m_Value: 0.527
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0.526
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0.558
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-5513912215843381144
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.2
  smoothness:
    m_OverrideState: 1
    m_Value: 1
  rounded:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-5461088535186388473
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.483
--- !u!114 &-5438285271087376767
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.198
  OnlyStenciled:
    m_OverrideState: 0
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.326
  Downscaling:
    m_OverrideState: 1
    m_Value: 4
  BlockSize:
    m_OverrideState: 1
    m_Value: 0
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.2
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 1
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 1.49
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-4652810265619861845
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 0
    m_Value: 1
  OnlyStenciled:
    m_OverrideState: 0
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 0
    m_Value: 0.3
  Downscaling:
    m_OverrideState: 0
    m_Value: 4
  BlockSize:
    m_OverrideState: 0
    m_Value: 1
  DontCrunchSkybox:
    m_OverrideState: 0
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 0
    m_Value: 0.25
  ReprojectBaseRerollSpeed:
    m_OverrideState: 0
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 0
    m_Value: 2
  KeyframeResetRate:
    m_OverrideState: 0
    m_Value: 0
  MotionVectorCorruption:
    m_OverrideState: 0
    m_Value: 0
  ErrorAccumulation:
    m_OverrideState: 0
    m_Value: 0
  DCTCorruption:
    m_OverrideState: 0
    m_Value: 0
  ChromaCorruption:
    m_OverrideState: 0
    m_Value: 0
  MotionAmplification:
    m_OverrideState: 0
    m_Value: 3
  MotionThreshold:
    m_OverrideState: 0
    m_Value: 0.001
  CameraObjectMotionBalance:
    m_OverrideState: 0
    m_Value: 0.3
  MotionSmoothing:
    m_OverrideState: 0
    m_Value: 0.1
  TrailIntensity:
    m_OverrideState: 0
    m_Value: 2
  TrailSmoothness:
    m_OverrideState: 0
    m_Value: 0
  TrailPersistence:
    m_OverrideState: 0
    m_Value: 0.8
  FlowSpread:
    m_OverrideState: 0
    m_Value: 2
  CorruptionMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  GlitchTransition:
    m_OverrideState: 0
    m_Value: 0
  FeedbackIntensity:
    m_OverrideState: 0
    m_Value: 0
  MultiScaleCorruption:
    m_OverrideState: 0
    m_Value: 0
  JPEGQuality:
    m_OverrideState: 0
    m_Value: 100
  LuminanceQuantization:
    m_OverrideState: 0
    m_Value: 0
  ChrominanceQuantization:
    m_OverrideState: 0
    m_Value: 0
  ChromaSubsampling:
    m_OverrideState: 0
    m_Value: 0
  RingingArtifacts:
    m_OverrideState: 0
    m_Value: 0
  MosquitoNoise:
    m_OverrideState: 0
    m_Value: 0
  EdgeSensitivity:
    m_OverrideState: 0
    m_Value: 0.5
  NoiseTransparency:
    m_OverrideState: 0
    m_Value: 0.05
  MaxNoiseBrightness:
    m_OverrideState: 0
    m_Value: 0.9
  BrightnessThreshold:
    m_OverrideState: 0
    m_Value: 0.7
  BrightAreaMasking:
    m_OverrideState: 0
    m_Value: 0.8
  Oversharpening:
    m_OverrideState: 0
    m_Value: 0.3
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
  DebugCompressionArtifacts:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-3603400311350254749
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.25
  OnlyStenciled:
    m_OverrideState: 0
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.199
  Downscaling:
    m_OverrideState: 1
    m_Value: 8
  BlockSize:
    m_OverrideState: 1
    m_Value: 2
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 1
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0.675
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 17.2
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 5
  KeyframeResetRate:
    m_OverrideState: 1
    m_Value: 0.059
  MotionVectorCorruption:
    m_OverrideState: 1
    m_Value: 0
  ErrorAccumulation:
    m_OverrideState: 1
    m_Value: 0
  DCTCorruption:
    m_OverrideState: 1
    m_Value: 0
  ChromaCorruption:
    m_OverrideState: 1
    m_Value: 0
  MotionAmplification:
    m_OverrideState: 0
    m_Value: 3
  MotionThreshold:
    m_OverrideState: 0
    m_Value: 0.001
  CameraObjectMotionBalance:
    m_OverrideState: 0
    m_Value: 0.3
  MotionSmoothing:
    m_OverrideState: 0
    m_Value: 0.1
  TrailIntensity:
    m_OverrideState: 0
    m_Value: 2
  TrailSmoothness:
    m_OverrideState: 1
    m_Value: 0.909
  TrailPersistence:
    m_OverrideState: 0
    m_Value: 0.8
  FlowSpread:
    m_OverrideState: 0
    m_Value: 2
  CorruptionMask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  GlitchTransition:
    m_OverrideState: 1
    m_Value: 0
  FeedbackIntensity:
    m_OverrideState: 1
    m_Value: 0.671
  MultiScaleCorruption:
    m_OverrideState: 1
    m_Value: 0.595
  JPEGQuality:
    m_OverrideState: 1
    m_Value: 47.4
  LuminanceQuantization:
    m_OverrideState: 1
    m_Value: 1.252
  ChrominanceQuantization:
    m_OverrideState: 1
    m_Value: 0.755
  ChromaSubsampling:
    m_OverrideState: 1
    m_Value: 1
  RingingArtifacts:
    m_OverrideState: 1
    m_Value: 0.734
  MosquitoNoise:
    m_OverrideState: 1
    m_Value: 0.619
  EdgeSensitivity:
    m_OverrideState: 1
    m_Value: 0.467
  NoiseTransparency:
    m_OverrideState: 1
    m_Value: 0.258
  MaxNoiseBrightness:
    m_OverrideState: 1
    m_Value: 0.924
  BrightnessThreshold:
    m_OverrideState: 1
    m_Value: 0.763
  BrightAreaMasking:
    m_OverrideState: 1
    m_Value: 0.748
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.753
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
  DebugCompressionArtifacts:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-444891487255627357
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2aebac967aa993140ab1982d43b8450a, type: 3}
  m_Name: GlitchVol
  m_EditorClassIdentifier: 
  active: 1
  _weight:
    m_OverrideState: 1
    m_Value: 0.135
  _power:
    m_OverrideState: 1
    m_Value: 0.246
  _scale:
    m_OverrideState: 1
    m_Value: 0.95
  _dispersion:
    m_OverrideState: 1
    m_Value: 0.15
  _period:
    m_OverrideState: 1
    m_Value: 0
  _color:
    m_OverrideState: 0
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0.773, g: 0.693, b: 1, a: 1}
        key1: {r: 0, g: 0.98, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0.773, g: 0.693, b: 1, a: 1}
      - {r: 0.7480645, g: 0.7022581, b: 1, a: 1}
      - {r: 0.72312903, g: 0.71151614, b: 1, a: 1}
      - {r: 0.69819355, g: 0.72077423, b: 1, a: 1}
      - {r: 0.67325807, g: 0.73003227, b: 1, a: 1}
      - {r: 0.6483226, g: 0.73929036, b: 1, a: 1}
      - {r: 0.6233871, g: 0.7485484, b: 1, a: 1}
      - {r: 0.5984516, g: 0.7578065, b: 1, a: 1}
      - {r: 0.57351613, g: 0.7670645, b: 1, a: 1}
      - {r: 0.54858065, g: 0.7763226, b: 1, a: 1}
      - {r: 0.52364516, g: 0.78558064, b: 1, a: 1}
      - {r: 0.49870968, g: 0.7948387, b: 1, a: 1}
      - {r: 0.4737742, g: 0.8040968, b: 1, a: 1}
      - {r: 0.4488387, g: 0.81335485, b: 1, a: 1}
      - {r: 0.42390323, g: 0.8226129, b: 1, a: 1}
      - {r: 0.39896774, g: 0.831871, b: 1, a: 1}
      - {r: 0.37403226, g: 0.84112906, b: 1, a: 1}
      - {r: 0.34909678, g: 0.8503871, b: 1, a: 1}
      - {r: 0.3241613, g: 0.8596452, b: 1, a: 1}
      - {r: 0.2992258, g: 0.8689033, b: 1, a: 1}
      - {r: 0.27429032, g: 0.8781613, b: 1, a: 1}
      - {r: 0.24935484, g: 0.88741934, b: 1, a: 1}
      - {r: 0.22441936, g: 0.89667743, b: 1, a: 1}
      - {r: 0.19948387, g: 0.9059355, b: 1, a: 1}
      - {r: 0.17454839, g: 0.91519356, b: 1, a: 1}
      - {r: 0.1496129, g: 0.92445165, b: 1, a: 1}
      - {r: 0.12467742, g: 0.9337097, b: 1, a: 1}
      - {r: 0.099741936, g: 0.9429678, b: 1, a: 1}
      - {r: 0.07480645, g: 0.9522258, b: 1, a: 1}
      - {r: 0.049870968, g: 0.9614839, b: 1, a: 1}
      - {r: 0.024935484, g: 0.970742, b: 1, a: 1}
      - {r: 0, g: 0.98, b: 1, a: 1}
  _chaotic:
    m_OverrideState: 0
    m_Value: 0.085
  _noLockNoise:
    m_OverrideState: 1
    m_Value: 1
  _density:
    m_OverrideState: 1
    m_Value: 1
  _lock:
    m_OverrideState: 1
    m_Value: 0.274
  _sharpen:
    m_OverrideState: 1
    m_Value: 0.474
  _crush:
    m_OverrideState: 1
    m_Value: 0.782
  _grid:
    m_OverrideState: 1
    m_Value: 0.502
  _bleed:
    m_OverrideState: 0
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 28527
        atime2: 65535
        atime3: 65535
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 3
      _pixels:
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0.074106365}
      - {r: 1, g: 1, b: 1, a: 0.14821273}
      - {r: 1, g: 1, b: 1, a: 0.2223191}
      - {r: 1, g: 1, b: 1, a: 0.29642546}
      - {r: 1, g: 1, b: 1, a: 0.37053183}
      - {r: 1, g: 1, b: 1, a: 0.4446382}
      - {r: 1, g: 1, b: 1, a: 0.5187445}
      - {r: 1, g: 1, b: 1, a: 0.5928509}
      - {r: 1, g: 1, b: 1, a: 0.66695726}
      - {r: 1, g: 1, b: 1, a: 0.74106365}
      - {r: 1, g: 1, b: 1, a: 0.81517}
      - {r: 1, g: 1, b: 1, a: 0.8892764}
      - {r: 1, g: 1, b: 1, a: 0.96338266}
      - {r: 1, g: 1, b: 1, a: 0.9711022}
      - {r: 1, g: 1, b: 1, a: 0.9139785}
      - {r: 1, g: 1, b: 1, a: 0.85685486}
      - {r: 1, g: 1, b: 1, a: 0.79973114}
      - {r: 1, g: 1, b: 1, a: 0.7426076}
      - {r: 1, g: 1, b: 1, a: 0.6854839}
      - {r: 1, g: 1, b: 1, a: 0.6283603}
      - {r: 1, g: 1, b: 1, a: 0.5712365}
      - {r: 1, g: 1, b: 1, a: 0.51411295}
      - {r: 1, g: 1, b: 1, a: 0.45698923}
      - {r: 1, g: 1, b: 1, a: 0.39986563}
      - {r: 1, g: 1, b: 1, a: 0.3427419}
      - {r: 1, g: 1, b: 1, a: 0.28561836}
      - {r: 1, g: 1, b: 1, a: 0.22849464}
      - {r: 1, g: 1, b: 1, a: 0.17137098}
      - {r: 1, g: 1, b: 1, a: 0.11424726}
      - {r: 1, g: 1, b: 1, a: 0.05712372}
      - {r: 1, g: 1, b: 1, a: 0}
  _screen:
    m_OverrideState: 1
    m_Value: 0
  _noLock:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-421021338226622681
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccf1aba9553839d41ae37dd52e9ebcce, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 0
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.08
  clamp:
    m_OverrideState: 0
    m_Value: 0.2
--- !u!114 &-275684943794088489
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f73d2631a9741743935bb8e4363a105, type: 3}
  m_Name: MoshVol
  m_EditorClassIdentifier: 
  active: 0
  m_Fade:
    m_OverrideState: 1
    m_Value: 0.391
  m_Strain:
    m_OverrideState: 1
    m_Value: 0.485
  m_Samples:
    m_OverrideState: 0
    m_Value: 1
  m_Angle:
    m_OverrideState: 0
    m_Value: 0
  m_Flow:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 13.8}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_BlockIntensity:
    m_OverrideState: 1
    m_Value: 0.478
  m_BlockSize:
    m_OverrideState: 1
    m_Value: 128
  m_Retention:
    m_OverrideState: 1
    m_Value: 1
  m_MotionThreshold:
    m_OverrideState: 0
    m_Value: 0
  m_GlitchIntensity:
    m_OverrideState: 0
    m_Value: 0
  m_TimeScale:
    m_OverrideState: 1
    m_Value: 2.97
  m_Compression:
    m_OverrideState: 1
    m_Value: 1
  m_Print:
    m_OverrideState: 1
    m_Value: 0.603
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0.691
  m_Focus:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: Global Volume Profile 1
  m_EditorClassIdentifier: 
  components:
  - {fileID: 5593642501290492971}
  - {fileID: 5283543248074490846}
  - {fileID: -5461088535186388473}
  - {fileID: -7265451847167681544}
  - {fileID: -421021338226622681}
  - {fileID: 1109151089363032151}
  - {fileID: -7940629632404280864}
  - {fileID: -5513912215843381144}
  - {fileID: 1656116689373144328}
  - {fileID: 1635298809904460121}
  - {fileID: -3603400311350254749}
--- !u!114 &95281886288751893
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bba95ee541be4fe3b76c758f1424e975, type: 3}
  m_Name: FlowVol
  m_EditorClassIdentifier: 
  active: 1
  m_Fade:
    m_OverrideState: 1
    m_Value: 0.134
  m_Strain:
    m_OverrideState: 1
    m_Value: 0.257
  m_Samples:
    m_OverrideState: 1
    m_Value: 4
  m_Angle:
    m_OverrideState: 1
    m_Value: -0.09
  m_Flow:
    m_OverrideState: 1
    m_Value: {x: 0, y: 1.42, z: -0.66}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0.27058825}
  m_Print:
    m_OverrideState: 1
    m_Value: 0.779
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0.327
  m_Focus:
    m_OverrideState: 1
    m_Value: 0
  m_Fps:
    m_OverrideState: 0
    m_Value: 60
--- !u!114 &191965508559115626
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.63
  blur:
    m_OverrideState: 1
    m_Value: 0.583
  edgePreserve:
    m_OverrideState: 1
    m_Value: 0.296
  rangeStart:
    m_OverrideState: 0
    m_Value: 14.4
  rangeEnd:
    m_OverrideState: 0
    m_Value: 8.57
--- !u!114 &1109151089363032151
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: -0.162
  xMultiplier:
    m_OverrideState: 0
    m_Value: 1
  yMultiplier:
    m_OverrideState: 0
    m_Value: 1
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1635298809904460121
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5ba363214cc0a3a40ac4e7626c83881c, type: 3}
  m_Name: BloomOverride
  m_EditorClassIdentifier: 
  active: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  internalBlend:
    m_OverrideState: 1
    m_Value: 0.85
  finalBlend:
    m_OverrideState: 1
    m_Value: 0.02
  bloomMaxIterations:
    m_OverrideState: 1
    m_Value: 6
  thresholdEdge:
    m_OverrideState: 1
    m_Value: 1
  thresholdRange:
    m_OverrideState: 1
    m_Value: 32
  ghostIntensity:
    m_OverrideState: 1
    m_Value: 0.3
  ghostTint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  ghostChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.0213, y: 0, z: 0.032}
  ghostTint1:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
  ghostSpread1:
    m_OverrideState: 1
    m_Value: -0.142
  ghostTint2:
    m_OverrideState: 0
    m_Value: {r: 0.231, g: 0.827, b: 0.384, a: 1}
  ghostSpread2:
    m_OverrideState: 0
    m_Value: 0.1
  ghostTint3:
    m_OverrideState: 0
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread3:
    m_OverrideState: 0
    m_Value: -0.7
  ghostTint4:
    m_OverrideState: 0
    m_Value: {r: 0.094, g: 0.654, b: 0.862, a: 1}
  ghostSpread4:
    m_OverrideState: 0
    m_Value: 0.78
  ghostTint5:
    m_OverrideState: 0
    m_Value: {r: 0.811, g: 0.243, b: 0.678, a: 1}
  ghostSpread5:
    m_OverrideState: 0
    m_Value: 0.23
  ghostTint6:
    m_OverrideState: 0
    m_Value: {r: 0.427, g: 0.792, b: 0.156, a: 1}
  ghostSpread6:
    m_OverrideState: 0
    m_Value: -0.1235
  ghostTint7:
    m_OverrideState: 0
    m_Value: {r: 0.921, g: 0.305, b: 0.058, a: 1}
  ghostSpread7:
    m_OverrideState: 0
    m_Value: 0.53
  ghostTint8:
    m_OverrideState: 0
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread8:
    m_OverrideState: 0
    m_Value: -0.412
  haloIntensity:
    m_OverrideState: 1
    m_Value: 0.3
  haloFisheyeStrength:
    m_OverrideState: 1
    m_Value: 0.5
  haloFisheyeWidth:
    m_OverrideState: 1
    m_Value: 0.4
  haloChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.02314, y: 0, z: 0.04213}
  haloTint:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
--- !u!114 &1656116689373144328
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70afe9e12c7a7ed47911bb608a23a8ff, type: 3}
  m_Name: SplitToning
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {r: 0.10659769, g: 0, b: 0.3490566, a: 1}
  highlights:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  balance:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &3411259592941581741
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c276c365a884f5a4293f9acd7b9ea680, type: 3}
  m_Name: AutoExposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 1
  evMin:
    m_OverrideState: 1
    m_Value: 0
  evMax:
    m_OverrideState: 1
    m_Value: 12
  evCompensation:
    m_OverrideState: 0
    m_Value: 0
  compensationCurveParameter:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 0
  darkToLightSpeed:
    m_OverrideState: 0
    m_Value: 3
  lightToDarkSpeed:
    m_OverrideState: 0
    m_Value: 1
  meteringMaskMode:
    m_OverrideState: 0
    m_Value: 0
  meteringMaskTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  meteringProceduralFalloff:
    m_OverrideState: 0
    m_Value: 2
  renderingMode:
    m_OverrideState: 1
    m_Value: 0
  sampleCount:
    m_OverrideState: 0
    m_Value: 30
  animateSamplePositions:
    m_OverrideState: 0
    m_Value: 0
  response:
    m_OverrideState: 0
    m_Value: 0.03
  clampingEnabled:
    m_OverrideState: 0
    m_Value: 0
  clampingBracket:
    m_OverrideState: 0
    m_Value: 2
--- !u!114 &5015906786889959328
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.15
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0
  Downscaling:
    m_OverrideState: 1
    m_Value: 1
  BlockSize:
    m_OverrideState: 1
    m_Value: 1
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.201
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 1
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0.495
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 20
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 4.22
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &5283543248074490846
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 221518ef91623a7438a71fef23660601, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  temperature:
    m_OverrideState: 1
    m_Value: 0
  tint:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5593642501290492971
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 0
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &8507371917668750682
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 79d2da40ebb77b944bc3b032a724941f, type: 3}
  m_Name: OutlineVolumeSettings
  m_EditorClassIdentifier: 
  active: 0
  outlineColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 0.75, b: 0.8417375, a: 1}
  maximumOutlineThickness:
    m_OverrideState: 1
    m_Value: 0.5
  edgeThreshold:
    m_OverrideState: 1
    m_Value: 0.918
  excludeSkybox:
    m_OverrideState: 1
    m_Value: 0
  resolutionScaling:
    m_OverrideState: 0
    m_Value: 2160
  frameRate:
    m_OverrideState: 0
    m_Value: 6
  depthThickness01:
    m_OverrideState: 0
    m_Value: 1
  depthThreshold:
    m_OverrideState: 0
    m_Value: 1
  depthSoftness:
    m_OverrideState: 0
    m_Value: 0.1
  normalThickness01:
    m_OverrideState: 0
    m_Value: 1
  normalThreshold:
    m_OverrideState: 0
    m_Value: 0.5
  normalSoftness:
    m_OverrideState: 0
    m_Value: 0.5
  lumaThickness01:
    m_OverrideState: 1
    m_Value: 0.7
  lumaThreshold:
    m_OverrideState: 0
    m_Value: 0.2
  lumaSoftness:
    m_OverrideState: 0
    m_Value: 0.1
  colorThickness01:
    m_OverrideState: 0
    m_Value: 1
  colorThreshold:
    m_OverrideState: 0
    m_Value: 0.2
  colorSoftness:
    m_OverrideState: 0
    m_Value: 0.1
  distanceStart:
    m_OverrideState: 1
    m_Value: 0
  distanceEnd:
    m_OverrideState: 1
    m_Value: 500
  nearThickness01:
    m_OverrideState: 0
    m_Value: 1
  farThickness01:
    m_OverrideState: 0
    m_Value: 1
  nearAlpha:
    m_OverrideState: 0
    m_Value: 1
  farAlpha:
    m_OverrideState: 0
    m_Value: 1
  grazingAngleThreshold:
    m_OverrideState: 0
    m_Value: 0.2
  grazingAngleOffset:
    m_OverrideState: 0
    m_Value: 3
  offsetTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  offsetTextureStrength:
    m_OverrideState: 0
    m_Value: 0
  offsetTextureScale:
    m_OverrideState: 0
    m_Value: 3
  offsetTextureSpeed:
    m_OverrideState: 0
    m_Value: 10
  offsetExcludeSkybox:
    m_OverrideState: 0
    m_Value: 1
  paperTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  paperTextureStrength:
    m_OverrideState: 0
    m_Value: 0
  paperTextureScale:
    m_OverrideState: 0
    m_Value: 10
  paperTextureSpeed:
    m_OverrideState: 0
    m_Value: 0
  paperExcludeSkybox:
    m_OverrideState: 0
    m_Value: 1
  blurAmount:
    m_OverrideState: 1
    m_Value: 5
  blurNoiseEnabled:
    m_OverrideState: 0
    m_Value: 0
  previewOutlines:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &8647912701800612948
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5ba363214cc0a3a40ac4e7626c83881c, type: 3}
  m_Name: BloomOverride
  m_EditorClassIdentifier: 
  active: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  internalBlend:
    m_OverrideState: 0
    m_Value: 0.85
  finalBlend:
    m_OverrideState: 0
    m_Value: 0.02
  bloomMaxIterations:
    m_OverrideState: 1
    m_Value: 5
  thresholdEdge:
    m_OverrideState: 1
    m_Value: 70
  thresholdRange:
    m_OverrideState: 1
    m_Value: 32
  ghostIntensity:
    m_OverrideState: 1
    m_Value: 0.4
  ghostTint:
    m_OverrideState: 1
    m_Value: {r: 0.2735849, g: 0.2735849, b: 0.2735849, a: 1}
  ghostChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.0213, y: 0, z: 0.032}
  ghostTint1:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
  ghostSpread1:
    m_OverrideState: 1
    m_Value: -0.142
  ghostTint2:
    m_OverrideState: 0
    m_Value: {r: 0.231, g: 0.827, b: 0.384, a: 1}
  ghostSpread2:
    m_OverrideState: 0
    m_Value: 0.1
  ghostTint3:
    m_OverrideState: 0
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread3:
    m_OverrideState: 0
    m_Value: -0.7
  ghostTint4:
    m_OverrideState: 0
    m_Value: {r: 0.094, g: 0.654, b: 0.862, a: 1}
  ghostSpread4:
    m_OverrideState: 0
    m_Value: 0.78
  ghostTint5:
    m_OverrideState: 0
    m_Value: {r: 0.811, g: 0.243, b: 0.678, a: 1}
  ghostSpread5:
    m_OverrideState: 0
    m_Value: 0.23
  ghostTint6:
    m_OverrideState: 0
    m_Value: {r: 0.427, g: 0.792, b: 0.156, a: 1}
  ghostSpread6:
    m_OverrideState: 0
    m_Value: -0.1235
  ghostTint7:
    m_OverrideState: 0
    m_Value: {r: 0.921, g: 0.305, b: 0.058, a: 1}
  ghostSpread7:
    m_OverrideState: 0
    m_Value: 0.53
  ghostTint8:
    m_OverrideState: 0
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread8:
    m_OverrideState: 0
    m_Value: -0.412
  haloIntensity:
    m_OverrideState: 1
    m_Value: 0.3
  haloFisheyeStrength:
    m_OverrideState: 1
    m_Value: 0.07
  haloFisheyeWidth:
    m_OverrideState: 1
    m_Value: 0.4
  haloChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.02314, y: 0, z: 0.04213}
  haloTint:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
