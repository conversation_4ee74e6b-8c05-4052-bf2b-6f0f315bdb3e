# July 18

<aside>
💡 FSR plugin cause camera to flicker with upside down image occarionally

</aside>

Disabled for now

Looking through curvy examples to see if there’s a better way to define a moving spline

Looking at snake control point / bone problems to see what i may have missed on previous attempts to fix this

New snake is promising, but wildly affected by speed in a way other level parts are not. Unsure why!

<aside>
💡 Speed much faster on new snake!

</aside>

Important to delete uneeded control points before removing from bone offsets list. Otherwise not populated properly, screws up tracking. 

Keeping dolly/spline and model seperate ise actually useful. Scale affects speed, so keeping a regular scale on dolly/spline and altering only the model appears to be better. Need to finalize things a bit more if that’s the approach