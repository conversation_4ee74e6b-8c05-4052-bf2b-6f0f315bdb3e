using UnityEngine;
using Stylo.Epoch;
using BTR.Audio;

namespace BTR.Tools
{
    /// <summary>
    /// Verification script for Epoch integration with FMOD audio system.
    /// Attach to any GameObject to monitor time synchronization.
    /// </summary>
    public class EpochIntegrationVerifier : MonoBehaviour
    {
        [Header("Verification Settings")]
        [SerializeField] private bool enableRealTimeLogging = false;
        [SerializeField] private float logInterval = 1f;

        private float lastLogTime;

        private void Update()
        {
            if (enableRealTimeLogging && UnityEngine.Time.time - lastLogTime >= logInterval)
            {
                VerifyEpochIntegration();
                lastLogTime = UnityEngine.Time.time;
            }
        }

        [ContextMenu("Verify Epoch Integration")]
        public void VerifyEpochIntegration()
        {
            UnityEngine.Debug.Log("=== Epoch Integration Verification ===");

            // Check if EpochTimekeeper exists
            if (EpochTimekeeper.Instance == null)
            {
                UnityEngine.Debug.LogError("❌ EpochTimekeeper.Instance is null - Epoch not initialized");
                return;
            }

            // Check Test clock
            try
            {
                if (EpochTimekeeper.Instance.HasClock("Test"))
                {
                    var testClockState = EpochTimekeeper.Instance.Clock("Test");
                    UnityEngine.Debug.Log($"✅ Test Clock Found - Time Scale: {testClockState.TimeScale:F2}, Time: {testClockState.Time:F2}");
                }
                else
                {
                    UnityEngine.Debug.LogWarning("⚠️ Test clock not found - will be created when needed");
                }
            }
            catch (System.Exception e)
            {
                UnityEngine.Debug.LogWarning($"⚠️ Test clock not available: {e.Message}");
            }

            // Check FMOD Phase 4 systems
            CheckPhase4Systems();

            // Check Unity vs Chronos time
            CheckTimeComparison();

            UnityEngine.Debug.Log("=== Verification Complete ===");
        }

        private void CheckPhase4Systems()
        {
            UnityEngine.Debug.Log("--- Phase 4 Systems Check ---");

            // Phase 4 audio systems have been removed; FMOD and Epoch now handle all audio optimization natively.
            UnityEngine.Debug.Log("Phase 4 audio systems (AudioPerformanceMonitor, AudioLODSystem, AudioMemoryOptimizer) have been removed. FMOD and Epoch now handle all audio optimization natively.");
        }

        private void CheckTimeComparison()
        {
            UnityEngine.Debug.Log("--- Time Comparison ---");

            UnityEngine.Debug.Log($"Unity Time: {UnityEngine.Time.time:F2}, Time Scale: {UnityEngine.Time.timeScale:F2}");

            if (EpochTimekeeper.Instance != null)
            {
                try
                {
                    if (EpochTimekeeper.Instance.HasClock("Test"))
                    {
                        var testClockState = EpochTimekeeper.Instance.Clock("Test");
                        UnityEngine.Debug.Log($"Epoch Test Clock: {testClockState.Time:F2}, Time Scale: {testClockState.TimeScale:F2}");

                        // Check if they're synchronized
                        float timeDiff = Mathf.Abs(UnityEngine.Time.time - testClockState.Time);
                        if (timeDiff < 0.1f)
                        {
                            UnityEngine.Debug.Log("✅ Unity and Epoch time are synchronized");
                        }
                        else
                        {
                            UnityEngine.Debug.LogWarning($"⚠️ Time difference detected: {timeDiff:F3}s");
                        }
                    }
                }
                catch (System.Exception e)
                {
                    UnityEngine.Debug.LogWarning($"⚠️ Could not access Test clock: {e.Message}");
                }
            }
        }

        [ContextMenu("Test Time Manipulation")]
        public void TestTimeManipulation()
        {
            if (EpochTimekeeper.Instance == null)
            {
                UnityEngine.Debug.LogError("❌ EpochTimekeeper not available for testing");
                return;
            }

            UnityEngine.Debug.Log("🧪 Testing Time Manipulation...");

            try
            {
                // Find the Global clock component
                EpochGlobalClock[] globalClocks = FindObjectsOfType<EpochGlobalClock>();
                EpochGlobalClock testClock = null;
                foreach (var clock in globalClocks)
                {
                    if (clock.clockKey == "Global")
                    {
                        testClock = clock;
                        break;
                    }
                }

                if (testClock != null)
                {
                    // Test slow motion
                    UnityEngine.Debug.Log("Setting slow motion (0.5x)...");
                    testClock.LocalTimeScale = 0.5f;

                    // Reset after 2 seconds
                    Invoke(nameof(ResetTimeScale), 2f);
                }
                else
                {
                    UnityEngine.Debug.LogWarning("⚠️ Test clock component not found");
                }
            }
            catch (System.Exception e)
            {
                UnityEngine.Debug.LogError($"❌ Time manipulation test failed: {e.Message}");
            }
        }

        private void ResetTimeScale()
        {
            try
            {
                // Find the Global clock component
                EpochGlobalClock[] globalClocks = FindObjectsOfType<EpochGlobalClock>();
                foreach (var clock in globalClocks)
                {
                    if (clock.clockKey == "Global")
                    {
                        clock.LocalTimeScale = 1f;
                        UnityEngine.Debug.Log("✅ Time scale reset to normal");
                        return;
                    }
                }
                UnityEngine.Debug.LogWarning("⚠️ Global clock component not found for reset");
            }
            catch (System.Exception e)
            {
                UnityEngine.Debug.LogError($"❌ Failed to reset time scale: {e.Message}");
            }
        }
    }
}
