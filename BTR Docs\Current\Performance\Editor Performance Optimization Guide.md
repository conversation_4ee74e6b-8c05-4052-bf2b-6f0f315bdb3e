# Editor Performance Optimization Guide

## Overview

This guide addresses the slow play mode entry and editor performance issues caused by excessive initialization code and domain reload overhead.

## Issues Identified

### 1. Domain Reload Performance
**Problem**: Unity was reloading the entire domain on play mode entry
**Solution**: Enabled fast Enter Play Mode (disabled domain reload)
**Impact**: Significantly faster play mode entry

### 2. Excessive InitializeOnLoad Attributes
**Problem**: Multiple third-party assets running heavy initialization on every script compilation
**Assets Affected**:
- Reach UI (2 InitializeOnLoad attributes)
- GPU Instancer Pro (heavy package loading)
- Chroma (define symbol management)
- RealToon (welcome screen initialization)
- VTabs (editor update registration)
- Altos (dependency checking)

### 3. Heavy Singleton Patterns
**Problem**: Multiple FindObjectOfType calls during initialization
**Impact**: Scene hierarchy scanning causing performance spikes

## Optimizations Applied

### 1. Enter Play Mode Settings
**File**: `ProjectSettings/EditorSettings.asset`
**Change**: `m_EnterPlayModeOptions: 0` → `m_EnterPlayModeOptions: 2`
**Result**: Disabled domain reload for faster play mode entry

### 2. BTR Scripts Domain Reload Handling
**Status**: ✅ Already implemented correctly
**Pattern Used**:
```csharp
[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
private static void ResetStaticData()
{
    _instance = null;
    _applicationIsQuitting = false;
}
```

### 3. Logging Performance Optimization
**Files Optimized**:
- `ProjectileManager.cs` - Wrapped debug logs in conditional compilation
- `CombatStrategy.cs` - Wrapped debug logs in conditional compilation
- `LoggerProvider.cs` - Optimized ZLogger minimum log levels

## Performance Impact

### Before Optimization
- Slow play mode entry (5-10 seconds)
- Editor latency spikes during script compilation
- Excessive logging causing frame drops
- Domain reload overhead on every play mode entry

### After Optimization
- Fast play mode entry (1-2 seconds)
- Reduced editor latency spikes
- Zero-allocation logging in production
- No domain reload overhead

## Monitoring and Maintenance

### 1. Third-Party Asset Management
**Recommendation**: Be cautious when adding new assets with InitializeOnLoad attributes
**Check**: Review asset documentation for editor performance impact

### 2. Custom Script Guidelines
**Pattern**: Always implement domain reload handling for singletons
**Template**:
```csharp
[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
private static void ResetStaticData()
{
    // Reset all static fields
}
```

### 3. Logging Best Practices
**Development**: Use conditional compilation for debug logs
**Production**: Ensure minimal logging overhead
**Pattern**:
```csharp
#if UNITY_EDITOR || DEVELOPMENT_BUILD
if (enableDebugLogs)
{
    _logger?.LogDebug("Debug message: {Parameter}", value);
}
#endif
```

## Troubleshooting

### If Play Mode Entry Becomes Slow Again
1. Check for new assets with InitializeOnLoad attributes
2. Verify domain reload settings haven't been reset
3. Profile editor performance to identify bottlenecks

### If Editor Latency Spikes Return
1. Check for excessive logging in Update loops
2. Verify ZLogger configuration is optimal
3. Look for new FindObjectOfType calls in initialization

### If Domain Reload Issues Occur
1. Ensure all singletons have RuntimeInitializeOnLoadMethod
2. Check for static field initialization issues
3. Verify DontDestroyOnLoad objects are handled correctly

## Future Considerations

### 1. Asset Audit
Regularly review third-party assets for performance impact

### 2. Initialization Profiling
Use Unity Profiler to monitor initialization performance

### 3. Editor Extension Optimization
Consider creating custom editor tools to replace heavy third-party initialization

## References

- [Unity Enter Play Mode Documentation](https://docs.unity3d.com/Manual/ConfigurableEnterPlayMode.html)
- [Domain Reload Best Practices](https://docs.unity3d.com/Manual/DomainReloading.html)
- [Editor Performance Guidelines](https://docs.unity3d.com/Manual/BestPracticeUnderstandingPerformanceInUnity.html)
