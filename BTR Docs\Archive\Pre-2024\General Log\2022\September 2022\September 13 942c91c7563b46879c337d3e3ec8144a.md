# September 13

Taking a look at things again after a roughly 2 month break

Issues with aiming reticle flying everywhere, want to fix to make game functional during movement.

Only works when player speed is 0 currently

Debugging with shoot modes scene

- Look at sphere script causing all the issues with movement
    - Disable this, things work fine
    - How to make movement of reticle rotate around sphere with controls? Is the answer in how Raycasts are moving?
    

Looking at GPU Instancer to see if I can reduce time for testing in editor

- Disabled Occlusion Culling, seemed to help
- Advanced features for improvements can be enabled once scene is fully designed

IDEAS

Player character changes for different types of bullet

- indicates what you’re about to shoot

Two colours like ikaruga? Polarity mechanic

[List of Games with Polarity Mechanics](https://videochums.com/category/duality-gameplay)

What’s my story? What’s my game about?

Removing viruses from infected systems

log on through a terminal

like superhot 

Add a time slow down?

Glitch time Right Trigger breaking things! Not sure why!

Changed RewindToBeat2 to slow down time

instead of -1 and .44s

0.1 and 2s

Not working, not sure why! Look into this