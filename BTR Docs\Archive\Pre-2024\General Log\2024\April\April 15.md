# April 15

Going through files, finding things to help with Ouroboros Planning

- Snake as rings wrapped around a planet - ouroboros
- Think ouroboros - surround an orb - shoot any bullets at that orb to take out the health of the current orb and when you are successful a kill all enemies event occurs , per wave
- Ouroboros - A serpentine creature that encircles the player, creating an ever-shrinking arena. Its attacks are wave-based and must be dodged to the beat.

Adding koreographer to enemy twin snake boss does not work properly, requires further refinement

Should i have a speed dash that affects tempo / music?

Created background snake level object

Movement mostly figured out, minor adjustments needed

still need to figure out gameplay over time