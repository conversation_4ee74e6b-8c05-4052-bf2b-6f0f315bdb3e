# March 3

[https://www.arts.on.ca/grants/media-artists-creation-projects](https://www.arts.on.ca/grants/media-artists-creation-projects)

April 7th Deadline!

[https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs](https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs)

May 4th!

Troubleshooting Koreo is not picking up events of same name upon transition to new koreo

FIXED - Reassociating Koreography with the audio file seems to have fixed it. Check the path, but also may just need a reload when this happens. 

Can use same event names for multiple koreo and it automatically carries over registration - this is ideal!

Several sections done and working - need to see how glitching time works now as well. Starting off with fixing Pause - need to pause FMOD as well

Pause working!

Next, time skip

Partially working! Reference implemntation here

[https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846](https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846)

Need to have seperate Koreo Tracks for Rewind Time and Shooting Time

Need to stop time from happening endlessly. Looking over the code

WOR<PERSON><PERSON>

can repeat 3 times

5 second cool down where you cant use it - how to visually communicate this to the player?

Stamina bar? [https://www.youtube.com/watch?v=sUvwKH7qyQQ](https://www.youtube.com/watch?v=sUvwKH7qyQQ)

Another example [https://www.youtube.com/watch?v=Fs2YCoamO_U](https://www.youtube.com/watch?v=Fs2YCoamO_U)

- more detailed, can show how much is used up

How should it look?

Need to work out the timeline int true value and the coroutine timing difference needed

Seems like collision to change song section is unrealiable - other possible methods?

Maybe Set variable, check variable, if not new value, attempting setting again, otherwise exist - loop until done?

Multi instruments with async values just keep playing and ignore timeline - need to consider this when setting things up

Magnet regions - investigating these

[https://www.youtube.com/watch?v=81sYxgHvx-U](https://www.youtube.com/watch?v=81sYxgHvx-U)

Look at Paramemter: User Labeled instead of using values

Magnet Regions - Can make a relative transition point instead of starting at the beginning of each next section, as shown in video, need to use Offset relative for the regions you’d live to see this behavior

Look into state machine behaviour, previously saw tutorials on this pop up

- infallible code?

Looking at code refactoring tutorials

[https://www.youtube.com/watch?v=7oZBfpI_hxI&t=1s](https://www.youtube.com/watch?v=7oZBfpI_hxI&t=1s)

Tips

Frame refactoring sessions on a specific goal

- scalabilty - decouple tightly coupled code
- testability - externalising classes dependencies to be injected
- readability - probably your most important

Readability Tips

- Consistency - indentations, naming, code styling
- Look at Microsoft’s C# conventions, available online
- Bake intent into code your writing as much as possible
    - extract portions of logic into methods that make things clearer
    - specific ex: naming coroutines DoesThingCoroutine
    - making variable value explicit ex: rename duration to durationInSeconds
    - create well named local variables when it makes sense
- Cut out any of the fat - dont use this. in c# if you dont need to
- Try not to use comments if you can make code self explanatory
    
    

IMPORTANT - too many IF statements, find video about fixing this again!

WHere is the video? Does command patterns help?

Command Patterns - [https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s](https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s)

STRUCTURE

who do I want things to progress? When should music advance?