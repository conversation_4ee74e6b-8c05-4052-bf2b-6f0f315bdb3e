# Codebase Restructure

Scriptable Object Architecture (Primary Recommendation):

- Scriptable Object Architecture (Primary Recommendation):
- Use ScriptableObjects as data containers and event channels
- Decouples systems while maintaining Unity's component-based design
- Great for runtime configuration
- Excellent for designer workflows
- Better memory management than pure service locator

Event System (Secondary Layer):

- Build on top of your existing EventManager
- Decouple systems through events rather than direct references
- Better for runtime communication between systems

Minimal Service Location (Tertiary Layer):

- Use for truly global systems (Audio, Input, Save System)
- Implement through a simple service container

Assets/_Scripts/
├── Core/
│   ├── Bootstrap/                    # Initialization and scene loading
│   │   ├── GameBootstrapper.cs
│   │   └── SceneLoader.cs
│   ├── Management/                   # Global managers
│   │   ├── TimeManager.cs           # Your existing time control
│   │   ├── EventManager.cs          # Keep your current event system
│   │   └── UpdateManager.cs         # New: Custom update manager
│   └── Services/                    # Global services
│       ├── AudioService.cs
│       └── InputService.cs
├── Data/
│   ├── ScriptableObjects/          # Configuration data
│   │   ├── Projectiles/
│   │   │   ├── StandardProjectile.asset
│   │   │   └── BossProjectile.asset
│   │   ├── Enemies/
│   │   │   └── EnemyTypes/
│   │   └── Player/
│   │       └── PlayerStats.asset
│   └── Runtime/                    # Runtime data containers
│       ├── GameState.cs
│       └── WaveState.cs
├── Gameplay/
│   ├── Player/
│   │   ├── Logic/                  # Separate logic from visuals
│   │   │   ├── PlayerController.cs
│   │   │   └── PlayerCombat.cs
│   │   └── Visual/
│   │       └── PlayerAnimator.cs
│   ├── Projectiles/
│   │   ├── Core/
│   │   │   ├── ProjectilePool.cs   # Object pooling
│   │   │   └── ProjectileManager.cs
│   │   └── States/
│   │       ├── BaseProjectileState.cs
│   │       └── ProjectileStates/
│   └── Enemy/
│       ├── AI/
│       └── Combat/
└── UI/
├── HUD/
└── Menus/

**Migration Strategy:**

- Create new folder structure
- Move existing files without changing them
- Gradually convert to ScriptableObjects starting with:
- ProjectileManager (high ref count)
- EnemyBasicSetup (multiple instances)
- PlayerConfig (frequently modified)
- Implement UpdateManager for heavy Update users
- Separate logic from visuals incrementally

This revised plan:

- Maintains existing EventManager
- Adds performance optimization with UpdateManager
- Separates logic from visuals
- Uses ScriptableObjects strategically
- Preserves current scene loading
- Reduces circular dependencies
- Improves memory management

**More detailed**

1. Create new folder structure
├── Core/
│ ├── Bootstrap/
│ ├── Management/
│ └── Services/
└── Data/
└── ScriptableObjects/
2. Move core managers
    - Move EventManager.cs to Core/Management/
    - Move TimeManager.cs to Core/Management/
    - Create UpdateManager.cs in Core/Management/
3. Create bootstrap system
    - Create GameBootstrapper.cs
    - Move scene loading logic to SceneLoader.cs

1. Create ScriptableObject configs
├── Data/ScriptableObjects/
│ ├── Projectiles/
│ │ └── ProjectileConfig.cs
│ ├── Enemies/
│ │ └── EnemyConfig.cs
│ └── Player/
│ └── PlayerConfig.cs
2. Convert existing MonoBehaviour configs
    - Start with ProjectileManager (high priority)
    - Convert enemy configurations
    - Convert player settings
3. Create runtime data containers
    - GameState.cs
    - WaveState.cs

Reorganize player systems
├── Gameplay/Player/
│   ├── Logic/
│   │   ├── PlayerController.cs
│   │   └── PlayerInput.cs
│   └── Visual/
│       └── PlayerAnimator.cs

1. Refactor projectile system
├── Gameplay/Projectiles/
│ ├── Core/
│ │ ├── ProjectilePool.cs
│ │ └── ProjectileManager.cs
│ └── States/
│ └── ProjectileState.cs
2. Update enemy systems
├── Gameplay/Enemy/
│ ├── AI/
│ └── Combat/

1. Update event subscriptions
    - Replace string events with constants
    - Implement UnsubscribeFromAllEvents
2. Implement UpdateManager integration
    - Convert heavy Update users
    - Profile performance improvements
3. Test and optimize
    - Profile memory usage
    - Test scene transitions
    - Verify event system