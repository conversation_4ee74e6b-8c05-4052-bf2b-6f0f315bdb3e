# August 3/5

Minor bug fixing. Issues with FMOD that i’m addressing

Speeding some things up in the game to increase intensity

- Snake shouldn’t do hit anaimation and then death animation, just death
    - Done!
- Disbale Y rotate direction in cahsed by snake level
    - Done! Disable Player Features script
- Cant shoot in section after snake, Koreogrpaher issue?
    - Beleive i fixed this once before

August 5

Really dont have it in my to work on this today

S<PERSON><PERSON> gets hit sound, as well as Death sound needed

- Done! Needs adjustments but a good start

Section 3 music not looping properly

Section 3 need the enemies and bullet speeds adjusted for faster movement. 

Consider second snake encounter - maybe not the crazy graphics? WHat else ot make it different 

Enemies flashing could be good, whats a good way to implement that? let’s look into it.