# Projectile States and system movement

**OnEnable** 

Set lifetime

Set projectileTarget as Player

Set parent object as EP1 - Enemy Plane?

Register with <PERSON>re<PERSON> for events

homing enabled on start attribute - likely need to remove this?

**Start**

locked = false

released = false

launching = false 

set originalparent - This is enemy plane. Is this what’s needed?

**FixedUpdate**

if projectileTarget isnt null

Missile movement math is done

if projectileTarget is null - Removed as it seems redundant

just move forward

if homing is true and projectileTarget is not null

look at the projTarget - Is this conflicting with any other math?

could disable homing when close - currently not doing this

if launching lifetime countdown is paused

if not launching then resume lifetime countdown - Lots to consider on lifetime, need to redefine how it works when shot vs never locked on 

if timescale > 0 and not locked and not launching 

just move forward

if locked is true, released is false, launching is false, collectable is false

release object rom oParticles system

homing = false

Freeze in place! Rigidbody

Look at HorizontalPlane - WHY

Set parent to horizontal plane - WHY

Rewindable by time is False

release is true

if locked is true and launching is false and LINE RENDERER is on

draw some line renderer stuff

if laucnhing is true and locked is true

UNFREEZE the projectile

draw some line renderer stuff

set projectile forward as same forward as player aiming

Move bullet forward

TLine.globalClockKey = "Test"; - WHY IS THIS HERE?

**Launchback**

**LaunchatEnemy - takes transform target**

if child exists at 0, destroy it. this should be aimPrefab - CHANGING THIS TO 1 for PARTICLE FX

This may explain why bullets were inactive but showing aimPrefab, the particles effects were deteled because they exist at 0

projectile target is set to target

set parent to null

homing is true

lanuching is true

---

if homing is true and launching is true

- will look at projectileTarget
- no RB constraints, local forward is being set to player rayspawn forward, rb velocity

What are all bool states when a projectile gets locked?

Crosshair class sets locked = true

locked should be true and other attributes all false, so it freezes the projectile

end of this action sets released to true, causing this to NOT be run again

locked is true and released is true