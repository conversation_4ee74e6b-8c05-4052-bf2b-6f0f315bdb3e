# May

**May  15**

G9 - Spinning triangle working with <PERSON><PERSON><PERSON><PERSON> (Full 360)

Attempting Fix of boxcast - need better aiming

FIXED!

Objects effected by time vs those that aren't - G9 Triangles

Commented out RewindShoot due to error tracing - cant find why objects are still being destoryed when i have <PERSON><PERSON><PERSON> commented out some ocode

Pretty sure trying to destory child of bullet actualyl destoryos entire bullet

FIX THIS

Launchab;e bullets not working

FIX THIS

**May 16**

- am not recycling on release from particle system
- am not calling Death

Seemed to fix this, uncertian how exactly. Recognized Lauchable in the Update method, that seems to be it

Enabled isTrigger on Homing Bullet

G9-2 has this lock object / fire back at target loop working

Need to learn Unity Events for trigger different stages of this boss / puzzle

[https://www.youtube.com/watch?v=TWxXD-UpvSg](https://www.youtube.com/watch?v=TWxXD-UpvSg)

**May 17th**

Gamma Space Meeting

**May 18th**

Unity Events  - [https://www.youtube.com/watch?v=OuZrhykVytg&t=12s](https://www.youtube.com/watch?v=OuZrhykVytg&t=12s)

Maybe I should use Actions instead of events?

Switch - look into this more

Learned Unity Events and have working system for bring up new game objects! All in G9

**May 19th**

Extending puzzle system - added more layers of objects to hit

Added player invincible option

Fixed collision problems by slowing launchback bullets

Also changed OnCollisionEnter to OnTrigger - homingbullets are triggers now

TODO

MeshManipulation pLugin - use this on objects??? Performant? NEED TO BUY $5

Change MMFeedbacks to Feel package? maybe already easy?

**May 22**

Changed out MMFeedbacks to Feel - may be some missed connections I will find in time

Linear speed areas VS Puzzle areas

Pause Menu integrated! Rough but working

Imported EasySave

Not using yet

**May 23**

Added Mesh Tracer to project

Some interesting effects for enemies / objects - flag for later use

**May 24**

Mechanically inspired by helix jumper

[https://assetstore.unity.com/packages/templates/packs/helix-smash-with-live-auto-generator-147944](https://assetstore.unity.com/packages/templates/packs/helix-smash-with-live-auto-generator-147944)

might be cool to have section where you move through faster/slow to get past targets

New greybox with mov forward through puzzle mechanism

bullets now attach to reticle before launch, and if using time skip mechanic can reaim a cluster of bullets to a new position

  

**May 25**

Added Easy Performant Outline - Try this with lock on for visual clarity

Made a CowCatcherForward for catching launched bullets

- decent solution so they dont go to infinity but likely need something better

Tried limiting number of rewinds possible

Still buggy!

May need to rethink this whole structure. Do I need Coroutines for all of this? Could probably just use methods / events? Look into this deeply!

**May 26**

Learned a bit about Unity Profiler and Deep Profiling

Suggests to me that I don't have much to worry about currently! Not seeing any big performance problems there

Try changing location of loop

**May 27**

Location of loop changed, limtied number of right trigger rewinds appears to be working

Feels like a fire shoot be initiated when you run out - adding this

Figured it out!

Mirror rotating idea, dont forget!

[https://assetstore.unity.com/packages/vfx/shaders/urp-lwrp-mirror-shaders-135215](https://assetstore.unity.com/packages/vfx/shaders/urp-lwrp-mirror-shaders-135215)

Try Easy Outliner and get it working

JUST REALIZED i should set the puzzle as waypoints, not just forward speed. Look at how to set this up with Cinemachine Dolly, and how to start/stop at waypoints

IDEA: counterpoint like occurring here 

![May%2083a377602a67417bb2a63c8cc1bbd387/Untitled.png](May%2083a377602a67417bb2a63c8cc1bbd387/Untitled.png)

**May 29**

LightProbeGroup/ReflectionProbe exists in original StarFox file - why not mine?

Changed Player tag to Player gameobject instead of Player model - does this cause problems?

Added lots! Start / Stop waypoints

Aiming laser for locked bullets

Playing with Reticle position when locked on / aiming a grouping of targets - needs work

Messing with Targets / Enenmy shooting relationship

Bug fixing!

Should I change the look at of bullets / laser so it's CowCatcherForward?

Will this fix my aiming issues?