# Sept 18

Fixing up proper BTRemake so it has improvements from jumbled one

Things needs to do 

- Fix lock on
    - This is layer or tag based issue, there are a few similar
- Turning and player flying away
    - Making adjustments to spline to help prevent fall off of main character,  issue with turning at certain places - perspective turning that is - might need to tighten how turning works so player doesn’t get stuck
    - Sphere collider restricting movement space worked! try this again
- Fix transparency issues on aim cube
- Fix positioning of UI
- Mobius 2 Variation has enemies falling off - look into this!
- May need to export dolly/waypoint placement from Beat Remake to BTRemake, had made some adjustments I think
    
    

Open ‘Beat Traveller’ from 4TB Hard drive to see if my appropriate render pipeline is still intact there

Trying to rebuild old pipeline, can use a general URP pipeline but want all my old render features and more

Not working, think i have to just rebuild render pipeline. Not the worst! Have the files within 

<aside>
💡 Should the highlight transparent queue be opaque or transparent? Check on this

</aside>

<aside>
💡 SRP batcher causing graphical errors - can i use it? can i fix it? off for now

</aside>

Added beautify 3 - play around with this for visual enhancement!

IDEA

Music - is movement - is action - is ?

movement forward / backward/ left / right 

dodging / moving resemble b boy dance moves?

Spikes in garbage collector - what is it?

- Every time you call Debug.Log, or an error or warning is triggered (that's how you get the whole path of "how did we even get to this function?" when you click on a console log), a stacktrace is performed, which in large numbers is a big performance hog and GC generator. Do you have some Debug.Log's happening that you don't need?

Added logo image - need to clean up a bit