# Rez V2 Log

Summary of ideas to consider from old docs

- [ ]  Adding code to allow different objects to trigger different Lock / Shoot sounds
- [ ]  Correct for Raycast detection of targets
- [ ]  Need bullets to spray more - vertical and horizontal

- [x]  FIX the offset of the target
- [x]  Place some graphics in the scene
- [x]  Look up journaling in notion?
- [x]  Define missile launching as public button
- [x]  Adjust joystick sensitivity
- [x]  Fix locking sounds
- [x]  Make bigger visual cue - refer to old Rez Alt and Square animation - LockedOnSpritePrefab
- [x]  Need faster tempos
- [x]  Try alternate lock sounds - melodies?? Refer to <PERSON><PERSON> for demo songs
- [x]  Lots with enemy dev + change some sounds + adjust controls
- [x]  Level design / enemy spawner is important next steps
- [x]  Fix lock and shoot timing
- [x]  Break lock and shoot into multiple methods
- [x]  Implement SplineMove and Koreographer
- [x]  Look at Space Combat Kit Unity Package for enemy movement
- [x]  Fix missile system locking up
- [x]  Create animation for fade in - instantiation
- [x]  Fix bullet pooling
- [x]  Understand how Raycast works

**Feb 24th**

Found new system for lock on, integrating level aspects into it

FIX the offset of the target

Place some graphics in the scene

Look up journaling in notion?

**Feb 25th**

Fixed offset of target !!!

Integrating Koreographer

Beat / Tempo is happening

Timed Lock on as well, but missile launching is broken - FIXED but need to define as public button

Joystick also too sensitive

**Feb 26th**

Fixed public Firing Keycode

Locked framerate to 60 in global variable

**Feb 27th**

Learned/changed some lock on animations - still need work

Need to adjust when lock / fire sounds happen

Need to adjust locking system / still shakey

Lock aim box size is causing targeting issues - partially fixed but need to fine tune!

Trails on missiles, some odd behaviour with missile laucnhing - investigate

FIX LOCKING SOUNDS - WHERE TO PUT THESE

**Feb 28th**

hangover! Not much happening today. Houdini in Unity looks interesting

Also researched Ableton 11 a bit, useful!

Swapped Snare / Hi hat - then replace snare with kick. Not totally working - look at Rez for guidance

**March 1st**

Figure out out of range error, eventually! AdvancedMissileLocker 267

Trying Out MultiMusicPlayer Koreographer

Trying to debug locking / firing issues. Need a deep dive on the code

AdvancedMissile / Musical Missile Issues - revert back to old code? Get backup working as well!!!

**March 2nd**

Issues with Missile Launching script and Koreographer? How to fix?

**March 3rd**

Made whole new project, seems to have fixed these issues! Backed up to Github

Double up Hihat sounds / rhythm for better effect?

What to import from old project?

**March 4th**

Trying doubled up hi hats - lock on is still not obvious but sounds better to have double hi hats

Make bigger visual cue - refer to old Rez Alt and Square animation - LockedOnSpritePrefab

Tried a crazy one - it helps!

Need faster tempos

Need to try alternate lock sounds - melodies?? Refer to Rez for demo songs

Maybe Nier gameplay?

**March 6th**

Lots with enemy dev + change some sounds + adjust controls

Need some environment stuff, hard to navigate!

**March 7th**

Trying alternate sounds for lock on / fire. Using notes instead of percussion

Level design / enemy spawner is important next steps

**March 8th**

Messing with level layout and Ultimate Spawner / New waypoint system 

Adding code to allow different objects to trigger different Lock / Shoot sounds

Seems to be working for Spinnerz - need different enemy type to verify - do this!

- doesnt seem to lock on to double spinnerz? Not sure changing layer messes it up

**March 9th**

Realizing shooting and locking is really not in time

Need to revamp and analyze code thoroughly

Break lock and shoot into multiple methods???

**March 10th**

Got good advice from Koreo Discord! Breaking into multiple methods. Need to comment and analyze more. Getting there! This is the main thing to fix now. If this work then the game works.

May need to refer to Koreo documentation for deeper understanding of plugin

**March 11th**

Height is wrong! Need to correct for Raycast detection of targets

Moved for look out of LaunchMissles to FireMissles

Then moved from FireMissles to OnMusicalFire

Moved almost everything there - but doesn't work! NEED to rethink, how/what am i timing?

Look at other Koreographer projects to better understand usage

Rhythm demo?

**March 12th**

Corrected height, still needs some adjustments

FOUND LAYER ISSUE - need to select all lockable masks

![Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled.png](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled.png)

THOUGHT - Using Corotuine to delay subsequent locks

Locking is happening fast but sound is not

Moving sound to intial Locking method does not work!  

**March 13th**

Doing more work on Musical Locking

Need to find a way to make lock speed / firing rate data pulled from Koreographer tempo

Looking for a way to time Start Lock - LOCKED - Fire sequence properly to music

Try sound effects and see

Need to find a way to break locking once lock has occured on object, or delay at least

initial lock speed vs concurrent lock speed

Pretty sure MultiLockingCheck_Co() has the key to this!

IDEA if the last value is the same as the previous value, dont add?

**March 15th**

Missiles no longer reloading!

**March 16th**

Fixed reload! Trail Render Autodestruct was the issue. Need to find a workaround. 

[https://forum.unity.com/threads/re-use-bullet-with-trailrenderer.321010/](https://forum.unity.com/threads/re-use-bullet-with-trailrenderer.321010/)

Tried new Trail plugin, sort of helps - needs tweaking

Added Sync Play Delay 0.179 to Koreogrpaher Multi Music Player

Does this fix anything? Test this out!

My game doesn't feel exciting when lock on/shoot  - why?

Referring to Rez......

Speed of motion + multi lock is more exciting?

Trying Scene transitions! Brackeyes video

Next Level and Restart Level implemented

Need to tie timing of level transition to Koreographer tempo

Need a cooler transition? maybe a musical one

Test Ultimate Spawner, basic functions work. Need to think through how to best use it. 

**March 17th**

Changing audio clip loading for easier swap of sounds

Resources/Music/buildnumber used for this 

Load next level using Trigger? Use Koreographer payloads for this probably

beat 1 payload string

Started on music for scene transitions

COOL IDEA - Ribbon trail on particles effects for creating lines

**March 20th**

Have a temporary fix for level transitions - need a real one

Can change scenes ~ relatively ~ in time with the music

Enemy AI Needed Now

movement  

shooting

 

[Enemy Movement](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Enemy%20Movement%20d9e03542d900416caac4dc1bde3da2d7.md)

**March 21st**

Figured out some paths and workflow for levels

BROKEN - cant actually destroy targets? Not sure what's wrong

FIXED - Missle speed is a problem, need to figure out limits

Likely a physics issues - do we want to take another approach?

**March 22**

Integrate SplineMove and Koreographer? Look at events

Thinking about design... should it be twin stick?

Space Harrier type of thing?

How should waves of enemies work?

Lock camera perspective? - look at how this was done on older version of this project

- possible to implement here?

consider how to recreate rez enemy appearing movement

base movements on musical ideas? When enemies move in or react

**March 23**

For enemies 

- Look at Space Combat Kit Unity Package

Fucked up bullet instatiation and enemy left trail of path - kind of a cool effect!

Bullets working with Koreographer now! 

Need bullets to spray more - vertical and horizontal

better missile? 

[https://assetstore.unity.com/packages/tools/pro-nav-guided-missiles-9137#reviews](https://assetstore.unity.com/packages/tools/pro-nav-guided-missiles-9137#reviews)

Special Move?

[https://assetstore.unity.com/packages/vfx/particles/spells/shuriken-salvo-52746](https://assetstore.unity.com/packages/vfx/particles/spells/shuriken-salvo-52746)

**BIG QUESTION**

Should I just move the level around the player and the player is static? 

Will this make things a lot easier?

Seems like it would actually introduce problems

Have some random patterns working for bullets, but not perfect yet

Is the bullet hell angle worth pursuing??

Problem with missile system locking up

Think it might be bullets dying before missles hit them

Maybe just use the locking system as hitting the bullets? Rewrite this?

Maybe branch to try that out one day

Think the scenario through a bit

Upon appearing - animation for fade in - instantiation

- this is for a big block of bullets or pattern - maybe use weird vertex shader?

BUg Fixixng

---

---

Current target List exists before attempting any lock on - purely stuff within target view

![Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%201.png](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%201.png)

But, this error keeps happening

![Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%202.png](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%202.png)

Causing

![Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%203.png](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Untitled%203.png)

Possible step between adding to LockedOnTargetList and accessing LockedOnTargetList where we need to check object

LockMissle_Multi is method that adds targets to LockedOnTargetList

Then when is the next time we access this list?

Review all my methods

LaunchMissles references the list - dont think error is here

LockMissle - maybe the error is here? Seems to only work for single

LockMissile_Multi - maybe it's in here

This method moves current targets to locked on targets list

also redefined as currentLockingProcess - so we can create multiple i presume? Its a coroutine

AML 387 - moved SetLockedSprite call to make sure it's in an if bracket - does this help?

NO - caused a different issue!

Is it just the sprite objec tthat's the issue?

Looking into lockedOnSpritesList - nah issue seems rampant everywehre

**Destorying bullets at max distance seems to be the issue!**

Need to introduce pooling for bullets

Applied pooling for bullets. Broken! Need to look over implementation

**March 27**

Got Pooling working! Also found a cool controller from MixnJam to use

Trying to understand how my Raycast works

Not in Player Canvas

[Bugs](Rez%20V2%20Log%206ba730a4a0ea4b119a6e5ba3560d466f/Bugs%20b6a66bb2bf33469daa58ee26b6d02d70.md)