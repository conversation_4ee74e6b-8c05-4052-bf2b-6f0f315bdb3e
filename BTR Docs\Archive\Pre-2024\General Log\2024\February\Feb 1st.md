# Feb 1st

Useful for Ophanim

[Ring World Using Random Flow](https://www.youtube.com/watch?v=YHkhEyh5G68)

Also good blender editing tool possibly 0 Quad Remesher - already installed

[Quad Remesher add-on | Blender Secrets](https://www.youtube.com/watch?v=gCj6dTNhO_c)

Look into Quadriflow Remesh as well?

[Unity VFX Graph：Use SDF to make model particle effects](https://www.youtube.com/watch?v=FBP9k6W48vM&t=905s)

Model for how I implemented SDF particle effects - May need as reference

[https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)

Game Design Resources full spreadsheet - lots of good links!

[SwiftRoll 🐦 – Medium](https://medium.com/@swiftroll3d)

Unity Optimization and Programming

[A multitude of Empty Slots (for Comissions) on Twitter / X](https://twitter.com/ZloNoNameSov/status/1716610855125799378)

Cool enemy character inspo - check out guys other stuff too!

[EXTREME PERFORMANCE with Unity DOTS! (ECS, Job System, Burst, Hybrid Game Objects)](https://www.youtube.com/watch?v=4ZYn9sR3btg)

Convert projectile system to DOTS?

How to spawn the DOTS projectiles

```jsx
using Unity.Entities;
using UnityEngine;

public class ProjectileSpawner : MonoBehaviour
{
    public GameObject projectilePrefab;

    void Start()
    {
        var settings = GameObjectConversionSettings.FromWorld(World.DefaultGameObjectInjectionWorld, null);
        var projectileEntityPrefab = GameObjectConversionUtility.ConvertGameObjectHierarchy(projectilePrefab, settings);

        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;

        Entity projectileEntity = entityManager.Instantiate(projectileEntityPrefab);
        entityManager.SetComponentData(projectileEntity, new ProjectileData { bulletSpeed = 10f, lifetime = 5f, homing = true, launched = true });
    }
}
```

Notes on current state of conversion of projectiles to DOTS

[Dots Notes](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Dots%20Notes.md)

Trying some other optimizations beofre DOTS

[Projectile Manager Optimizations](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Projectile%20Manager%20Optimizations.md)

Singleton Manager - definitely seems to help!