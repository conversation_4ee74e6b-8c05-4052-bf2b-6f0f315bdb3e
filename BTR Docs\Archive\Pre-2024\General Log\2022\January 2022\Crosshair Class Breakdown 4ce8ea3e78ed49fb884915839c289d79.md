# Crosshair Class Breakdown

**Start**

Setup player input

Load Shooting Audio files for level

Get Renderer component from Reticle

locking = true

Register Koreographer instances for Shooting, Locking, and Time Rewind

delayLoop = false

num of rewinds = 0

rewindTriggerStillPressed = false;

**Fixed Update**

Debug.DrawRay Called

OnLock method

clock registered  to Timekeep.instance.Clock

**OnLock**

If the Raycast hits something & player is pressing fire & it’s either tagged bullet, launchablebullet, or enemy

if my array of targets does not contain this hit.transform & it has a Projectile class & Projectile is not already locked.

if locked list is less then maximum targets & targets is less then max targets

Set that bullet to locked

Add the transform to target array

Else

just return; COMMENTED THIS OUT - WAS THIS A CHOKE POINT?

Else if we hit and enemy, lockedlist count > 0, player is pressing lockenemy button, and we are not already targeting this enemy

Frist check if we ever locked onto any enemy, if so set locked to false and remove that aimPrefab from them 

Otherwise lock the enemy, give them an aimPrefab, setup the color, etc

if hit.transform = null then just return

**OnMusicalLock - fed by koreo event**

if fire button is pressed and targets array is greater then zero & timescale matches

add the aimprefab at targets [0]

add value from targets to LockedList

Run lockvibrate coroutine

Play random locking sound

++ to Locks value

Remove targets[0]

**OnMusicalShoot - fed by koreo event**

setup combo score value

tempLocks = Locks;

if player has released fire or triggeredLockFire is true & LockedList > 0 && Timescale matches

locking = false

clean the LockedList of null values

if the item in LockedList is a Launchable Bullet & not null

if enemyTarget is not null and enemyTarget is active

Launch bullet at enemyTarget

else

Launch bullet at RaycastTarget

else

Debug null object still locked

Vibrate controller

Shake Camera

if we’re on last item of LockedList then play a shootTag if enabled

Remove item from LockedList

locking = true

ShotTally++, comboScore++

Locks = Locks - 1

Score equals combination of locks and combo score WHEN BUGS ARE FIXED LOOK AT THIS CLOSER

**RaycastTarget**

Returns either hit.point or ray.GetPoint(range)

This would be the end of the maximum range the raycast is set

What is LineToTarget??