# August 12

COnverting some DOtWeen to Prime Tween 

Need to look over and make sure all work evenetually

Scripts caught as having DoTween, upgrading some

DestroyEffect

- done

LookAtGameObject

- done

UILockOnEffect

- done

ColliderHitCallback 

- done

PlayerMovement

- done, very important to verify!!!

EnemySnakeMidBoss

- wrongly thought it was there, but it isnt

DestroyEffect

- done

Also, trying to fix previous issue with getting locked in rewind time, unsure exactly how that’s happening. Possibly FMOD issue as well

Play test and see if it pops up again after FMOD fixes

August 13

Issue popped up again. Code base scan showing these potential issues 

-In the Crosshair script, check the EndRewind method to ensure it's properly resetting all rewind-related variables and states.

-In the ProjectileStateBased script, ensure that the lifetime is properly managed during rewind:

-In the GameManager script, make sure the time scale is properly managed during and after rewind:

-In the ProjectileStateBased script, add safety checks to prevent unexpected behavior:

-Implement a global reset function: Create a method in GameManager to reset the game state, including projectiles, when switching between normal and rewind modes:

Should move time features to Game Manager and just have everything talk to that. 

Considering priority system so time interrupts are possible. 

Make sure adjustments to timescale by gamemanager are global when needed, local when needed

Shifted time features to Game Manager - not fully working but needed to be done!

Need to implement the player / crosshair time features properly now, they dont quite work as they used to