# May 17th

Look at some of the Jason <PERSON> optimizations to apply to my project. 

Adjusting update internval of behavior manager (I think? may need to verify)

Have it running half speed now, is this more performant?

Also applying some performance optimizations to Projectile State Based, this is based on seeing spikes due to the On Enable method in the Profiler. 

Careful with materialInitialized flag that’s been added in ProjectileStateBased, may cause an unexpected error 

May 18th

inkblot not working, should be in game now, just bring it in agian and koreo it. 

may need to import new vfx librayr once again from occa