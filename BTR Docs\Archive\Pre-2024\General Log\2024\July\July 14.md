# July 14

Trying to debug path fail issue on current iteration, version from a few weeks ago doesn’t encounter this

Eventually path fails critically on a mesh, or when enemy dies and they spawn again, they all get paith fail and are on the fritz. This behaviour doesn’t happen in older project version, so trying to debug this. 

Need to assess if fixing new version is better then building on top of old version until something breaks

In the mean time - level strucutre! What do I want to do?  <PERSON>il taking a break from Ouroboros?

<PERSON><PERSON><PERSON> is coming along. 

Idea - use deformer on ophanim to  make things twistier? Will <PERSON><PERSON>vy splines respond appropriately?