# March 9

Change Wave to show brefly at start and then disappear

- make this like Control? Need Sound Effect

Integrating target locks stat

Need to make bullet and enemy stat colors same as what’s on the radar

Suggestted to put them on the reticle… there or the player character? 

Implemented this in a basic way. Testing to see how it feels

Integrate other menu quality settings!

Using asset AllSettings to comapre issues

<aside>
💡 May have a better integration of resolution, need to test a build and see!

</aside>

Use Projectile Toolkit to fix bullet issues?

Optimizations needed

- turn off second camera until it’s necessary
- removed altos to gain some performance. will possibly revisit this

Shadow distance in render pipeline - what is ideal?

Do I need the depath texture and opaque textures on?

Enabled Tools for Frame Debugging in Render Pipeline - disbale these if not needed! 

Turned off features of Render Camera to try and get some performance

Optimization

• **Gfx.WaitForPresent**: When the main thread is ready to start rendering the next frame, but the render thread has not finished waiting on the GPU to Present the frame. This might indicate that your game is GPU bound. Look at the Timeline view to see if the render thread is simultaneously spending time in Gfx.PresentFrame. If the render thread is still spending time in Camera.Render, your game is CPU bound and e.g. spending to much time sending draw calls/textures to the GPU.

![Untitled](March%209%206ca6200d922949ec88ff22bf96a80ffc/Untitled.png)

Semaphore.WaitForSignal
17.28ms

Current frame accumulated time:
17.42ms for 21 instances on thread 'Main Thread'
324.38ms for 102 instances over 11 threads

I am GPU bound - need to find out why

[https://docs.unity3d.com/Manual/ProfilerGPU.html](https://docs.unity3d.com/Manual/ProfilerGPU.html)