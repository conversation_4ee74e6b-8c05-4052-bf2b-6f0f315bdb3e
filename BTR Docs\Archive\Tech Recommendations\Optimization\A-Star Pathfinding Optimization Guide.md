# A* Pathfinding Optimization Guide

## Overview

This document outlines optimization strategies for the A* Pathfinding Project in our game, with a focus on the `GetNearest` method which was identified as a significant performance bottleneck (consuming 26% of CPU time). These optimizations are particularly relevant for enemy prefabs like `Enemy Dodeca ES - Migration 1.prefab`.

**Updated 2024-12-19**: Added centralized pathfinding architecture using EnemyManager for improved performance with the new entity system.

## Key Bottlenecks

1. **GetNearest Method**: Frequently called to find the nearest walkable node, consuming significant CPU resources
2. **Path Recalculation**: Enemies recalculating paths too frequently
3. **RVO Collision Avoidance**: Excessive neighbor checks
4. **Movement Strategy Updates**: High-frequency decision making (55.5% CPU usage identified)
5. **Individual FollowerEntity Calculations**: Each enemy performs independent pathfinding operations
6. **Redundant Distance Calculations**: Multiple Vector3.Distance calls per frame per enemy

## Optimization Strategies

### 1. Spatial Node Caching

Implement a caching system for `GetNearest` results to avoid redundant calculations:

```csharp
// PathfindingOptimizer.cs
public class PathfindingOptimizer : MonoBehaviour
{
    // Cache settings
    [SerializeField] private float nodeCacheTime = 0.5f;
    [SerializeField] private float nodeCacheDistance = 1.5f;
    [SerializeField] private int maxCachedNodes = 1000;
    [SerializeField] private float gridCellSize = 5f;

    private Dictionary<Vector2Int, List<CachedNode>> spatialNodeCache = new Dictionary<Vector2Int, List<CachedNode>>();

    // Cache lookup and storage methods
    public bool TryGetCachedNearestNode(Vector3 position, out Vector3 nearestNodePosition) { ... }
    public void CacheNearestNode(Vector3 position, Vector3 nearestNodePosition) { ... }
}
```

### 2. Throttled GetNearest Calls

Create extension methods to reduce the frequency of `GetNearest` calls:

```csharp
// AStarExtensions.cs
public static NNInfo GetNearestOptimized(this AstarPath astar, Vector3 position, NNConstraint constraint = null) { ... }
public static NNInfo GetNearestThrottled(this AstarPath astar, Vector3 position, Transform transform,
    ref Vector3 lastQueryPosition, ref NNInfo lastResult, ref float lastQueryTime,
    float queryInterval = 0.2f, float minMoveDistance = 0.5f, NNConstraint constraint = null) { ... }
```

### 3. Distance-Based LOD for Pathfinding

Adjust update frequency based on distance to player:

```csharp
// BasicChaseMovementStrategy.cs
[Header("Distance-Based LOD")]
[SerializeField] private bool enableDistanceLOD = true;
[SerializeField] private float closeDistance = 20f;
[SerializeField] private float mediumDistance = 50f;
[SerializeField] private float farDistance = 100f;
[SerializeField] private float closeUpdateInterval = 0.3f;
[SerializeField] private float mediumUpdateInterval = 0.8f;
[SerializeField] private float farUpdateInterval = 1.5f;
[SerializeField] private float veryFarUpdateInterval = 3f;
```

### 4. Batch Processing Path Requests

Group path requests by spatial location to improve cache coherence:

```csharp
// EnemyPathManager.cs
public void BatchProcessPathRequests()
{
    // Group requests by grid cell
    var requestsByCell = new Dictionary<Vector2Int, List<(float priority, EnemyCore enemy, IPathHandler handler)>>();

    // Process requests by cell to improve cache locality
    foreach (var cellRequests in requestsByCell.Values)
    {
        foreach (var (priority, enemy, handler) in cellRequests)
        {
            ProcessPathRequest(enemy, handler);
        }
    }
}
```

### 5. Optimized Enemy Prefab Settings

Adjust A* Pathfinding settings for better performance:

```csharp
// EnemyDodecaOptimizer.cs
[Header("A* Pathfinding Settings")]
[SerializeField] private float repathRate = 0.5f;
[SerializeField] private float pickNextWaypointDist = 2f;
[SerializeField] private int maxNeighbours = 5; // Reduced from default

[Header("Movement Strategy Settings")]
[SerializeField] private float decisionUpdateInterval = 0.8f; // Increased from 0.5f
[SerializeField] private float stuckCheckInterval = 2f; // Increased from 1f
```

## Implementation Steps

### Phase 1: Individual Optimizations
1. Create `PathfindingOptimizer.cs` for spatial caching
2. Implement `AStarExtensions.cs` with optimized methods
3. Modify `BasicChaseMovementStrategy.cs` to use distance-based LOD
4. Update `EnemyPathManager.cs` for batch processing
5. Create `EnemyDodecaOptimizer.cs` to apply optimized settings
6. Create optimized prefab variant

### Phase 2: Centralized Architecture (Recommended)
1. **Enhance EnemyManager** with centralized pathfinding system
2. **Create CentralizedPathfindingService** as a component of EnemyManager
3. **Modify Movement Strategies** to optionally use centralized pathfinding
4. **Update Entity System Integration** to work with both approaches
5. **Implement Fallback System** for backward compatibility
6. **Create Performance Comparison Tools** to validate improvements

## Expected Performance Improvements

- Reduction in `GetNearest` calls by 60-80%
- Lower CPU usage for pathfinding by 15-20%
- More consistent frame rates with large numbers of enemies
- Reduced GC allocations from pathfinding operations

## Recommended Settings for Enemy Dodeca

| Parameter | Original Value | Optimized Value | Notes |
|-----------|---------------|----------------|-------|
| repathRate | 0.5 | 0.5 | Keep same to maintain responsiveness |
| decisionUpdateInterval | 0.5 | 0.8 | Reduce update frequency |
| stuckCheckInterval | 1.0 | 2.0 | Less frequent stuck checks |
| maxNeighbours | 10 | 5 | Fewer RVO calculations |
| enableDistanceLOD | N/A | true | Add distance-based updates |

## Centralized Pathfinding Architecture (New Entity System)

### 6. EnemyManager Centralized Pathfinding

Leverage the existing EnemyManager spatial grid and centralize pathfinding operations:

```csharp
// Enhanced EnemyManager.cs
public class EnemyManager : MonoBehaviour
{
    [Header("Centralized Pathfinding")]
    [SerializeField] private bool enableCentralizedPathfinding = true;
    [SerializeField] private float pathfindingUpdateInterval = 0.1f;
    [SerializeField] private int maxPathCalculationsPerFrame = 10;
    [SerializeField] private float pathCacheRadius = 5f;
    [SerializeField] private float pathCacheTime = 1f;

    // Centralized pathfinding data
    private Dictionary<Vector2Int, CachedPathData> centralizedPathCache = new Dictionary<Vector2Int, CachedPathData>();
    private Queue<PathRequest> pathRequestQueue = new Queue<PathRequest>();
    private float lastPathfindingUpdate;

    public void RequestCentralizedPath(IEntity entity, Vector3 destination, System.Action<List<Vector3>> callback)
    {
        // Use spatial grid to check for cached paths
        // Batch similar requests
        // Prioritize by distance and entity importance
    }
}
```

### 7. Movement Strategy Integration

Modify movement strategies to use centralized pathfinding:

```csharp
// Enhanced BasicChaseMovementStrategy.cs
public class BasicChaseMovementStrategy : MovementStrategy
{
    [Header("Centralized Pathfinding")]
    [SerializeField] private bool useCentralizedPathfinding = true;
    [SerializeField] private float pathRequestCooldown = 0.2f;

    private float lastPathRequest;
    private bool hasPendingPathRequest;

    protected override void ExecuteMovementDecision()
    {
        if (useCentralizedPathfinding && EnemyManager.Instance != null)
        {
            RequestCentralizedPath();
        }
        else
        {
            // Fallback to individual FollowerEntity
            UseIndividualPathfinding();
        }
    }
}
```

### 8. Movement Strategy Performance Optimization

Address the 55.5% CPU usage from MovementStrategy.Update():

```csharp
// Enhanced MovementStrategy.cs
public abstract class MovementStrategy : MonoBehaviour
{
    [Header("Performance Optimization")]
    [SerializeField] protected float updateInterval = 0.1f; // 10fps instead of 60fps
    [SerializeField] protected float significantMovementThreshold = 0.1f;
    [SerializeField] protected bool enablePerformanceOptimization = true;

    private float lastUpdateTime;
    private Vector3 lastPosition;
    private float cachedDistanceToDestination;

    protected virtual void UpdateMovementLogicOptimized()
    {
        // Early exit if not enough time has passed
        if (Time.time - lastUpdateTime < updateInterval)
            return;

        // Only update if something significant changed
        bool hasMovedSignificantly = CheckSignificantMovement();
        if (!hasMovedSignificantly && !needsUpdate)
            return;

        // Perform optimized updates with cached distances
        UpdateTargetFollowingOptimized();
        UpdateMovementToDestinationOptimized();
    }
}
```

## Additional Optimization Ideas

1. **Job System Integration**: Use Unity's Job System with Burst compiler to parallelize pathfinding operations
2. **Hierarchical Pathfinding**: Implement a two-level pathfinding system (coarse for long distances, fine for nearby)
3. **Path Prediction**: For moving targets, predict future positions to reduce path recalculations
4. **Dynamic Obstacle Avoidance**: Implement local steering behaviors to avoid small obstacles without full path recalculation
5. **Memory Optimization**: Profile memory usage to identify and fix any leaks or excessive allocations
6. **Centralized A* Node Caching**: Cache GetNearest results at the EnemyManager level for spatial reuse

## Monitoring and Validation

After implementing these optimizations, use the Unity Profiler to:
1. Verify reduction in `GetNearest` method CPU usage
2. Check for any new bottlenecks that may have emerged
3. Ensure enemy behavior remains consistent with game design expectations
4. Monitor memory usage to confirm no new memory issues

## References

- A* Pathfinding Project Documentation
- Unity Optimization Best Practices
- Data-Oriented Design Principles