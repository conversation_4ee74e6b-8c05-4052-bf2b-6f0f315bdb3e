# BTR Journal - AI Assistant Guide

## Overview
This guide provides instructions for AI assistants on how to properly create and maintain BTR Journal entries when requested by the user.

## System Configuration

### Base Path
```
j:\BTR U6 2025 RG\BTR_Journal\
```

### Folder Structure
```
BTR_Journal/
├── README.md                    # System overview
├── QUICK_START.md              # User quick reference
├── AI_ASSISTANT_GUIDE.md       # This file - AI instructions
├── create_journal_entry.py     # Automated creation script
├── Templates/                  # Entry templates
│   ├── bug_fix_template.md
│   ├── feature_template.md
│   └── debug_session_template.md
├── 2025/                       # Year-based organization
│   └── MM-MonthName/          # Month folders
└── Index/                      # Quick reference files
    ├── bug_fixes_index.md
    ├── features_index.md
    └── system_changes_index.md
```

## Entry Creation Process

### When User Requests Journal Entry

1. **Determine Entry Type**
   - **Bug Fix**: User reports fixing a problem or issue
   - **Feature**: User describes implementing new functionality
   - **Debug Session**: User describes investigating or troubleshooting

2. **Generate Filename**
   - Format: `YYYY-MM-DD_TYPE_description.md`
   - Date: Current date in ISO format
   - Type: `BUG`, `FEATURE`, or `DEBUG` (uppercase)
   - Description: Brief, underscore-separated summary

3. **Create Month Folder (if needed)**
   - Format: `2025/MM-MonthName/` (e.g., `2025/07-July/`)
   - Only create if it doesn't exist

4. **Use Appropriate Template**
   - Copy content from relevant template file
   - Replace timestamp placeholder with current ISO timestamp
   - Keep all template sections intact

5. **Update Index Files**
   - Add entry to relevant index file(s)
   - Update statistics and counts
   - Maintain chronological order

### Templates and Their Use Cases

#### Bug Fix Template (`bug_fix_template.md`)
Use when:
- User reports fixing a bug or issue
- Problem was identified and resolved
- System behavior was corrected

Key sections:
- Problem Description (symptoms, root cause, impact)
- Solution (approach, implementation details)
- Files Modified
- Testing and Verification

#### Feature Template (`feature_template.md`)
Use when:
- User describes implementing new functionality
- New capabilities were added to the system
- Enhancement or improvement was made

Key sections:
- Feature Overview (purpose, requirements, acceptance criteria)
- Design Decisions (architecture, technical approach)
- Implementation Details
- Testing and Documentation

#### Debug Session Template (`debug_session_template.md`)
Use when:
- User describes investigating an issue
- Troubleshooting process was documented
- Diagnostic tools were created or used

Key sections:
- Initial Problem and Hypothesis
- Investigation Process (tools used, steps taken)
- Key Findings and Resolution
- Lessons Learned

## Naming Conventions

### File Names
- **Date**: Always `YYYY-MM-DD` format
- **Type**: `BUG`, `FEATURE`, `DEBUG` (uppercase)
- **Description**: Brief, descriptive, underscore-separated
- **Examples**:
  - `2025-07-18_BUG_shooting_system_fix.md`
  - `2025-07-18_FEATURE_new_enemy_ai.md`
  - `2025-07-18_DEBUG_memory_leak_investigation.md`

### Month Folders
- **Format**: `MM-MonthName` (e.g., `07-July`, `12-December`)
- **Path**: `2025/MM-MonthName/`

## Index File Maintenance

### Bug Fixes Index (`bug_fixes_index.md`)
- Add new entries to the monthly table
- Update quick search statistics
- Add to "Recent Fixes" section
- Maintain chronological order

### Features Index (`features_index.md`)
- Add new entries to the monthly table
- Update status counts
- Track feature categories
- Note completion status

### System Changes Index (`system_changes_index.md`)
- Add all significant changes (bugs, features, refactors)
- Categorize by impact level
- Track system-wide modifications
- Document breaking changes

## Content Guidelines

### Timestamp Format
- Use ISO format with timezone: `YYYY-MM-DDTHH:MM:SS-04:00`
- Always use current timestamp when creating entries
- Replace template placeholder `YYYY-MM-DDTHH:MM:SS-TZ`

### Writing Style
- Be specific and detailed
- Include exact file paths and line numbers when relevant
- Use code blocks for code snippets
- Include before/after comparisons when applicable
- Use checkboxes for test cases and action items

### Required Information
- **Always include**: Date, type, summary, details, files modified
- **For bugs**: Problem description, root cause, solution, testing
- **For features**: Requirements, design decisions, implementation
- **For debug sessions**: Investigation process, findings, tools used

## Example Workflow

When user says: *"I fixed the shooting system bug where players couldn't shoot"*

1. **Identify**: This is a bug fix
2. **Create**: `2025-07-18_BUG_shooting_system_fix.md`
3. **Use**: `bug_fix_template.md` as base
4. **Fill**: All sections with user's information
5. **Update**: `bug_fixes_index.md` and `system_changes_index.md`
6. **Confirm**: Entry created and indexes updated

## Quality Checklist

Before completing journal entry creation:
- [ ] Filename follows naming convention
- [ ] Month folder exists or was created
- [ ] Template sections are complete
- [ ] Timestamp is current and correct
- [ ] Relevant index files are updated
- [ ] Code snippets are properly formatted
- [ ] File paths are accurate and complete

## Common Mistakes to Avoid

- Don't use generic descriptions like "fixed stuff"
- Don't skip the testing/verification sections
- Don't forget to update index files
- Don't use incorrect timestamp formats
- Don't create entries without proper categorization
- Don't omit file modification details

---
*This guide ensures consistent, high-quality journal entries that provide maximum value for future reference and team collaboration.*
