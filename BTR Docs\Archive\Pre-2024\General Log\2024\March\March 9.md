# March 9

Using <PERSON> to reorganize some classes

 Player Movement, 

Removed instance from Crosshair

Due to animation issues and Umodeler issues, Creature Testing 2 is a thing

DO NOT upgrade the unity version for this creature testing project if you dont wanna break everything!!

<PERSON> with better anims is in main project now. Still need to do mesh edits to make it more unique

Need to bring over eyes and other aspects for infinite track section