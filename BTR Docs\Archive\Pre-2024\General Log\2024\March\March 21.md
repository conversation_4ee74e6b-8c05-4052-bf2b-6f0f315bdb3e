# March 21

adjusting infinite prefab static shooters placement to affect rotation of them better

also adjusting generation frequency and placement to generally improve them

Minor improvements, needs more work

Building scene changing into Game Manager singleton class

Learning about Scriptable Objects in doing this 

Figured out tail issues, it had to do with where the player spline controller was being placed. tail adjusted correctly but controller was always being placed at the end + 2. Now this number is adjustable in the inspector

Basic EnemySnakeMid<PERSON>oss health figured out, with animations when getting hit and scene transition upon death 

Have a method figured out where I can call ‘Switch Scene’ in custom event when final wave occurs from enemy wave spawner, and it will initiate GameManager’s scene switching. 

Should I adjust midboss death so it doesnt call scene switching so directly? Unsure

Likely need to move the appearance of wave # text to the custom event call for a cleaner approach. 

SnakeMidBoss

Started adding attack - setup basics of animation

THinknig about attack patterns for bullet shots…

Use Object Particle Spawner or adapt Uni Bullet Hell into 3D?

Tempted by second solution

Experimenting with that a bit… but it seems like more of a pain. Not well designed. 

OPS may be the better bet. 

Just need the right particle patterns.