# June 17

Think Neon WHite - how to expand on mechanics?

Still caught up on this!

Need more enemy variations - help mix up the waves

Need varying behaviours / movement patterns

Maybe not all shoot bullets?

Need one hit enemies, at least at first. 

Narrative: You’re showing up in a sector - these are old data beings trying to communicate. 

You’re sending the communications back at them. They expect a response, so getting these sent message back corrupts them. THOUGHT you alter it when you lock on, then send it back and it confuses them.

They’re not real beings just mindless protocols. 

Think Reboot Internet ideas, more wild and pirate like. 

You’re a digital archaeologist, venturing to old parts of the web. Or maybe it’s religious studies? 

**According to QM, before you look inside the box, the cat is both in an alive state, and in a dead state, and the bomb both did and did not go off**
. It is only when you look inside an see either an alive or dead cat that the cat actually becomes either alive or dead. Prior to looking in, it was both at the same time.

Could the old data / old ways of doing things VS the new be a nod to quantum mechanics?

On a personal level, it touches on how what we believe to be true is truth. 

On Quantum Mechanics

"Okay, see this stick? If I swing it, it moves swoosh. If I hit it against something, it stops moving smack. If I let go of it, it falls down clatter. You're not very surprised by this, right? That's because everything in the world uses the same rules. About three hundred years ago, a guy named <PERSON> wrote down all the rules, and we call them the laws of physics. Scientists still use his versions of the laws for all sorts of stuff, but there are a couple places they found where things are a little bit different. One of them is for things that are very, very small. So if I break this stick in half crack, both halves work just like they did before, right? swoosh, smack, clatter And if I break one of the halves snap it does too. If you had a tiny saw and a microscope, you could keep making smaller and smaller sticks, and they would all work the same way, right? Well it it turns out that when things get small enough, smaller than things that are too small to see, they start to act a little bit weird. So imagine this stick is just one of those tiny tiny pieces inside the stick. If I throw it to you and you catch it, then someone uses a stick-finding machine, it might turn out to still be in my hand, or it might be in your hand like it would if it was a normal stick. Yeah, it's weird: scientists were really confused about this when they started seeing it, and a whole lot of them working together took about fifty years to get it right, because it's so strange. Eventually they figured out that the tiny stick, and everything else that small, is actually always in multiple places at once. So even though we think it's in my hand, it's actually also in my other hand, and already on the ground, and still in my hand but just a tiny bit to the side of where we thought it was. Even stranger, the stick is more in some of these places than it is in others: most might be in this one spot in my hand, but less in my other hand, and just a tiny bit on the ground. And it's all still the same stick. This is all really weird, but one thing about it is still perfectly normal: all of the places the stick is in still follow Newton's laws. If I drop the stick from my hand onto the stick on the ground, it'll stop when it hits the ground, and the pieces will add up, so most of the stick will be on the ground. (I think now would be a good point to mention that yes, a real conversation with a 4 year old wouldn't go like this. You'd have to stop and answer questions and re-explain pieces of it. I'm just proof-of-concepting this.) Okay, so remember when I said the stick could be in more than one place? It can also be going more than one speed! So some of the stick could be on the ground and not moving, and some could be in the middle of falling down, and some could be just starting to fall out of my hand so it's still moving slowly. Another very smart man, this one is named Werner Heisenberg, figured out that the more different places the stick is in, the less speeds it is moving in, and the more speeds it has, the less different places its in. So if all the tiny parts of everything are acting this strange, how is everything so normal when you get back to big things like us? Well, there is one more odd thing about tiny tiny things that I haven't told you yet. Say I have TWO tiny tiny sticks. You know how one can be in a whole bunch of places at once? Well it turns out that sometimes you have to take both sticks together to figure out how much is in any place. So maybe for most of the tiny sticks, stick one is in my hand and stick two is on the ground, and there's also some where stick two is in my hand, and stick one is on the ground. But, there's no sticks where both are in my hand at all, even though both sticks by themselves are at least a little in my hand. (This is where the kid will have the most questions, and also probably where anyone reading this is going to have questions, so ask away. Though tell me whether I'm allowed to use grown-up words like "particle" or I have to keep saying "tiny stick.") When how much of one tiny tiny thing is in one spot depends on how much of another tiny tiny thing is in another spot, scientists call those two things "entangled." That makes it sound like it's a special, weird case, but it's really the other way around. Scientists go through lots of trouble to get tiny things that aren't entangled so they can study them, but just about everything is entangled most of the time. All the tiny tiny pieces of stick in this actual stick are very entangled with each other. That's how big things like us and this stick don't seem like they're in more than one place at a time. The pieces of stick are all in a few places at once, but every different group of tiny sticks adds up to the big stick in my hand, even if the tiny pieces could be swapped around a little.”

Environment Assets - Sci Fi city Scape - kind of cool, could be useful!

THink about Dune and negative space for visual aesthetic - making the player feel small

Chart out a level narrative?

Need to work on player animations as well!

**Blender**

[https://www.youtube.com/watch?v=ij_y6rBawl0](https://www.youtube.com/watch?v=ij_y6rBawl0)

How to use Random Flow extension

Dodging - Take a risk to look and feel cool. 

What current mechanic could be implemnted as a doge? turning?

Panzer Dragoon Saga turned based combat - how does dodging work there?

I frames when turning?

Dodge release bullets? 

Need more attack / response modes

Attack mode 1 - gather up and shoot

Attack Mode 2 - gather as health / points

Previous idea was that some unlaunchabes had to be desryoed by rewind. 

Health as a TIME IS RUNNING OUT feature, need to grab some bullets to increase life

Adding a collect health mode

Need a different sound a FX for this!

Rebuild raycast from camera

Not working! Not sure why its such a pain to figure this out

Trying to figure out camera issue and fix thing most of day, didnt get much of anywhere!! Ugh

LookAtObject is breaking things? Not sure why