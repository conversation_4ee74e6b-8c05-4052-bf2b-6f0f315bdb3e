# Sept 20

Hang Line GDC talk

[Moving Mountains: Speedy Level Creation as a Desperate Indie Dev](https://www.youtube.com/watch?v=XK-yTqYAD-c)

Potential interesting strategies to game development 

[Fake Grimlock: Win Like Stupid](https://readwrite.com/fake-grimlock-win-like-stupid/)

Simple unique aspect to mechanic - build entire game around this

Building levels quickly

- automatic mesh generation
- applying noise to these meshes

Layering game mechanics

- Made a chart of various mechanics
    - Goats can kick, objects are destructible, what if goat can kick objects? This kind of approach
    - How can various currently isolated mechanics interact (layer) to give the game greater complexity? (Meaning more things can happen)

How to make a good level

- Tutorials
- Variety
- Theming

Tutorials for Mechanics

- How to teach player organically
    - Within a grouping, first level teaches mechanic, next few levels reinforces, then next levels push that mechanic further to its limits
    - Ex. Goats kicking itnroduced, then you encoutner many goats, then you get to levels where rocks can fall and crush goat, goat can hit something and kick it into something else, etc
- Made a colour coded chart of every mechanic, and all levels, and seeing which levels use which mechanics, to see the pacing and how much any of them are used
- Introduce mechanics faster at start of the game, later in the game not as necessary to move at that pace

Variety

- No single part of the level should repeat in the same way later
- Change from large spaces to tighter spaces
- Vary difficulty of sections of the level
- Main path in level, but have several side paths
- Create challenge through optional item pickups

Theming

- At least one unique and memorable element in each level
    - Why does this level exist? Does it add anything new to the current set of levels we have?
- Could a player describe the level to their friend without using its name?

Level building process that allows rapid iteration is very helpful!

Bioshock  - How to do linear level design

Conveyance - communicating with the player

Referenced script on TargetCube missing, fixed this

Investigating camera options for better reticle look around movement

Replaced camera with Cinemachine Free Look - this might work way better!

Comparing with RDR Mix and Jam file

Need to make spherical movement work - hard lock reticle ?

Adjust AimRotationSphere location to roughly camera position - much better!

Need to restrict left/right distance or adjust properties of reticle movement to fit this new movement setup

Adjusted a bit better now - still needs refinement! Getting there!