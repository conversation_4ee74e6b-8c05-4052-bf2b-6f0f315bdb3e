using UnityEngine;
using Unity.Mathematics;
using Unity.Jobs;
using Unity.Collections;
using Unity.Burst;
using Stylo.Epoch;
using System.Collections;
using FluffyUnderware.Curvy;
using FluffyUnderware.Curvy.Controllers;
using System;

namespace BTR
{
    [System.Serializable]
    public class ProjectileConfiguration
    {
        [Header("Prediction Settings")]
        [Range(0.5f, 2.0f)] public float predictionTimeMultiplier = 1.0f;
        [Range(1.0f, 3.0f)] public float predictionDistanceMultiplier = 1.5f;
        [Range(0.3f, 1.0f)] public float predictionSmoothingFactor = 0.5f;
        [Range(10f, 100f)] public float bulletSpeed = 50f;

        [Header("Targeting Distances")]
        [Range(8f, 20f)] public float directTargetingDistance = 12f;
        [Range(15f, 35f)] public float approachBlendDistance = 25f;
        [Range(5f, 15f)] public float closeRangeThreshold = 8f;

        [Header("Movement Settings")]
        [Range(90f, 270f)] public float maxTurnRate = 180f;
        [Range(0.3f, 0.9f)] public float velocityMaintainFactor = 0.7f;
        [Range(0.2f, 0.8f)] public float closeRangeTurnRateMultiplier = 0.4f;

        [Header("Curve Settings")]
        [Range(0.1f, 1.0f)] public float curveIntensity = 0.3f;
        [Range(15f, 60f)] public float maxCurveAngle = 30f;
        [Range(0.5f, 2.0f)] public float lateralOffsetMultiplier = 1.0f;
    }

    public partial class ProjectileJobSystem : MonoBehaviour, IDisposable
    {
        private const int MAX_PROJECTILES = 1000;
        private const float CLEANUP_THRESHOLD = 0.1f;
        private const float BASE_PREDICTION_TIME = 1.0f;
        private const float BASE_PREDICTION_DISTANCE = 1.5f;

        [SerializeField] private ProjectileConfiguration config;
        
        [Header("Epoch Time Integration")]
        [SerializeField, Tooltip("The key of the Epoch global clock to use for time scaling")]
        private string epochClockKey = "Global";

        private SplineController playerSplineController;
        private NativeArray<float> zoneConfig;

        private void OnValidate()
        {
            if (config == null) config = new ProjectileConfiguration();

            // Ensure distances maintain proper relationships
            config.approachBlendDistance = Mathf.Max(config.approachBlendDistance, config.directTargetingDistance + 5f);
            config.directTargetingDistance = Mathf.Max(config.directTargetingDistance, config.closeRangeThreshold + 4f);

            // Enforce more direct targeting at close range
            if (config.directTargetingDistance < 15f)
            {
                config.curveIntensity = Mathf.Min(config.curveIntensity, 0.4f);
                config.maxCurveAngle = Mathf.Min(config.maxCurveAngle, 35f);
            }
        }

        private void Awake()
        {
            if (config == null) config = new ProjectileConfiguration();
            InitializeSystem();
        }

        private void InitializeSystem()
        {
            if (isInitialized || isDisposed) return;

            try
            {
                // Initialize core arrays with proper error handling and size validation
                if (MAX_PROJECTILES <= 0)
                {
                    throw new System.ArgumentException("MAX_PROJECTILES must be greater than 0");
                }

                // Initialize core arrays with proper error handling
                positions = new NativeArray<float3>(MAX_PROJECTILES, Allocator.Persistent);
                rotations = new NativeArray<quaternion>(MAX_PROJECTILES, Allocator.Persistent);
                velocities = new NativeArray<float3>(MAX_PROJECTILES, Allocator.Persistent);
                targetPositions = new NativeArray<float3>(MAX_PROJECTILES, Allocator.Persistent);
                homingFlags = new NativeArray<int>(MAX_PROJECTILES, Allocator.Persistent);
                rotateSpeeds = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);
                bulletSpeeds = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);
                timeScales = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);
                lifetimes = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);
                activeFlags = new NativeArray<int>(MAX_PROJECTILES, Allocator.Persistent);
                zoneConfig = new NativeArray<float>(1, Allocator.Persistent);

                // Initialize curved trajectory arrays
                curveControlPoints = new NativeArray<float3>(MAX_PROJECTILES, Allocator.Persistent);
                curveIntensities = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);
                targetVelocities = new NativeArray<float3>(MAX_PROJECTILES, Allocator.Persistent);
                interceptTimes = new NativeArray<float>(MAX_PROJECTILES, Allocator.Persistent);

                // Initialize queue with validation
                availableSlots = new NativeQueue<int>(Allocator.Persistent);
                for (int i = MAX_PROJECTILES - 1; i >= 0; i--)
                {
                    availableSlots.Enqueue(i);
                }

                // Validate initialization
                if (!ValidateArrays())
                {
                    throw new System.InvalidOperationException("Array initialization validation failed");
                }

                isInitialized = true;

                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "System Initialization",
                        $"Successfully initialized with {MAX_PROJECTILES} slots",
                        Vector3.zero
                    );
                }
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "System Initialization Error",
                        e.ToString(),
                        Vector3.zero
                    );
                }
                SafeDispose();
                throw;
            }
        }

        private float3 GetTargetPosition(float3 currentPosition, float3 targetPosition, float distanceToTarget, int index)
        {
            float3 predictedPos = targetPositions[index];
            float straightLineDistance = config.directTargetingDistance * 0.5f;

            // Force direct targeting when very close
            if (distanceToTarget <= straightLineDistance)
            {
                return targetPosition;
            }
            else if (distanceToTarget <= config.directTargetingDistance)
            {
                // Strong bias towards direct targeting in close range
                float directBias = 1f - (distanceToTarget - straightLineDistance) / (config.directTargetingDistance - straightLineDistance);
                directBias = math.pow(directBias, 2f); // Exponential falloff for prediction influence
                return math.lerp(predictedPos, targetPosition, directBias);
            }
            else if (distanceToTarget <= config.approachBlendDistance)
            {
                float blendFactor = (distanceToTarget - config.directTargetingDistance) / (config.approachBlendDistance - config.directTargetingDistance);
                blendFactor = math.pow(blendFactor, 1.5f);
                return math.lerp(targetPosition, predictedPos, blendFactor * 0.7f);
            }

            return predictedPos;
        }

        // Public properties for radar integration
        public NativeArray<float3> Positions => positions;
        public NativeArray<int> HomingFlags => homingFlags;
        public NativeArray<int> ActiveFlags => activeFlags;

        // Core data arrays
        internal NativeArray<float3> positions;
        internal NativeArray<quaternion> rotations;
        internal NativeArray<float3> velocities;
        internal NativeArray<float3> targetPositions;
        internal NativeArray<int> homingFlags;
        internal NativeArray<float> rotateSpeeds;
        internal NativeArray<float> bulletSpeeds;
        internal NativeArray<float> timeScales;
        internal NativeArray<float> lifetimes;
        internal NativeArray<int> activeFlags;
        private NativeQueue<int> availableSlots;

        // New arrays for curved trajectory
        private NativeArray<float3> curveControlPoints;
        private NativeArray<float> curveIntensities;
        private NativeArray<float3> targetVelocities;
        private NativeArray<float> interceptTimes;

        internal JobHandle currentJobHandle;
        private bool isInitialized = false;
        private bool hasScheduledJob = false;
        private bool isClockInitialized = false;

        // Epoch timeline reference
        private EpochGlobalClock globalClock;

        private bool isDisposed = false;
        private readonly object disposeLock = new object();

        // Public property for job handle
        public JobHandle CurrentJobHandle => currentJobHandle;

        // Public interface methods
        public void UpdateProjectileMovementData(
            int index,
            Vector3 position,
            Quaternion rotation,
            Vector3 velocity,
            Vector3 targetPosition,
            bool homing,
            float rotateSpeed,
            float bulletSpeed,
            float timeScale,
            float lifetime
        )
        {
            if (!isInitialized || index < 0 || index >= MAX_PROJECTILES) return;

            // Complete any running jobs before modifying data
            CompleteProjectileUpdate();

            // Get predicted position for the target
            Vector3 predictedPosition = PredictPlayerPosition(targetPosition);

            // Calculate target velocity if we have a spline controller
            Vector3 targetVelocity = Vector3.zero;
            if (playerSplineController != null && homing)
            {
                // Get the current position and direction from the spline controller
                float currentTF = playerSplineController.RelativePosition;
                CurvySpline spline = playerSplineController.Spline;

                if (spline != null && spline.IsInitialized)
                {
                    // Get the tangent at the current position
                    Vector3 tangent = spline.GetTangentFast(currentTF);
                    // Apply the movement direction (forward or backward)
                    tangent *= playerSplineController.MovementDirection.ToInt();
                    // Scale by speed to get velocity
                    targetVelocity = tangent * playerSplineController.Speed;
                }
            }

            positions[index] = position;
            rotations[index] = rotation;
            velocities[index] = velocity;
            targetPositions[index] = predictedPosition;
            homingFlags[index] = homing ? 1 : 0;
            rotateSpeeds[index] = rotateSpeed;
            bulletSpeeds[index] = bulletSpeed;
            timeScales[index] = timeScale;

            // Update curved trajectory data
            targetVelocities[index] = targetVelocity;
            curveIntensities[index] = config.curveIntensity;
            interceptTimes[index] = 0f; // Will be calculated in the job

            if (lifetime > 0)
            {
                lifetimes[index] = lifetime;
            }

            activeFlags[index] = 1;
        }

        public bool IsProjectileActive(int index)
        {
            if (!isInitialized || index < 0 || index >= MAX_PROJECTILES) return false;
            return activeFlags[index] == 1;
        }

        public Vector3 GetProjectilePosition(int index)
        {
            CompleteProjectileUpdate();
            if (index < 0 || index >= MAX_PROJECTILES) return Vector3.zero;
            return positions[index];
        }

        public Quaternion GetProjectileRotation(int index)
        {
            CompleteProjectileUpdate();
            if (index < 0 || index >= MAX_PROJECTILES) return Quaternion.identity;
            return rotations[index];
        }

        public Vector3 GetProjectileVelocity(int index)
        {
            CompleteProjectileUpdate();
            if (index < 0 || index >= MAX_PROJECTILES) return Vector3.zero;
            return velocities[index];
        }

        public float GetProjectileLifetime(int index)
        {
            CompleteProjectileUpdate();
            if (index < 0 || index >= MAX_PROJECTILES) return 0f;
            return lifetimes[index];
        }

        public void DeactivateProjectile(int index)
        {
            if (!isInitialized || index < 0 || index >= MAX_PROJECTILES) return;

            // Complete any running jobs before modifying data
            CompleteProjectileUpdate();

            // Clear all state for this projectile
            activeFlags[index] = 0;
            homingFlags[index] = 0;  // Ensure homing flag is cleared
            positions[index] = float3.zero;
            rotations[index] = quaternion.identity;
            velocities[index] = float3.zero;
            targetPositions[index] = float3.zero;
            rotateSpeeds[index] = 0f;
            bulletSpeeds[index] = 0f;
            timeScales[index] = 1f;
            lifetimes[index] = 0f;

            // Clear curved trajectory data
            curveControlPoints[index] = float3.zero;
            curveIntensities[index] = 0f;
            targetVelocities[index] = float3.zero;
            interceptTimes[index] = 0f;

            // Return the index to the available slots queue
            availableSlots.Enqueue(index);
        }

        public void OnProjectileReturned(int index)
        {
            DeactivateProjectile(index);
        }

        /// <summary>
        /// Schedules the projectile movement and lifetime update job.
        /// This should be called every frame with Epoch-scaled delta time.
        /// </summary>
        /// <param name="deltaTime">Epoch-scaled delta time (globalClock.DeltaTime or globalClock.FixedDeltaTime)
        /// which already includes all time scaling from the Epoch time system hierarchy</param>
        public void ScheduleProjectileMovement(float deltaTime)
        {
            if (!isInitialized || config == null) return;

            // Complete previous job before scheduling a new one
            CompleteProjectileUpdate();

            // Update zone configuration before scheduling job
            UpdateZoneConfiguration();

            // Get the current global time scale from Epoch's computed values
            float globalTimeScale = globalClock != null ? globalClock.TimeScale : 1f;

            var job = new ProjectileUpdateJob
            {
                deltaTime = deltaTime,
                positions = positions,
                rotations = rotations,
                velocities = velocities,
                targetPositions = targetPositions,
                homingFlags = homingFlags,
                rotateSpeeds = rotateSpeeds,
                bulletSpeeds = bulletSpeeds,
                timeScales = timeScales,
                lifetimes = lifetimes,
                activeFlags = activeFlags,
                globalTimeScale = globalTimeScale,
                zoneConfig = zoneConfig,
                curveControlPoints = curveControlPoints,
                curveIntensities = curveIntensities,
                targetVelocities = targetVelocities,
                interceptTimes = interceptTimes,

                // Pass configuration values
                directTargetingDistance = config.directTargetingDistance,
                approachBlendDistance = config.approachBlendDistance,
                closeRangeThreshold = config.closeRangeThreshold,
                closeRangeTurnRateMultiplier = config.closeRangeTurnRateMultiplier,
                curveIntensity = config.curveIntensity,
                maxCurveAngle = config.maxCurveAngle,
                maxTurnRate = config.maxTurnRate,
                velocityMaintainFactor = config.velocityMaintainFactor
            };

            currentJobHandle = job.Schedule(MAX_PROJECTILES, 64);
            hasScheduledJob = true;
        }

        [BurstCompile]
        public struct ProjectileUpdateJob : IJobParallelFor
        {
            public float deltaTime;
            public float globalTimeScale;
            public NativeArray<float3> positions;
            public NativeArray<quaternion> rotations;
            public NativeArray<float3> velocities;
            public NativeArray<float3> targetPositions;
            public NativeArray<int> homingFlags;
            public NativeArray<float> rotateSpeeds;
            public NativeArray<float> bulletSpeeds;
            public NativeArray<float> timeScales;
            public NativeArray<float> lifetimes;
            public NativeArray<int> activeFlags;
            [ReadOnly] public NativeArray<float> zoneConfig;
            public NativeArray<float3> curveControlPoints;
            public NativeArray<float> curveIntensities;
            public NativeArray<float3> targetVelocities;
            public NativeArray<float> interceptTimes;

            // Add configuration data
            public float directTargetingDistance;
            public float approachBlendDistance;
            public float closeRangeThreshold;
            public float closeRangeTurnRateMultiplier;
            public float curveIntensity;
            public float maxCurveAngle;
            public float maxTurnRate;
            public float velocityMaintainFactor;

            public void Execute(int index)
            {
                if (activeFlags[index] != 1) return;

                float3 position = positions[index];
                float3 velocity = velocities[index];
                float3 targetPos = targetPositions[index];
                bool isHoming = homingFlags[index] == 1;
                float speed = bulletSpeeds[index];
                float timeScale = timeScales[index] * globalTimeScale;

                lifetimes[index] -= deltaTime * timeScale;
                if (lifetimes[index] <= 0f)
                {
                    activeFlags[index] = 0;
                    return;
                }

                if (isHoming)
                {
                    float3 toTarget = targetPos - position;
                    float distanceToTarget = math.length(toTarget);
                    float3 dirToTarget = SafeNormalize(toTarget, math.forward());

                    float3 currentDir = SafeNormalize(velocity, math.forward());
                    float approachAngle = math.degrees(math.acos(math.dot(currentDir, dirToTarget)));

                    // Calculate desired velocity with stronger direct targeting
                    float3 desiredVelocity;
                    if (distanceToTarget <= closeRangeThreshold)
                    {
                        // Direct path at close range
                        desiredVelocity = dirToTarget * speed * 1.2f; // Slight speed boost for final approach
                    }
                    else
                    {
                        // Curved approach at longer range
                        float curveAmount = math.min(curveIntensity * (approachAngle / maxCurveAngle), 0.5f);
                        float3 right = math.cross(math.up(), dirToTarget);
                        float3 curvedDir = math.normalize(dirToTarget + right * curveAmount);
                        desiredVelocity = curvedDir * speed;
                    }

                    // Blend based on distance
                    float blendFactor = math.saturate(distanceToTarget / approachBlendDistance);
                    float turnRate = maxTurnRate * (1f + (1f - blendFactor) * closeRangeTurnRateMultiplier);

                    // Apply velocity changes
                    float3 newVelocity = math.lerp(
                        velocity,
                        desiredVelocity,
                        math.saturate(deltaTime * turnRate * (1f - velocityMaintainFactor))
                    );

                    // Update position and velocity
                    position += newVelocity * deltaTime * timeScale;
                    velocity = newVelocity;

                    // Update rotation to face velocity direction
                    if (math.lengthsq(velocity) > 0.001f)
                    {
                        float3 forward = math.normalize(velocity);
                        float3 up = new float3(0, 1, 0);
                        float3 rightVector = math.normalize(math.cross(up, forward));
                        up = math.cross(forward, rightVector);
                        rotations[index] = quaternion.LookRotation(forward, up);
                    }
                }
                else
                {
                    // Non-homing projectile logic
                    /* // Burst-disabled log: Cannot use Debug.Log directly in a Burst-compiled job.
                    if (ProjectileDebugHelper.EnableDetailedDirectionLogging &&
                        ProjectileDebugHelper.LoggedMessagesCount < ProjectileDebugHelper.MaxTotalLogMessages &&
                        index < ProjectileDebugHelper.MaxUniqueProjectilesToLog * 2)
                    {
                        Debug.Log($"[JobExecuteNonHoming] Index: {index}, Using Velocity: {velocities[index].ToString()}, Current Stored Rotation: {rotations[index].ToString()}");
                        ProjectileDebugHelper.LoggedMessagesCount++;
                    }
                    */
                    position += velocity * deltaTime * timeScale;
                }

                positions[index] = position;
                velocities[index] = velocity;
                interceptTimes[index] += deltaTime;
            }

            float3 SafeNormalize(float3 vector, float3 fallback)
            {
                float lengthSq = math.lengthsq(vector);
                if (lengthSq > 1e-6f)
                {
                    return vector * math.rsqrt(lengthSq);
                }
                return fallback;
            }
        }

        private void OnEnable()
        {
            if (config == null) config = new ProjectileConfiguration();
            if (!isInitialized)
            {
                InitializeSystem();
            }
        }

        private void Start()
        {
            if (config == null) config = new ProjectileConfiguration();
            StartCoroutine(InitializeEpochClock());

            // Find and store reference to player's SplineController
            GameObject playerPlane = GameObject.FindGameObjectWithTag("PlayerPlane");
            if (playerPlane != null)
            {
                playerSplineController = playerPlane.GetComponent<SplineController>();
                if (playerSplineController == null)
                {
                    Debug.LogError("[ProjectileJobSystem] PlayerPlane does not have a SplineController component!");
                }
            }
            else
            {
                Debug.LogError("[ProjectileJobSystem] Could not find GameObject with tag 'PlayerPlane'!");
            }
        }

        private IEnumerator InitializeEpochClock()
        {
            // Wait a frame to ensure Epoch is initialized
            yield return null;

            int retryCount = 0;
            while (retryCount < 3)
            {
                try
                {
                    // Find the EpochGlobalClock component directly (matching PlayerTimeControl approach)
                    EpochGlobalClock[] globalClocks = FindObjectsByType<EpochGlobalClock>(FindObjectsSortMode.None);
                    foreach (var clock in globalClocks)
                    {
                        if (clock.clockKey == epochClockKey)
                        {
                            globalClock = clock;
                            break;
                        }
                    }

                    if (globalClock != null)
                    {
                        if (ProjectileLogger.Instance != null)
                        {
                            ProjectileLogger.Instance.LogJobSystem(
                                "Epoch Clock",
                                $"Successfully connected to Epoch clock '{epochClockKey}'",
                                Vector3.zero
                            );
                        }
                        isClockInitialized = true;
                        yield break;
                    }
                }
                catch (System.Exception e)
                {
                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogJobSystem(
                            "Epoch Clock Error",
                            $"Attempt {retryCount + 1}/3: Failed to get Epoch clock '{epochClockKey}': {e.Message}",
                            Vector3.zero
                        );
                    }
                }

                retryCount++;
                yield return new WaitForSeconds(0.1f);
            }

            if (ProjectileLogger.Instance != null)
            {
                ProjectileLogger.Instance.LogJobSystem(
                    "Epoch Clock Error",
                    $"Failed to initialize Epoch clock '{epochClockKey}' after 3 attempts. Time scaling will not work correctly.",
                    Vector3.zero
                );
            }
            globalClock = null;
            isClockInitialized = true; // Still mark as initialized so we can proceed with default time scale
        }

        private void OnDisable()
        {
            SafeDispose();
        }

        private void OnDestroy()
        {
            SafeDispose();
        }

        public void Dispose()
        {
            SafeDispose();
        }

        private void SafeDispose()
        {
            if (isDisposed) return;

            lock (disposeLock)
            {
                if (isDisposed) return;

                try
                {
                    // Complete any running jobs before disposal
                    CompleteProjectileUpdate();

                    // Dispose core arrays
                    SafeDispose(ref positions);
                    SafeDispose(ref rotations);
                    SafeDispose(ref velocities);
                    SafeDispose(ref targetPositions);
                    SafeDispose(ref homingFlags);
                    SafeDispose(ref rotateSpeeds);
                    SafeDispose(ref bulletSpeeds);
                    SafeDispose(ref timeScales);
                    SafeDispose(ref lifetimes);
                    SafeDispose(ref activeFlags);
                    SafeDispose(ref zoneConfig);

                    // Dispose curved trajectory arrays
                    SafeDispose(ref curveControlPoints);
                    SafeDispose(ref curveIntensities);
                    SafeDispose(ref targetVelocities);
                    SafeDispose(ref interceptTimes);

                    // Dispose queue
                    if (availableSlots.IsCreated)
                    {
                        availableSlots.Dispose();
                    }

                    isInitialized = false;
                    isDisposed = true;

                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogJobSystem(
                            "System Disposal",
                            "Successfully disposed all resources",
                            Vector3.zero
                        );
                    }
                }
                catch (Exception e)
                {
                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogJobSystem(
                            "System Disposal Error",
                            e.ToString(),
                            Vector3.zero
                        );
                    }
                    throw;
                }
            }
        }

        private void SafeDispose<T>(ref NativeArray<T> array) where T : struct
        {
            if (array.IsCreated)
            {
                try
                {
                    array.Dispose();
                }
                catch (Exception e)
                {
                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogJobSystem(
                            "Array Disposal Error",
                            e.Message,
                            Vector3.zero
                        );
                    }
                }
            }
        }

        public int GetNextAvailableSlot()
        {
            if (availableSlots.Count > 0)
            {
                return availableSlots.Dequeue();
            }
            return -1;
        }

        public void ReleaseSlot(int index)
        {
            if (index >= 0 && index < MAX_PROJECTILES)
            {
                activeFlags[index] = 0;
                availableSlots.Enqueue(index);
            }
        }

        private void FixedUpdate()
        {
            if (!isInitialized || !isClockInitialized || isDisposed) return;

            try
            {
                // Use Epoch's fixed delta time which already includes all time scaling
                float epochFixedDeltaTime = globalClock != null ? globalClock.FixedDeltaTime : Time.fixedDeltaTime;
                ScheduleProjectileMovement(epochFixedDeltaTime);
                CompleteProjectileUpdate();
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "FixedUpdate Error",
                        e.Message,
                        Vector3.zero
                    );
                }
            }
        }

        public void CompleteProjectileUpdate()
        {
            if (!isInitialized || !hasScheduledJob) return;

            try
            {
                currentJobHandle.Complete();
                UpdateTransforms();
                hasScheduledJob = false;
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "Job Completion Error",
                        e.Message,
                        Vector3.zero
                    );
                }
            }
        }

        public void Execute()
        {
            if (!isInitialized || isDisposed) return;

            try
            {
                if (hasScheduledJob)
                {
                    CompleteProjectileUpdate();
                }

                // Use Epoch's delta time which already includes all time scaling
                float epochDeltaTime = globalClock != null ? globalClock.DeltaTime : Time.deltaTime;
                ScheduleProjectileMovement(epochDeltaTime);
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "Execute Error",
                        e.Message,
                        Vector3.zero
                    );
                }
            }
        }

        private void UpdateTransforms()
        {
            if (!isInitialized || isDisposed) return;

            try
            {
                // Update transforms for all active projectiles
                for (int i = 0; i < MAX_PROJECTILES; i++)
                {
                    if (activeFlags[i] == 1)
                    {
                        var projectile = ProjectileManager.Instance.GetProjectileById(i);
                        if (projectile != null)
                        {
                            projectile.Transform.position = new Vector3(positions[i].x, positions[i].y, positions[i].z);
                            projectile.Transform.rotation = new Quaternion(rotations[i].value.x, rotations[i].value.y, rotations[i].value.z, rotations[i].value.w);
                            if (projectile.Rigidbody != null)
                            {
                                projectile.Rigidbody.linearVelocity = new Vector3(velocities[i].x, velocities[i].y, velocities[i].z);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "Transform Update Error",
                        e.Message,
                        Vector3.zero
                    );
                }
            }
        }

        private void Update()
        {
            if (!isInitialized || isDisposed) return;

            try
            {
                Execute();
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "Update Error",
                        e.Message,
                        Vector3.zero
                    );
                }
            }
        }

        private void UpdateZoneConfiguration()
        {
            if (ProjectileZoneManager.Instance != null)
            {
                var zoneManager = ProjectileZoneManager.Instance;
                zoneConfig[0] = zoneManager.AllowedShootingAngle;
            }
        }

        // Add method to predict player's future position
        private Vector3 PredictPlayerPosition(Vector3 currentPlayerPos)
        {
            try
            {
                if (playerSplineController == null || !playerSplineController.IsReady || playerSplineController.Spline == null)
                {
                    if (ProjectileLogger.Instance != null)
                    {
                        ProjectileLogger.Instance.LogJobSystem(
                            "Player Position Prediction",
                            "Failed to predict player position: Spline controller not ready",
                            currentPlayerPos
                        );
                    }
                    return currentPlayerPos;
                }

                CurvySpline spline = playerSplineController.Spline;
                float playerSpeed = playerSplineController.Speed;
                float currentTF = playerSplineController.RelativePosition;

                // Get current player position and direction
                Vector3 playerPos = spline.transform.TransformPoint(spline.Interpolate(currentTF));
                Vector3 playerDir = spline.transform.TransformDirection(spline.GetTangent(currentTF)).normalized;

                // Calculate time until intersection using law of cosines
                Vector3 toPlayer = playerPos - currentPlayerPos;
                float distanceToPlayer = toPlayer.magnitude;
                Vector3 toPlayerDir = toPlayer / distanceToPlayer;

                // Calculate angle between player's movement and line to projectile
                float cosAngle = Vector3.Dot(playerDir, toPlayerDir);
                float angle = Mathf.Acos(cosAngle);

                // Calculate speed ratio to determine how far ahead to look
                float speedRatio = playerSpeed / config.bulletSpeed;
                float lookAheadMultiplier = Mathf.Lerp(2.0f, 5.0f, speedRatio); // Look much further ahead for slower projectiles

                // Law of cosines: c² = a² + b² - 2ab*cos(C)
                // c = projectileSpeed * t
                // b = playerSpeed * t
                // Therefore: (projectileSpeed * t)² = distanceToPlayer² + (playerSpeed * t)² - 2 * distanceToPlayer * playerSpeed * t * cos(angle)

                // Solve quadratic equation: at² + bt + c = 0
                float a = config.bulletSpeed * config.bulletSpeed - playerSpeed * playerSpeed;
                float b = 2 * distanceToPlayer * playerSpeed * cosAngle;
                float c = -distanceToPlayer * distanceToPlayer;

                // Quadratic formula: t = (-b ± √(b² - 4ac)) / (2a)
                float discriminant = b * b - 4 * a * c;
                if (discriminant < 0)
                {
                    // No solution - player is moving away faster than projectile can catch up
                    return currentPlayerPos;
                }

                float t = (-b + Mathf.Sqrt(discriminant)) / (2 * a);
                if (t < 0)
                {
                    // Negative time - use the other solution
                    t = (-b - Mathf.Sqrt(discriminant)) / (2 * a);
                }

                // Calculate how far the player will travel in this time
                float distancePlayerTravels = playerSpeed * t * lookAheadMultiplier;

                // Get current curvature to adjust prediction
                float nextTF = Mathf.Min(1f, currentTF + 0.01f);
                Vector3 currentTangent = spline.GetTangent(currentTF);
                Vector3 nextTangent = spline.GetTangent(nextTF);
                float curvature = Vector3.Angle(currentTangent, nextTangent) / 0.01f;

                // Adjust prediction distance based on curvature
                float curvatureMultiplier = 1f + Mathf.Abs(curvature) * 0.5f;
                distancePlayerTravels *= curvatureMultiplier;

                // Calculate the predicted position on the spline
                float predictedTF = currentTF + (distancePlayerTravels / spline.Length);

                // Handle closed splines
                if (spline.Closed)
                {
                    predictedTF = predictedTF % 1f;
                }
                else
                {
                    predictedTF = Mathf.Clamp01(predictedTF);
                }

                // Get the predicted world position
                Vector3 predictedPos = spline.transform.TransformPoint(spline.Interpolate(predictedTF));

                return predictedPos;
            }
            catch (Exception e)
            {
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogJobSystem(
                        "Player Position Prediction Error",
                        e.Message,
                        currentPlayerPos
                    );
                }
                return currentPlayerPos;
            }
        }

        private bool ValidateArrays()
        {
            if (!positions.IsCreated || !rotations.IsCreated || !velocities.IsCreated ||
                !targetPositions.IsCreated || !homingFlags.IsCreated || !rotateSpeeds.IsCreated ||
                !bulletSpeeds.IsCreated || !timeScales.IsCreated || !lifetimes.IsCreated ||
                !activeFlags.IsCreated || !zoneConfig.IsCreated || !curveControlPoints.IsCreated ||
                !curveIntensities.IsCreated || !targetVelocities.IsCreated || !interceptTimes.IsCreated ||
                !availableSlots.IsCreated)
            {
                return false;
            }
            return true;
        }
    }
}