using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using BTR;

#if URP_INSTALLED
using Stylo.Flux.Universal;
#endif

namespace BTR
{
    /// <summary>
    /// FluxEffectController - Modern replacement for JPG bitcrunching using Stylo Flux system
    /// Provides centralized control over Flux datamoshing effects with presets and dynamic intensity control
    /// </summary>
    public class FluxEffectController : MonoBehaviour
    {
        public static FluxEffectController Instance { get; private set; }

        [Header("Flux System Configuration")]
        [SerializeField]
        private Volume globalVolume;
        
        [SerializeField]
        private bool enableDebugLogs = true;

        [Header("Effect Control")]
        [SerializeField, Range(0f, 1f)]
        private float masterIntensity = 0.35f;
        
        [SerializeField]
        private bool enableEffectOnStart = false;

        [Header("Flux Presets")]
        [SerializeField]
        private FluxPresetType defaultPreset = FluxPresetType.VHSDatamosh;

        // Flux effect reference
#if URP_INSTALLED
        private FluxEffect fluxEffect;
#endif

        public enum FluxPresetType
        {
            VHSDatamosh,
            DigitalGlitch,
            SubtleCorruption,
            IntenseTrailing,
            CompressionArtifacts,
            Custom
        }

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;
            InitializeFluxEffect();
        }

        private void Start()
        {
            if (enableEffectOnStart)
            {
                ApplyPreset(defaultPreset);
                SetIntensity(masterIntensity);
            }
        }

        private void InitializeFluxEffect()
        {
            // Check if URP is actually installed by looking for URP types
            bool isURPInstalled = IsURPInstalled();
            
            if (!isURPInstalled)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] URP not detected. Flux effects disabled. Install Universal Render Pipeline to enable visual effects.");
                }
                return;
            }

#if URP_INSTALLED
            // Find or create a Volume in the scene
            Volume volume = FindFirstObjectByType<Volume>();
            if (volume == null)
            {
                GameObject volumeObject = new GameObject("Global Volume");
                volume = volumeObject.AddComponent<Volume>();
                volume.isGlobal = true;
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Created new Global Volume for Flux effects");
                }
            }

            // Ensure the volume has a profile
            if (volume.profile != null)
            {
                // Try to find existing FluxEffect in the profile
                if (volume.profile.TryGet<FluxEffect>(out fluxEffect))
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[{GetType().Name}] Found existing FluxEffect in Volume profile");
                    }
                }
                else
                {
                    // Add FluxEffect to the profile
                    fluxEffect = volume.profile.Add<FluxEffect>();
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[{GetType().Name}] Added FluxEffect to Volume profile");
                    }
                }
            }
            else
            {
                if (enableDebugLogs)
                {
                    Debug.LogError($"[{GetType().Name}] Volume has no profile assigned. Please assign a VolumeProfile.");
                }
            }
#else
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[{GetType().Name}] URP compiler directive not defined. Flux effects disabled.");
            }
#endif

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Flux effect system initialization complete");
            }
        }
        
        /// <summary>
        /// Checks if URP is installed by looking for URP-specific types
        /// </summary>
        private bool IsURPInstalled()
        {
            try
            {
                // Try to find URP-specific types
                var volumeType = System.Type.GetType("UnityEngine.Rendering.Volume, Unity.RenderPipelines.Core");
                var urpAssetType = System.Type.GetType("UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset, Unity.RenderPipelines.Universal");
                
                return volumeType != null && urpAssetType != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the current effect intensity
        /// </summary>
        public float GetCurrentIntensity()
        {
#if URP_INSTALLED
            if (fluxEffect != null)
            {
                return fluxEffect.EffectIntensity.value;
            }
#endif
            return 0f;
        }

        /// <summary>
        /// Sets the effect intensity
        /// </summary>
        public void SetIntensity(float intensity)
        {
            intensity = Mathf.Clamp01(intensity);
            masterIntensity = intensity;

#if URP_INSTALLED
            if (fluxEffect != null)
            {
                fluxEffect.EffectIntensity.value = intensity;
                fluxEffect.EffectIntensity.overrideState = intensity > 0f;

                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Set Flux intensity to {intensity:F2}");
                }
            }
#endif
        }

        /// <summary>
        /// Applies a preset configuration to the Flux effect
        /// </summary>
        public void ApplyPreset(FluxPresetType presetType)
        {
#if URP_INSTALLED
            if (fluxEffect == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] FluxEffect not initialized. Cannot apply preset.");
                }
                return;
            }

            switch (presetType)
            {
                case FluxPresetType.VHSDatamosh:
                    ApplyVHSDatamoshPreset();
                    break;
                case FluxPresetType.DigitalGlitch:
                    ApplyDigitalGlitchPreset();
                    break;
                case FluxPresetType.SubtleCorruption:
                    ApplySubtleCorruptionPreset();
                    break;
                case FluxPresetType.IntenseTrailing:
                    ApplyIntenseTrailingPreset();
                    break;
                case FluxPresetType.CompressionArtifacts:
                    ApplyCompressionArtifactsPreset();
                    break;
                case FluxPresetType.Custom:
                    // Don't modify settings for custom preset
                    break;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Applied {presetType} preset to Flux effect");
            }
#endif
        }

#if URP_INSTALLED
        private void ApplyVHSDatamoshPreset()
        {
            // Classic VHS-style datamoshing
            fluxEffect.EffectIntensity.value = 0.4f;
            fluxEffect.ColorCrunch.value = 0.6f;
            fluxEffect.Downscaling.value = 8;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._8x8;
            fluxEffect.MotionAmplification.value = 2.5f;
            fluxEffect.TrailIntensity.value = 1.8f;
            fluxEffect.TrailSmoothness.value = 0.3f;
            fluxEffect.ReprojectBaseNoise.value = 0.15f;
            fluxEffect.ErrorAccumulation.value = 0.4f;
            
            // Enable override states
            SetOverrideStates(true);
        }

        private void ApplyDigitalGlitchPreset()
        {
            // Modern digital glitch effects
            fluxEffect.EffectIntensity.value = 0.5f;
            fluxEffect.ColorCrunch.value = 0.8f;
            fluxEffect.Downscaling.value = 6;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._4x4;
            fluxEffect.MotionAmplification.value = 4.0f;
            fluxEffect.TrailIntensity.value = 2.5f;
            fluxEffect.TrailSmoothness.value = 0.7f;
            fluxEffect.ChromaCorruption.value = 0.3f;
            fluxEffect.DCTCorruption.value = 0.2f;
            
            SetOverrideStates(true);
        }

        private void ApplySubtleCorruptionPreset()
        {
            // Subtle background corruption
            fluxEffect.EffectIntensity.value = 0.2f;
            fluxEffect.ColorCrunch.value = 0.3f;
            fluxEffect.Downscaling.value = 10;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._16x16;
            fluxEffect.MotionAmplification.value = 1.5f;
            fluxEffect.TrailIntensity.value = 1.0f;
            fluxEffect.TrailSmoothness.value = 0.8f;
            fluxEffect.ReprojectBaseNoise.value = 0.05f;
            
            SetOverrideStates(true);
        }

        private void ApplyIntenseTrailingPreset()
        {
            // Heavy pixel trailing effects
            fluxEffect.EffectIntensity.value = 0.6f;
            fluxEffect.ColorCrunch.value = 0.4f;
            fluxEffect.Downscaling.value = 5;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._8x8;
            fluxEffect.MotionAmplification.value = 6.0f;
            fluxEffect.TrailIntensity.value = 4.0f;
            fluxEffect.TrailSmoothness.value = 0.2f;
            fluxEffect.TrailDuration.value = 0.8f;
            fluxEffect.ReprojectLengthInfluence.value = 0.3f;
            
            SetOverrideStates(true);
        }

        private void ApplyCompressionArtifactsPreset()
        {
            // Authentic compression artifacts
            fluxEffect.EffectIntensity.value = 0.45f;
            fluxEffect.ColorCrunch.value = 0.7f;
            fluxEffect.Downscaling.value = 7;
            fluxEffect.BlockSize.value = FluxEffect._BlockSize._16x16;
            fluxEffect.KeyframeResetRate.value = 0.05f;
            fluxEffect.MotionVectorCorruption.value = 0.6f;
            fluxEffect.ErrorAccumulation.value = 0.5f;
            fluxEffect.DCTCorruption.value = 0.4f;
            fluxEffect.ChromaCorruption.value = 0.2f;
            
            SetOverrideStates(true);
        }

        private void SetOverrideStates(bool enabled)
        {
            fluxEffect.EffectIntensity.overrideState = enabled;
            fluxEffect.ColorCrunch.overrideState = enabled;
            fluxEffect.Downscaling.overrideState = enabled;
            fluxEffect.BlockSize.overrideState = enabled;
            fluxEffect.MotionAmplification.overrideState = enabled;
            fluxEffect.TrailIntensity.overrideState = enabled;
            fluxEffect.TrailSmoothness.overrideState = enabled;
            fluxEffect.ReprojectBaseNoise.overrideState = enabled;
        }
#endif

        /// <summary>
        /// Enables the Flux effect
        /// </summary>
        public void EnableEffect()
        {
            SetIntensity(masterIntensity > 0f ? masterIntensity : 0.35f);
        }

        /// <summary>
        /// Disables the Flux effect
        /// </summary>
        public void DisableEffect()
        {
            SetIntensity(0f);
        }

        /// <summary>
        /// Toggles the Flux effect on/off
        /// </summary>
        public void ToggleEffect()
        {
            if (GetCurrentIntensity() > 0f)
            {
                DisableEffect();
            }
            else
            {
                EnableEffect();
            }
        }

        /// <summary>
        /// Smoothly transitions intensity over time
        /// </summary>
        public void TransitionIntensity(float targetIntensity, float duration)
        {
            StartCoroutine(TransitionIntensityCoroutine(targetIntensity, duration));
        }

        private IEnumerator TransitionIntensityCoroutine(float targetIntensity, float duration)
        {
            float startIntensity = GetCurrentIntensity();
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                float currentIntensity = Mathf.Lerp(startIntensity, targetIntensity, progress);
                SetIntensity(currentIntensity);
                yield return null;
            }

            SetIntensity(targetIntensity);
        }

        /// <summary>
        /// Validates and auto-fixes Flux parameters
        /// </summary>
        public void ValidateAndFixParameters()
        {
#if URP_INSTALLED
            if (fluxEffect != null)
            {
                string warnings = fluxEffect.ValidateParameters();
                if (!string.IsNullOrEmpty(warnings) && enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] Parameter warnings:\n{warnings}");
                }

                string fixes = fluxEffect.AutoFixParameters();
                if (!string.IsNullOrEmpty(fixes) && enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Auto-fixes applied:\n{fixes}");
                }
            }
#endif
        }

        /// <summary>
        /// Gets the current Flux effect component (for advanced usage)
        /// </summary>
        public object GetFluxEffect()
        {
#if URP_INSTALLED
            return fluxEffect;
#else
            return null;
#endif
        }

        private void OnValidate()
        {
            // Apply intensity changes in editor
            if (Application.isPlaying)
            {
                SetIntensity(masterIntensity);
            }
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [ContextMenu("Apply VHS Datamosh Preset")]
        private void ApplyVHSPresetFromMenu()
        {
            ApplyPreset(FluxPresetType.VHSDatamosh);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [ContextMenu("Apply Digital Glitch Preset")]
        private void ApplyDigitalGlitchPresetFromMenu()
        {
            ApplyPreset(FluxPresetType.DigitalGlitch);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [ContextMenu("Validate Parameters")]
        private void ValidateParametersFromMenu()
        {
            ValidateAndFixParameters();
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        [ContextMenu("Toggle Effect")]
        private void ToggleEffectFromMenu()
        {
            ToggleEffect();
        }
    }
}
