---
title: The Unity HACK that the PROS know
tags: [Unity, PlayerLoop, Performance, Systems]
date: 2025-01-20
---

# [[The Unity HACK that the PROS know]]

## [[Overview]]
This guide covers advanced techniques for working with Unity's Player Loop, enabling custom update cycles and performance-critical systems without relying on MonoBehaviour's Update method.

## [[Implementation]]

### [[Understanding the Player Loop]]
```csharp
// Unity's PlayerLoopSystem struct
public struct PlayerLoopSystem {
    public Type type;
    public PlayerLoopSystem.UpdateFunction updateDelegate;
    public PlayerLoopSystem[] subsystemList;
}
```

Key methods:
- `GetDefaultPlayerLoop()` - Gets default engine system order
- `GetCurrentPlayerLoop()` - Gets current state
- `SetPlayerLoop()` - Applies changes

### [[Timer System Implementation]]

#### Bootstrapper Class
```csharp
[RuntimeInitializeOnLoadMethod]
static void Initialize() {
    var currentLoop = PlayerLoop.GetCurrentPlayerLoop();
    // Modify and set player loop
}
```

#### Timer Manager
```csharp
public class TimerManager {
    private List<Timer> timers = new();
    
    public void RegisterTimer(Timer timer) { /*...*/ }
    public void DeregisterTimer(Timer timer) { /*...*/ }
    public void UpdateTimers() {
        foreach (var timer in timers) {
            timer.Tick();
        }
    }
}
```

#### Timer Base Class
```csharp
public abstract class Timer {
    public float CurrentTime { get; protected set; }
    public bool IsRunning { get; protected set; }
    
    public abstract void Tick();
    public abstract bool IsFinished { get; }
    
    public void Start() {
        CurrentTime = InitialTime;
        if (!IsRunning) {
            IsRunning = true;
            TimerManager.Register(this);
            OnTimerStart?.Invoke();
        }
    }
}
```

### [[Play Mode Handling]]
```csharp
#if UNITY_EDITOR
[InitializeOnLoadMethod]
static void SubscribeToPlayModeChanges() {
    EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
}

static void OnPlayModeStateChanged(PlayModeStateChange state) {
    if (state == PlayModeStateChange.ExitingPlayMode) {
        var currentLoop = PlayerLoop.GetCurrentPlayerLoop();
        RemoveTimerManager<Update>(currentLoop);
        PlayerLoop.SetPlayerLoop(currentLoop);
        TimerManager.Clear();
    }
}
#endif
```

### [[Countdown Timer Implementation]]
```csharp
public class CountdownTimer : Timer {
    public CountdownTimer(float duration) : base(duration) {}
    
    public override void Tick() {
        if (IsRunning && CurrentTime > 0) {
            CurrentTime -= Time.deltaTime;
            if (CurrentTime <= 0) {
                Stop();
            }
        }
    }
    
    public override bool IsFinished => CurrentTime <= 0;
}
```

## [[Best Practices]]
1. Use [[Player Loop Systems]] for performance-critical updates
2. Implement proper [[Cleanup]] when exiting play mode
3. Clear [[Static References]] to prevent memory leaks
4. Use [[Editor Hooks]] for proper initialization and cleanup
5. Consider [[Thread Safety]] when modifying shared state

## [[Additional Resources]]
- [[Unity Documentation: Player Loop API]]
- [[Custom Update Systems]]
- [[Performance Optimization Techniques]]
- [Player Loop Deep Dive](https://example.com/player-loop)
- [Advanced Unity Systems](https://example.com/unity-systems)