# April 5

Watching Unreal stream, looking through things and getting caught up on 

Cleaning up hard drive to install UE5

Moving Landforms to hard drive - worked fine

Watching Unity Twitch GDC stream for info - looking for Tunic Post Processing info

Note - Need to get wave spawner working OR need better wave spawner

[https://assetstore.unity.com/packages/tools/spawn-ity-58832](https://assetstore.unity.com/packages/tools/spawn-ity-58832)

Tunic VFX - Scrolling textures

- Chromatic abbberation built into the texture it seems

ShaderForge used for the texture 

Should be able to do this all in Shader Graph

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled.png)

Applying similar Texture/Shader FX to a different meshes as well

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%201.png)

Scrolling textures along tubes here, could maybe do the same with bezier curves easier?

Looks great though especially with Post Processing

These are UV’d shapes with scrolling textures

Background is a scrolling texture as well - Using minimum to blend two things 

Colour wheel, if you’re gonna go hard on one chroma, try pulling back a little and giving some in from a colour on the opposite side of the wheel

4:37 GDC Day 1 - Tunic Post Processing - building it up from nothing

Turns on Light Mapping, shadow catches of geometric complexity - nice touch - try thinking this way?

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%202.png)

Mentions turning on SSAO first but this scene doesnt have it - can I add to my game?

Turns on bloom next

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%203.png)

Using Amplify Color with custom lookup tables next

Tonemapping / Colorgrading brings a lot to this

Amplify Color can let you import a photoshop file

You can screenshot your scene, bring into photoshop, make changes, then import to amplify color

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%204.png)

Saturating shadows is a key trick to do

Applied a gradient to the whole image through one of the cameras - using BLIT camera

Applied upper right and lower left 

Adds a glowing off in the distance

Different scenes will use different colors / types of this

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%205.png)

SHowing old version of the world without textures - looks too flat in some ways

Using some textures to add a bit of detail really brings out more

Maybe simplest way of bringing grit to the objects

Good technique to steal!

SSAO leaned on pretty hard for lighting

Added SSAO to my Forward Renderer in Beat Traveller! Experiment with this

Andrew - worked with PowerUp Audio

But bouncing it off people is super important - Finji big help!

Playtesting along the way is important as well

How was color palette established?

Not really a lot of concept art

A lot of trial in error with color palette, and what techniques

For example, saturated shadows 

- Lots of floating annotations -cool thing here- lol

Modelling the world was mostly done in Pro Builder

Automatic UV in Pro Builder largely does the job

What are UVs again?

Need to throughly look at my wave spawner for gameplay issues

Checking now - destroyed an enemy but it did not register as destroyed in GUI log 

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%206.png)

Also this error seems to happen upon spawning

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%207.png)

Investigating if there’s any special code for enemy death in demo

- Has SpawnableIdentity script, thought I believe this has issues with NavMesh? Meaning it wants one though I dont use NavMesh

Destroys gameobjec tin demo

![Untitled](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%208.png)

I dont want to destroy generally - way around this?

Maybe using Despawners - can they track when item becomes inactive it can be seen as destroyed?

Maybe add DespawnOnEvent and then I can - ‘call this method from Unity events or animation events to trigger despawns at specific times.’

Check if demo features any despawners

SEE Despawn From Code section - probably best bet!