# Shmup Principles

[Movement, Shooting & Hitboxes [SHMUP WORKSHOP 01]](https://www.youtube.com/watch?v=yAF2FkgyiYM)

No acceleration / decelertation

Add a turning animation - make the speed match

Use follow through animations

Fast bullets - feel more dagerous and powerful

- make sure they’re adequately faster than the player

Fast bullets should be tall, simulate motion blur

Think detailed bullets and splash aniamtions - make them high contrast

Add a shot limit?

- adds risk vs reward dynamics to shooting

Use animations to make some projectile seem more powerful while only increasing effectiveness by a minimal amount

Hurt boxes vs Hit boxes - easier to aim but harder to collide and get hurt

[Visibility [SHMUP WORKSHOP 02]](https://www.youtube.com/watch?v=jYuqPKa0yPE)

Attack types

[Bullet Pattern Design [SHMUP WORKSHOP 03]](https://www.youtube.com/watch?v=ArZRZSYICLo)

Aimed / Static / Randomized 

Dodging types

Micro and Macro 

Think about telegraphing attacks 

How can attack sequences flow into each other?

Think in lanes - what are the lanes of my game?