# General Approach

Synesthesia - **when you experience one of your senses through another**

‘Music Games’ and ‘Rhythm Games” are a big example - create scenarios invoking this due to tightly locked interaction between music / visuals / player control 

We are taking a larger experience and pairing it down to necessary components

Whether that be visuals, audio, or control

Synesthesia in gaming - 

Poke at any idea for me if you don't mind. Games have varying types and degrees of interlocked systems on a visual, audio, and tactile level. Some interactions / cues are represented in one or multiple of these streams at the same time. There are some games that attempt to operate on all streams at the same time, creating tightly interlocked systems between senses, and these games are taking a Synesthetic approach. 

They create a common ground, a universality on the way these systems operate functionally by doing this.