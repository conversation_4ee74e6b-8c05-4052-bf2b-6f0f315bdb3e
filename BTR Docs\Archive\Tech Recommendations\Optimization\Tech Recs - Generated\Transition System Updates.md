# Simplified Wave Transition System  

## Core Philosophy  
**"Waves manage combat, Sections manage progression, Scenes manage environment"**  

## Key Changes from Previous System  
1. Removed WaveEventSubscriptions/WaveEventChannel middlemen  
2. Separated scene loading into dedicated SceneLoader service  
3. Introduced explicit state machine for transitions  
4. Consolidated wave/section tracking into SectionManager  

## New Component Structure  

### 1. WaveManager (Replaces WaveEventSubscriptions)  
```csharp
public class WaveManager : MonoBehaviour {
    // Directly handles wave events from spawners
    public UnityEvent<WaveResult> OnWaveCompleted;
    
    public void StartWave(WaveConfig config) {
        // Handle wave setup
        EnemyManager.OnWaveStart();
        ProjectileManager.OnWaveStart();
    }
    
    public void EndWave(WaveResult result) {
        // Cleanup and notification
        OnWaveCompleted.Invoke(result);
        EnemyManager.OnWaveEnd();
    }
}
```

### 2. SectionManager (New Component)  
```csharp
public class SectionManager : MonoBehaviour {
    public Section CurrentSection { get; private set; }
    
    public void Initialize(SectionConfig config) {
        // Set up section requirements
    }
    
    public bool TryProgressSection() {
        // Validate and move to next section
    }
}
```

### 3. SceneLoader (Extracted from SceneManagerBTR)  
```csharp
public class SceneLoader : MonoBehaviour {
    public async Task TransitionToScene(string sceneName) {
        // Handle ALL scene transition logic:
        // 1. Fade out
        // 2. Unload current
        // 3. Load new
        // 4. Initialize
        // 5. Fade in
    }
}
```

## Simplified Flow  

### Wave Progression  
```mermaid
sequenceDiagram
    WaveSpawner->>WaveManager: Wave Started
    WaveManager->>EnemyManager: OnWaveStart
    WaveManager->>SectionManager: RegisterWaveStart
    WaveSpawner->>WaveManager: Wave Ended
    WaveManager->>SectionManager: RegisterWaveCompletion
    SectionManager->>SceneLoader: CheckSceneTransition()
```

### Scene Transition Decision Tree  
```mermaid
graph TD
    A[Wave Completed] --> B{Section Complete?}
    B -->|Yes| C{Last Section?}
    B -->|No| D[Continue Current Scene]
    C -->|Yes| E[Initiate Scene Transition]
    C -->|No| F[Progress Section]
```

## State Management Improvements  

### 1. Unified Transition State  
```csharp
public enum GameState {
    Combat,
    SceneTransition,
    SectionTransition,
    Paused
}

public class StateManager : MonoBehaviour {
    public GameState CurrentState { get; private set; }
    
    public void SetState(GameState newState) {
        // Handle state validation and transitions
    }
}
```

### 2. Section Configuration SO  
```csharp
[CreateAssetMenu]
public class SectionConfig : ScriptableObject {
    public int requiredWaves;
    public SceneReference scene;
    public AudioCue sectionMusic;
    public Spline pathSpline;
}
```

## Migration Benefits  
1. **-40% Event System Complexity**  
2. **Clearer State Transitions**  
3. **Decoupled Scene/Wave Logic**  
4. **Easier Debugging**  
5. **Config-Driven Section Setup**  

---

**Confidence Score:** 85%  
**Recommended Next Steps:**  
1. Create migration plan for old event system subscribers  
2. Develop validation tests for state transitions  
3. Build editor tools for SectionConfig setup  

Would you like me to proceed with implementation examples for any specific component?
