# May 30

Overhauling Dolly system

Introducing Curvy Splines and adding them to Ouroboros level

Way more functional than cinemachine dolly

Can use them for Behavior Designer movement patterns as well 

Successfully implemented

- Now time freezes for you, technical error but feel like this may be a good idea?
- Allows you to look around a bit

Due to camera movement changes, i am adjusting reticle/shooting position based on z value form player, instead of z value from camera. 

Add disolve effect to projectiles upon death

shader swap? Look at the VFX unity asset that you found

---

---

May 31

Fixed reticle by attaching to game object - need to make look a bit better

Possibly fixed enemies falling off grid using fixed update - more testing needed

Check their movement - affected by this?

Add volumetric clouds for snakes to come out of ?

Using smoothDeltaTime in palce of delta time, may be better for Chronos / TImeline use

Target cube approach might not be working out well, projectiles getting stuck on occasion.

Laucnhable Bullet Locked are getting shot at targetcube’s, why is this? Look at ProjectileStateBased breakdown for answers

Updated Targetcube script to bring in line with other updates

Setting dissolve material, shaders and and more to make projectiles look nicer

alternate color when shot is not working, issue with alpha channel of flatkit shader

eliminated errors with velocity on kinematic object being set - new error that is no longer allowed fully, not needed anyway

Enemy Shooting particles not pooling correctly, need to take a close look 

![Untitled](May%2030%208c27e9786ce54d88ba686147033d5de5/Untitled.png)

Change material on mobius defrom 2 from glaalxy material 9 to see it easier