# January 2022

January 2

Spawner + Dyna mic Waypoint testing  

Current working scene is “Dynamic Waypoint Testing and Spawning”

Need to create a proper Enemy Class and use old one for just bullet now

Bullets can be everywhere - so how does aiming and combat work? 

Need to think this through a bit. Should I be able to lock on to enemies?

Prototype this idea out - locked bullets fire at current locked enemy 

What are the possible scenarios for optimal shooting at enemies?

- gather up bullets on aiming device, fire back in a line
- lock on to target enemy, all bullets aim at them

**January 3**

Exploring case 2

if raycast seeing tag is enemy

if locked bullets > 0 

lock on to target - only lock on to one at a time

this attaches game object of LOCKED ON like bullets do

set bullets to target that enemy

Running into many errors, need to put this aspect aside to properly separate Enemy and Bullet classes

Doing a backup first!

Renaming EnemyBasics to Projectile

Enemy Basic Setup is new Enemy class

working on enemy lock on now

see if enemy class locked becomes true - if bullets can target it  

using homing and playerTarget in Projectile to get Projectile to target the enemies

may need to seperate this - same homing for homing bullets hitting player may not be the best approach for bullet aimed at an enemy through LaunchAtEnemy

May need to implement a different Minimap / Radar option. Not updating colors and some functions at run time!

Running into koreographer issues - cross hair issues

Crosshair.OnMusicalShoot (SonicBloom.Koreo.KoreographyEvent evt) (at Assets/Scripts/Crosshair.cs:216)

Choke point of project?

Looks like homing is off possibly?

Projectiles are hitting enemy and health is decreasing now - fixed by turning off homing closer to enemy

Homing does seem somewhat off... not sure why

IDEA: Mixing between running animation and flying segments?

Issues with waypoint verticality might be more to do with external behaviours then anything else? Tag name in BD - white vs greyed out difference??

Dyanmic waypoint testing and spawning 1 WORKS but not 2!

Figure out if any meaningful difference and move on from here

**January 4**

Made a scene based on 1, not 2, could not fix 2. now operating in 3

Disabled Player Radius on the Player model - believe it is unecessary

Playing with camera angle and perspective

How do I get the best angle to see what’s happening while also maintaing a close to the action, thrilling feel??

LOCK ON BUG

Locks list shows 1 but Targets shows no objects

- how does this issue occur? Put in a check for discrepancy?

Bullets getting stuck on grid as well - why is this happening? They die eventually due to lifetime, but unsure why this is happening. Will need addressing

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled.png)

Tactical AI pack for my enemies!

- Shoot and Scoot
- Flank
- Ambush
- Surround

Bringing in A* Versions to scene to test

Trying to intergrate Behavior Designer A* Tactical Assets with A* Star Moving example and it’s not working. Getting Local Space Rich AI Graph error. Have had this error before, means it doesnt see the graph.

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.Update () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:57)

Anything in the Behavior Designer A* Movement Pack works fine. Remove AI Path (2D,3D) and replaced with Local Space Rich AI. Have RVO Controller that recognizes Local Space Rich AI

Full error

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.CalculatePathRequestEndpoints (UnityEngine.Vector3& start, UnityEngine.Vector3& end) (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:50)
Pathfinding.AIBase.SearchPath () (at Assets/AstarPathfindingProject/Core/AI/AIBase.cs:480)
Pathfinding.RichAI.SearchPath () (at Assets/AstarPathfindingProject/Core/AI/RichAI.cs:252)
BehaviorDesigner.Runtime.Tactical.AstarPathfindingProject.Tasks.IAstarAITacticalGroup+NavMeshTacticalAgent.SetDestination (UnityEngine.Vector3 destination) (at Assets/Behavior Designer Tactical/Integrations/Astar Pathfinding Project/Tasks/IAstarAITacticalGroup.cs:57)
BehaviorDesigner.Runtime.Tactical.AstarPathfindingProject.Tasks.ShootAndScoot.OnUpdate () (at Assets/Behavior Designer Tactical/Integrations/Astar Pathfinding Project/Tasks/ShootAndScoot.cs:129)
BehaviorDesigner.Runtime.BehaviorManager.RunTask (BehaviorDesigner.Runtime.BehaviorManager+BehaviorTree behaviorTree, System.Int32 taskIndex, System.Int32 stackIndex, BehaviorDesigner.Runtime.Tasks.TaskStatus previousStatus) (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Tick (BehaviorDesigner.Runtime.BehaviorManager+BehaviorTree behaviorTree) (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Tick () (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Update () (at <58623c9461324266a5b7839460bb9d26>:0)

Trying a scene combining the moving example and the BD Tactical but no luck. 

Add Ultimate Character Controller to try out Deathmatch AI - need to look into implementation as it shoots errors now

IDEA: A a dodge button for attacks? general dodge option within a certain closeness, or just attach dodging to the guiding of the reticle?

**January 12**

Think previous bullet error stem from crashing into each other. What to do in this case?

Program for this scenario

January 13

Enabled this feature

[https://www.youtube.com/watch?v=P7cYVg5fAvY](https://www.youtube.com/watch?v=P7cYVg5fAvY)

Change this playmode options if weird bug occurs! May need to troubleshoot

Static variables may not be reset when reload domain is OFF

Reload Scene needs to be on for proper use of Scriptiable Objects - like A* !!!!

If all enemies defeated, need to clear out target

Need to develop targetting system more!!! Need a proper bug and dev categories / log to keep track of these things and mark them as DONE

These videos teach me that I am still in the prototyping phase IMO - focus on this 

Contact Robbie - Level Curve? - about FMOD and other things- talk to Henry about this!

Judgement Silversword - SHIELD - should I do this? Or a dodge?

**Jan 18**

Things to figure out

- Bullets not moving properly?
- Enemies not going to way points?

Issue when area is moving, static seems fine! Double check this though   

Reparent to EP1 once awake? WORKED WOO

Tried slow down on lock on - not sure how I feel about this! It’s pretty intense effect

IDEA: Use Mirror / Portal like effect for a BOX level / section? 

[https://www.youtube.com/watch?v=3AIsleRlx5Q](https://www.youtube.com/watch?v=3AIsleRlx5Q)

Jan 19

If make power lines, need to be concious of sag of wire. will sag in wrong direction if on side

Utility pole / Power Line prefab made, need to work on a better scene for it

Learn more about Mesh Combine and Mesh Simplify - settings shoudl help you reduce objects

Figure out what is taking so long to get to play mode

**Jan 20**

**Installed and using Mesh Baker instead of Mesh Combine**

- Insight note - Baking the whole mesh may be harder on the system. Baking quadrants looks like it might be better, because then some of them will be culled. Sound like culling will not occur if all baked into one mesh

This is true! One big mesh is BAD

Texture Baker Video

[https://www.youtube.com/watch?v=XSdwuLvChRg](https://www.youtube.com/watch?v=XSdwuLvChRg)

Mesh Baker Grouper Video

[https://www.youtube.com/watch?v=y5kmWixP0tg&](https://www.youtube.com/watch?v=y5kmWixP0tg&)

Trying out Ultimate LOD System while downloading Automatic LOD

Will see if this helps / will do or more popular tool is better

Jan 21

Trying many different optimization tools

AutoLOD for all the buildings

Prefab Brush+ is working!!!! Paint Prefabs on a plane

Using GPU Instancer with Prefabs seems to help performance - not as much as I thought!

Moving Navmesh doesnt seem to work anymore UGH

Consider alternatives for this - could i do smaller sections and flip between scenes? Or is that even necesary?

Jan 22 

Maybe should use general A* Pathfinding

Research adjust material color at runtime. DOTWeen can probably lerp this?

[https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/](https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/)

Tried Ethereal URP again for FOG and such, no great luck it seems! Maybe try again later

Jan 24

Graph problems are fixed! Aron has found the problem

Need to fix spawn point location issues - not properly updating?

ALso fix my little guy disappearing. bring him back!

Jan 25 

Spawn Points Bug - Why is it not moving properly? Position not updating?

Implement IDamagable interface into Enemies and Player for Behavior Designer

Implemented these things in Player and enemy. Broke the Enemy Rhythm shooting - more investigation needed to fix this

Cannot seem to get interesting Behavior Designer patterns working for enemy AI

Attempting import of Deathmatch AI kits to rectify this. 

Bring over that enemy AI into my scene, see how it behaves?

Figured out import errors but need to make a Deathmatch AI Test scene to see if anything from this is feasible

Chart out how Deathmatch AI works

Jan 26

Successful AI scenario with BD

Shoot and Scoot - working!

Flank - working! No leader on main enemy, others must follow that one

One good enemy type - scoot and shoot - can i get spawner properply spawning it?

Not able to shoot when spawned - unsure why this is happening!

When fired the original enemy show itself as parent spawner

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%201.png)

This is not happening on clones

Pool is also not being generated, while that is not an issue for enemy already in scene

Enemy IN SCENE will generate pool once made active - but does not shoot!

Needs to be active at scene start - why? How did I get around this?

In Old scene, Particle System and OBS was on child game object- trying this

FIXED IT - Needs to have particle system / Object Particle Spawner on a child object

Enemy Test 3-2 works with Shoot and Scoot in the Spawner now

Enemies are getting stuck though when trying to move around though 

- adjusting settings on them to affect this

**Believe it may be recalculating graph issues - when not moving it never gets stuck**

- read up on graph properties

Fluxy for fluid visual effects!!!!

Magio visual effects?

Go through all recent examples

Jan 27th

Key Problems Today

- A* graph while moving (noted yesterday)
- Can’t lock on to enemies anymore (no blue outline or what?)
- Bullets getting stuck - maybe have them destruct when collide with each other?
- Add Koreo to Shooting Mechanism

Bullet fix - seems to work! Will go with this for now - need to do more to account for all bullet states though

Trying this solution as well

> Make a new layer called "Bullets"
> 

> Go to Edit -> Project Settings -> Physics and under the Layer Collision Matrix, untick the box that has the row and column for Bullets (Should be on the anti-diagonal)
> 

> Now your bullets will pass through each others
> 

Seem to only get stuck when fired at enemies now

Will also get stuck around player character

Ex. Two bullets, both stuck  - only enabled states are Homing and Homing Enabled on Start

turns out this next bit is related!

Enemy Lock On - Did not have Enemy Basic Setup class on Enemy Test 3-2

- Need to inheriet IDamagable for BD use? Look into this

Stuck bullets look like this  

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%202.png)

None appear to have hit the enemy

Had any issue with bullets colliding with the enemy shooting them

Projectiles set to ignore any gameobject tagged Enemy for Triggers

Currently using Trigger instead of OnCollision but probably doesn’t matter

Enemies weren’t taking damage but was just a dumb oversight - good reason to clean up the code!!!

Lock on bullets to hit enemies are ALWAYS getting stuck 

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%203.png)

**Enemies are now taking damage but this error not fixed - investigate what is happening here**

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%204.png)

Also happens to the Player - bullets get stuck around here

Checking if the bullets that get stuck had previously been recycled or are fresh

Look like it’s been recycled - not all properties refreshed on recycle?

Disabled homingEnabledOnStart - looks like a source of some error

**CAN get stuck with recycle count = 0**

Something else is the issue here

Bullet stuck next to play - homing is only thing enabled

disabling homing variable on death - need to look at legitimate homing methods for different bullet type

SOme stuck bullets in enviroment - maybe prev stuck around enemy?

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%205.png)

**KEY SCENARIO? -** else if (launching == true && locked == true) 

Is the projectile circling the player very quickly? constantly looking at it and then rehoming?

I increased the distance needed for the projectile to disable homing - now many are missing the intended target

Local rotation and position is just CONSTANTLY flipping back and forth on this, so i think this diagnosis of the issue is on the right track?

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%206.png)

This is happening here as well, with all 4 parameter enabled on these stuck bullets. One recycle count ZERO and hasHitPlayer is NOT checked. 

Another stuck one 

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%207.png)

If both of these are true- what should be done? This is turning up more now - bullets i’ve locked onto to shoot as well

Reassessing structure while referencing [https://www.youtube.com/watch?v=Z6qBeuN-H1M&t=12s](https://www.youtube.com/watch?v=Z6qBeuN-H1M&t=12s)

Moving bullets to rigid bodies for movement and trying something new with slower speeds

IMPORTANT: Need projectile to check that target still exists or it will just get stuck when it’s already destroyed. Quick fix is now in for this

Audio Cue for every time particle emits

[https://www.youtube.com/watch?v=jKSz8JJnL4E](https://www.youtube.com/watch?v=jKSz8JJnL4E)

Look at ObjectParticleSpawner to see if that might be more performant with sound effects

Maybe just have all bullets play sound on awake?

DISABLED TargetCube to reduce visual clutter - need to come back to this idea of using target cube

Important difference between using BehaviorDesigner.Runtime.Tactical and declaring within namespace BehaviorDesigner.Runtime.Tactical 

Learn more about this!

~ Spawner Issues  - spawner location changing ~ do not seems to be happening anymore! Unsure why ~

Trying bullet as drone - interesting!

Koreogrpaher stack or Shooting stack seems to break things - look into this deeper

Reference Area X for visual clarity when lots of far away bullets / items are on screen moving around 

Look through twitter sends and look through recent assets 

**Jan. 28**

Early thoughts - Outline on main character? 

Errors in DOTWEEN Support so it’s been removed

Fluxy package - looked at a little bit - may be neat - look at in future

Need to fix this

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%208.png)

Added projectiletarget=nulll if statement

MAKE SURE this doesnt mess up other aspects of projectile states - NOT SURE IF IT DOES

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%209.png)

Seems to be Koreographer related issue. Is it a timing thing? 

Investigate this, adapt Wave Spawner waves and inventory other issues

**Jan 29**

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%2010.png)

Bullets caught in circling loops when missing enemy target

![Untitled](January%202022%20130755c3ec8943419f6377e790bf4a86/Untitled%2011.png)

Add bullet particle effect with kick track?