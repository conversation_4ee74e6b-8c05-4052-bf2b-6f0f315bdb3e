using UnityEngine;
using BTR;

namespace BTR.Testing
{
    /// <summary>
    /// Simple test script to verify PlayerTimeControl speed functionality
    /// Attach this to a GameObject in the scene to test the speed control system
    /// </summary>
    public class PlayerTimeControlTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private KeyCode testSlowTimeKey = KeyCode.T;
        [SerializeField] private KeyCode debugSpeedKey = KeyCode.Y;
        
        private PlayerTimeControl playerTimeControl;
        
        void Start()
        {
            // Find the PlayerTimeControl component
            playerTimeControl = FindFirstObjectByType<PlayerTimeControl>();
            
            if (playerTimeControl == null)
            {
                Debug.LogError("[PlayerTimeControlTest] PlayerTimeControl not found in scene!");
            }
            else
            {
                Debug.Log("[PlayerTimeControlTest] Found PlayerTimeControl - ready for testing");
                Debug.Log("[PlayerTimeControlTest] Press T to test slow-time, Y to debug speed state");
            }
        }
        
        void Update()
        {
            if (playerTimeControl == null) return;
            
            // Test slow-time functionality
            if (Input.GetKeyDown(testSlowTimeKey))
            {
                Debug.Log("[PlayerTimeControlTest] Testing slow-time effect...");
                // Trigger slow-time effect (you'll need to call the appropriate method)
                // This is just a placeholder - you'd call the actual slow-time method
                playerTimeControl.DebugSpeedState();
            }
            
            // Debug speed state
            if (Input.GetKeyDown(debugSpeedKey))
            {
                Debug.Log("[PlayerTimeControlTest] Debugging speed state...");
                playerTimeControl.DebugSpeedState();
            }
        }
        
        void OnGUI()
        {
            if (playerTimeControl == null) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("PlayerTimeControl Test");
            GUILayout.Label($"Press {testSlowTimeKey} to test slow-time");
            GUILayout.Label($"Press {debugSpeedKey} to debug speed state");
            
            if (GUILayout.Button("Debug Speed State"))
            {
                playerTimeControl.DebugSpeedState();
            }
            
            if (GUILayout.Button("Refresh Original Speed"))
            {
                playerTimeControl.RefreshOriginalPlayerSpeed();
            }
            
            GUILayout.EndArea();
        }
    }
}
