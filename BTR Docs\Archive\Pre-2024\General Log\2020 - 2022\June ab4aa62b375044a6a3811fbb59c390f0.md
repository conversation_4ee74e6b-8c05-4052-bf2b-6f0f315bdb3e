# June

**June 1**

 **-** <PERSON> is away, i am also going a bit crazy, so progress has been slow at best maybe. I need to review all previous notes for May, see what i'm planning for June. Also working on a Gamma Space project concurrently

Viewing some of the Code Money Unity Functions tutorial tonight, to just get more familiar with the basics

**June 9**

Centering not working on lock on - center aim reticle to targets - reposition aim reticle

Seems to be a problem in the computer of center with X and Y

Is this due to negative/positive numbers? COME BACK TO THIS

Need to make better level design for lock on / shooting mechanic

<PERSON><PERSON> from Ynglet?

**June 10**

Adding the Panzer Dragoon camera angle changing feature

Not working properly. Needs adjustment

Need to be additive with angle on player movement

How to rotate the aiming reticle?

**June 11**

Need to thoroughly look into how aim reticle is bounded

Errors happening when rotated

Camera Properly rotates! Mostly! Weird 360 spins sometimes?

Reticle needs adjusting

**June 15**

Several days downtime due to second covid shot

Looking to Panzer Dragoon for character control

Should I just chain player character to reticle?

Need to understand reticle movement better!

Spin everything on a rotation game object??? Seems way simpler. Trying this. It works!!!

Fix jittery movement - refer to old project on desktop

**June 16**

Trying rotation with Time Glitching

Need more thorough implementation to keep in time

Design choice: Time Glitch and rotate or just allow rotation?? Maybe just allow rotation with sound effect?

Download Pro Radar to integrate radar on game for orientation

**June 17**

Pipeline errors - spent the morning fixing that. 

Made small adjustments to radar. Need to get in Pro Builder and Pro Grids, etc

**June 18**

[https://www.youtube.com/watch?v=GioRYdZbGGk](https://www.youtube.com/watch?v=GioRYdZbGGk)

Can make a Material with flat "base map" of level, apply it to a plane, and build from there

Lots of good greyboxing advice in for 20 minutes

Error - if rotating while moving downward, screws up perspective, need to fix this!!!

How to handle rotation platform rotating in any direction?

Have a function recognizing Y movement that changes rotation axis - not totally working, may need to adjust for oritentation - if 1 of 4 positions, rotate accordingly to next position on X or Z/Y axis

**June 19**

Figuring out rotations for dolly travelling in different directions

Spent several hours on this, still didnt get anywhere

**June 22**

Add Tetris gameplay - very rough and needs work.

Try to make pieces lockable and transferable between perspectives?

Integrate EnemyBasics Script with game pieces script? Maybe need an edited version of this for these

**June 23**

Thinking out Tetris integration problems

Movement class constantly spawning new and object is not moving down - need to fix!

commented out spawning new

**June 24**

Fixed problems with Tessera Pro conflict and Tetris Class "Rotation" - now Tetris Rotation

**June 28**

Upgraded to HDRP over past couple days and also played with Tetris implementation

Looking at Jelly Cube puzzle implementation as well

6 Games Match Puzzle and Jigsaw Puzzle and Toybox Construction Kit too!

ALSO Polarith AI Pro for some enemies? Might be cool! Worth Testing

Some forward / Backward level like this? 

[https://assetstore.unity.com/packages/templates/packs/000-endless-maze-complete-project-template-175485](https://assetstore.unity.com/packages/templates/packs/000-endless-maze-complete-project-template-175485)

Assessed several - looks like 

Really need to assess what I'm trying to make here. Maybe scale back, keep it simpler

**June 29**

Checking out Tesera - can generate flat landscapes this way