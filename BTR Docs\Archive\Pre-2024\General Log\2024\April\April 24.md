# April 24

<PERSON><PERSON><PERSON> refactored to LockAndShoot

lockandshoot

- playerlocking on and player locking off events
    - use these to trigger feedbacks? currently private
    - Can assign them to some list or object?
    - Are they causing errors once ive edited them / commented out?

slowTime variable not assigned in boss twinsnake scene- what should it be?

THIS IS WHATS BREAKING SLOW TIME - FIND APPROPRIATE EFFECT

Adjusting the Boss Time’s that are indepedent of standard time, to be affected by slow time. 

In the parent Boss script

Adjust SlowToBeat so it effect player movement on spline time as well

Bullets getting stuck around player - not sure why - is this only in the Twin enemy snake level?

```jsx

```

Getting all these errors when i shoot

![Untitled](April%2024%20fea7a6ff4e544d4a93b93d5391502e1d/Untitled.png)

Seems like Look direction getting 0 or NaN values given, need to avoid those situations?

Bullet speeds too slow in some circumstances. maybe default is too slow? Sped u, it helped

At some point, bullets are getting stuck on kinematic, and arent able to move, that’s why i cant shoot properly at times.

Not sure what triggers this, but also trips up FMOD as well

Seems something like being stuck in locked by player state, some transition not working properly

May have to do with updates made dureing twin boss bullet lock on decisions, to make them transparent and follow reticle. Test other scenes, see how this behaviour manifests there

Is it the same problem that occurs in regular ouroboros scene?