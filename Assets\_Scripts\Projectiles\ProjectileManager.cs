using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Unity.Cinemachine;
// REMOVED: using Michsky.UI.Reach; - Replaced with custom UI system
using PrimeTween;
using UnityEngine;
using UnityEngine.Events;
using FluffyUnderware.Curvy;
using FluffyUnderware.Curvy.Controllers;
using Stylo.Epoch;
using UnityEngine.SceneManagement;
using UnityEditor;
using Stylo.Cadance;
using BTR;
using BTR.Projectiles;
using BTR.Projectiles.Management;
using Microsoft.Extensions.Logging; // For ILogger
using ZLogger;
using ZLogger.Unity; // For Unity specific ZLogger extensions

namespace BTR
{
    [DefaultExecutionOrder(-250)]

    public class ProjectileManager : ProjectileManagerBase<ProjectileManager>
    {
        // Manager ID for the standardized manager system
        public override string ManagerId => "ProjectileManager";

        [SerializeField]
        private int staticShootingRequestsPerFrame = 10;

        private const int MAX_PROJECTILES = 1000;
        private IProjectile[] projectileArray;
        private bool[] activeSlots;
        private IProjectile[] playerProjectileArray;
        private IProjectile[] enemyProjectileArray;
        private int playerProjectileCount;
        private int enemyProjectileCount;
        private ProjectileGrid projectileGrid;

        [SerializeField] private LayerMask playerLayerMask;
        [SerializeField] private LayerMask enemyLayerMask;
        [SerializeField] public float projectileAccuracy = 1f;

        private bool isTransitioning;
        private ProjectileJobSystem projectileJobSystem;
        private ProjectilePool projectilePool;
        private ProjectileSpawner projectileSpawner;
        private Dictionary<GameObject, Vector3> lastPositions = new Dictionary<GameObject, Vector3>();
        private Dictionary<int, Transform> enemyTransforms = new Dictionary<int, Transform>();
        private float lastEnemyUpdateTime = 0f;
        private const float ENEMY_UPDATE_INTERVAL = 0.5f;
        private HashSet<int> homingProjectileIds = new HashSet<int>();

        private Microsoft.Extensions.Logging.ILogger _logger;

        // Debug flag to control homing projectile logs
        [Header("Debug Settings")]
        [SerializeField] private bool enableHomingDebugLogs = false;

        [Header("Coordinated Attack Settings")]
        [SerializeField, EventID, Tooltip("The Cadance event ID that triggers coordinated attacks. Projectiles will attack on each beat of this event.")]
        private string coordinatedAttackEventID = "Coordinated Attack";

        [Header("Group Attack Settings")]
        [SerializeField, Tooltip("Enable or disable group attack behavior")]
        private bool enableGroupAttacks = true;

        [System.Serializable]
        private class GroupAttackSettings
        {
            [SerializeField, Tooltip("The range at which projectiles will start grouping together for a coordinated attack.")]
            public float groupAttackRange = 10f;

            [SerializeField, Tooltip("The minimum number of projectiles needed to form a group attack.")]
            public int minProjectilesForGroup = 3;

            [SerializeField, Tooltip("The color that grouped projectiles will glow while preparing to attack.")]
            public Color groupAttackColor = new Color(1f, 0.5f, 0f, 1f);

            [SerializeField, Tooltip("The distance from the player at which grouped projectiles will position themselves.")]
            public float groupAttackDistance = 5f;

            [SerializeField, Tooltip("How much faster grouped projectiles move when taking their positions.")]
            public float groupAttackSpeed = 2f;

            [SerializeField, Tooltip("How long projectiles will wait in formation before attacking.")]
            public float groupAttackDelay = 1f;

            [SerializeField, Tooltip("Toggle to show/hide debug visualization of group attack ranges.")]
            public bool showGroupDebugVisualization = true;
        }

        [SerializeField]
        private GroupAttackSettings groupAttackConfig;

        private void OnValidate()
        {
            if (!enableGroupAttacks)
            {
                groupAttackConfig = null;
            }
            else if (groupAttackConfig == null)
            {
                groupAttackConfig = new GroupAttackSettings();
            }
        }

        private Queue<IProjectile> groupAttackQueue = new Queue<IProjectile>();
        private float groupAttackStartTime;

        // IsInitialized property is inherited from ProjectileManagerBase<T>
        // Removed duplicate property to fix serialization conflict

        private const int BATCH_SIZE = 64;
        private const float POSITION_UPDATE_THRESHOLD = 0.1f; // Only update grid position if moved more than this
        private float updateInterval = 0.033f; // Update at ~30Hz for far projectiles
        private float nearUpdateInterval = 0.016f; // Update at ~60Hz for near projectiles
        private float distanceThreshold = 20f; // Distance to consider a projectile "near"
        private float lastUpdateTime;

        // Cache arrays and lists to avoid allocations
        private List<IProjectile> projectilesToProcess = new List<IProjectile>();
        private List<IProjectile> nearbyProjectilesList; // PHASE 5D: Updated to use interface

        private int currentProjectileIndex = 0;

        // Cache the array to avoid allocations
        private RaycastHit[] raycastHits = new RaycastHit[10]; // Adjust the size as needed

        private class PlayerShotTrail
        {
            public LineRenderer lineRenderer;
            public float startTime;
            public float duration;
            public Vector3 startPos;
            public Vector3 endPos;
            public Color startColor;
            public Color endColor;
        }

        [Header("Player Shot Trails")]
        [SerializeField] private float trailDuration = 1f;
        [SerializeField] private float trailStartWidth = 0.2f;
        [SerializeField] private float trailEndWidth = 0.05f;
        [SerializeField] private Color trailStartColor = Color.white;
        [SerializeField] private Color trailEndColor = new Color(1f, 1f, 1f, 0f);
        [SerializeField] private Material trailMaterial;
        private List<PlayerShotTrail> activeTrails = new List<PlayerShotTrail>();
        private Queue<LineRenderer> trailPool = new Queue<LineRenderer>();
        private const int INITIAL_TRAIL_POOL_SIZE = 20;

        [Header("Epoch Time Integration")]
        [SerializeField, Tooltip("The key of the Epoch global clock to use for time scaling")]
        private string epochClockKey = "Global";
        
        private EpochGlobalClock globalClock;
        private bool isClockInitialized = false;
        private SplineController playerSplineController;  // Add spline controller reference

        protected override void Awake()
        {
            Debug.Log($"[ProjectileManager] Awake START - GameObject: {gameObject.name} (InstanceID: {GetInstanceID()})");

            // Initialize ZLogger through our centralized provider
            try
            {
                _logger = BTR.Logging.LoggerProvider.CreateLogger<ProjectileManager>();
                Debug.Log($"[ProjectileManager] ZLogger initialized successfully");
            }
            catch (System.Exception ex)
            {
                // Fallback if ZLogger isn't ready
                Debug.LogError($"[{nameof(ProjectileManager)}] ZLogger initialization failed: {ex.Message}. Falling back to Unity Debug.Log.");
            }

            _logger?.LogInformation("Awake called on GameObject: {GameObjectName} (InstanceID: {InstanceIDValue})", gameObject.name, GetInstanceID());

            // Set manager configuration before calling base
            initializationPriority = ManagerPriority.Critical;
            persistAcrossScenes = true;
            initializeOnAwake = true; // Initialize in Awake to ensure availability for other managers

            // Set dependencies
            dependencies = new ManagerDependency[]
            {
                new ManagerDependency("ProjectilePool", true, "Required for projectile pooling"),
                new ManagerDependency("ProjectileSpawner", true, "Required for projectile spawning")
            };

            _logger?.LogInformation("About to call base.Awake() - HasInstance before: {HasInstanceBool}", HasInstance);

            Debug.Log($"[ProjectileManager] About to call base.Awake() - HasInstance: {HasInstance}");

            // Call base Awake for singleton management
            try
            {
                base.Awake();
                Debug.Log($"[ProjectileManager] base.Awake() completed successfully - Instance: {(Instance != null ? Instance.gameObject.name : "NULL")}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ProjectileManager] base.Awake() failed: {ex.Message}\nStackTrace: {ex.StackTrace}");
                throw;
            }

            _logger?.LogInformation("After base.Awake() - Instance: {InstanceNameOrNull} (ID: {InstanceIDOrNull}), IsInitialized: {IsInitializedStatus}",
                (Instance != null ? Instance.gameObject.name : "NULL"),
                (Instance != null ? Instance.GetInstanceID() : -1),
                IsInitialized);

            // Initialize job system component
            if (projectileJobSystem == null)
            {
                projectileJobSystem = gameObject.AddComponent<ProjectileJobSystem>();
                if (projectileJobSystem == null)
                {
                    _logger?.LogError("Failed to add ProjectileJobSystem component");
                }
                else
                {
                    _logger?.LogInformation("ProjectileJobSystem component added successfully");
                }
            }

            currentProjectileIndex = 0;
            _logger?.LogInformation("Awake completed - Instance available: {InstanceAvailableBool}, This == Instance: {ThisIsInstanceBool}", (Instance != null), (Instance == this));

            Debug.Log($"[ProjectileManager] Awake COMPLETED - Instance: {(Instance != null ? Instance.gameObject.name : "NULL")}, This == Instance: {(Instance == this)}");
        }

        private void Start()
        {
            _logger?.LogInformation("Start called - IsInitialized: {IsInitializedBool}, Instance: {InstanceNameOrNull}", IsInitialized, (Instance != null ? Instance.gameObject.name : "NULL"));

            // Ensure initialization is complete (may have already happened in Awake)
            if (!IsInitialized)
            {
                _logger?.LogInformation("Not initialized in Awake, calling Initialize() in Start");
                Initialize();
            }
            else
            {
                _logger?.LogInformation("Already initialized in Awake");
            }

            // Retry dependency initialization if needed
            RetryDependencyInitialization();

            // Register with the manager registry
            if (ProjectileManagerRegistry.Instance != null)
            {
                ProjectileManagerRegistry.Instance.RegisterManager(this);
                _logger?.LogInformation("Registered with ProjectileManagerRegistry");
            }
            else
            {
                _logger?.LogWarning("ProjectileManagerRegistry.Instance is null");
            }

            InitializeTrailPool();
            StartCoroutine(InitializeEpochClock());

            // Register for Cadance events
            if (Cadance.Instance != null)
            {
                Cadance.Instance.RegisterForEvents(coordinatedAttackEventID, OnCoordinatedAttackBeat);
                if (ProjectileLogger.Instance != null)
                {
                    ProjectileLogger.Instance.LogProjectileDeath(
                        "System",
                        ProjectileDeathReason.Unknown,
                        0,
                        0,
                        0,
                        Vector3.zero
                    );
                }
                _logger?.LogInformation("Registered with Cadance");
            }
            else
            {
                _logger?.LogWarning("Cadance.Instance is null");
            }

            _logger?.LogInformation("Start completed - Final status: IsInitialized: {IsInitializedStatus}, IsActive: {IsActiveStatus}", IsInitialized, IsActive);
        }

        private void RetryDependencyInitialization()
        {
            // Retry getting dependencies if they weren't available during Awake
            if (projectilePool == null)
            {
                projectilePool = ProjectilePool.Instance;
                if (projectilePool != null)
                {
                    _logger?.LogInformation("Successfully connected to ProjectilePool in Start");
                }
                else
                {
                    _logger?.LogError("ProjectilePool still not available in Start - projectile system may not function properly");
                }
            }

            if (projectileSpawner == null)
            {
                projectileSpawner = ProjectileSpawner.Instance;
                if (projectileSpawner != null)
                {
                    _logger?.LogInformation("Successfully connected to ProjectileSpawner in Start");
                }
                else
                {
                    _logger?.LogError("ProjectileSpawner still not available in Start - projectile system may not function properly");
                }
            }
        }

        private IEnumerator InitializeEpochClock()
        {
            // Wait a frame to ensure Epoch is initialized
            yield return null;

            int retryCount = 0;
            while (retryCount < 3)
            {
                try
                {
                    // Find the EpochGlobalClock component directly (matching PlayerTimeControl approach)
                    EpochGlobalClock[] globalClocks = FindObjectsByType<EpochGlobalClock>(FindObjectsSortMode.None);
                    foreach (var clock in globalClocks)
                    {
                        if (clock.clockKey == epochClockKey)
                        {
                            globalClock = clock;
                            break;
                        }
                    }

                    if (globalClock != null)
                    {
                        _logger?.LogInformation($"Successfully connected to Epoch clock '{epochClockKey}'");
                        isClockInitialized = true;
                        yield break;
                    }
                }
                catch (System.Exception e)
                {
                    _logger?.LogError(e, $"Exception during Epoch Clock initialization retry {retryCount + 1}/3");
                }

                retryCount++;
                yield return new WaitForSeconds(0.1f);
            }

            _logger?.LogWarning($"Failed to initialize Epoch clock '{epochClockKey}' after 3 attempts. Time scaling will not work correctly.");
            globalClock = null;
            isClockInitialized = true; // Still mark as initialized so we can proceed with default time scale
        }

        /// <summary>
        /// Legacy initialization method - now calls the base class Initialize
        /// </summary>
        private new void Initialize()
        {
            // Call the base class Initialize method
            base.Initialize();
        }

        #region ProjectileManagerBase Implementation

        protected override void OnInitialize()
        {
            Debug.Log($"🛠️ [ProjectileManager] OnInitialize START - GameObject: {gameObject.name}, InstanceID: {GetInstanceID()}");
            
            try
            {
                Debug.Log($"🛠️ [ProjectileManager] Adding ProjectileGrid component...");
                // Initialize core components
                projectileGrid = gameObject.AddComponent<ProjectileGrid>();
                Debug.Log($"🛠️ [ProjectileManager] ProjectileGrid added: {(projectileGrid != null ? "✅" : "❌")}");

                Debug.Log($"🛠️ [ProjectileManager] Getting singleton dependencies...");
                // Try to get dependencies, but don't fail if they're not ready yet
                projectilePool = ProjectilePool.Instance;
                projectileSpawner = ProjectileSpawner.Instance;

                Debug.Log($"🛠️ [ProjectileManager] Dependency check results:");
                Debug.Log($"   - ProjectilePool.Instance: {(projectilePool != null ? "✅ FOUND" : "❌ NULL")}");
                Debug.Log($"   - ProjectileSpawner.Instance: {(projectileSpawner != null ? "✅ FOUND" : "❌ NULL")}");

                if (projectilePool == null)
                {
                    Debug.LogWarning($"🛠️ [ProjectileManager] ProjectilePool not available yet - will retry in Start if needed");
                    _logger?.LogWarning("ProjectilePool not available yet - will retry in Start if needed");
                }

                if (projectileSpawner == null)
                {
                    Debug.LogWarning($"🛠️ [ProjectileManager] ProjectileSpawner not available yet - will retry in Start if needed");
                    _logger?.LogWarning("ProjectileSpawner not available yet - will retry in Start if needed");
                }

                Debug.Log($"🛠️ [ProjectileManager] Initializing arrays and data structures...");
                // Initialize arrays and data structures
                projectileArray = new IProjectile[MAX_PROJECTILES];
                activeSlots = new bool[MAX_PROJECTILES];
                playerProjectileArray = new IProjectile[MAX_PROJECTILES];
                enemyProjectileArray = new IProjectile[MAX_PROJECTILES];
                playerProjectileCount = 0;
                enemyProjectileCount = 0;

                if (projectileGrid != null)
                {
                    nearbyProjectilesList = projectileGrid.GetPooledResultsList();
                    Debug.Log($"🛠️ [ProjectileManager] ProjectileGrid pooled results list initialized");
                }

                Debug.Log($"🛠️ [ProjectileManager] Core initialization completed successfully");
                _logger?.LogInformation("Core initialization completed successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"🛠️ [ProjectileManager] CRITICAL ERROR during initialization: {e.Message}");
                Debug.LogError($"🛠️ [ProjectileManager] Stack trace: {e.StackTrace}");
                _logger?.LogError(e, "Error during core initialization");
                throw; // Re-throw to let base class handle the error
            }
        }

        protected override void OnShutdown()
        {
            try
            {
                _logger?.LogInformation("Shutting down...");

                // Clear all projectiles
                ClearAllProjectiles();

                // Unregister from Cadance events
                if (Cadance.Instance != null)
                {
                    Cadance.Instance.UnregisterForAllEvents(this);
                }

                // Clean up trail pool
                ClearTrailPool();

                // Clean up job system
                if (projectileJobSystem != null)
                {
                    projectileJobSystem.Dispose();
                }

                _logger?.LogInformation("Shutdown completed");
            }
            catch (System.Exception e)
            {
                _logger?.LogError(e, "Error during shutdown");
            }
        }

        protected override void OnReset()
        {
            try
            {
                _logger?.LogInformation("Resetting ProjectileManager...");

                // Clear all active projectiles
                ClearAllProjectiles();

                // Reset counters
                playerProjectileCount = 0;
                enemyProjectileCount = 0;
                currentProjectileIndex = 0;

                // Clear tracking data
                homingProjectileIds.Clear();
                lastPositions.Clear();
                enemyTransforms.Clear();

                // Reset transition state
                isTransitioning = false;

                _logger?.LogInformation("Reset completed");
            }
            catch (System.Exception e)
            {
                _logger?.LogError(e, "Error during reset");
            }
        }

        protected override string GetCustomStatus()
        {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            var status = new System.Text.StringBuilder();
            status.AppendLine($"Active Projectiles: {GetActiveProjectileCount()}");
            status.AppendLine($"Player Projectiles: {playerProjectileCount}");
            status.AppendLine($"Enemy Projectiles: {enemyProjectileCount}");
            status.AppendLine($"Homing Projectiles: {homingProjectileIds.Count}");
            status.AppendLine($"Clock Initialized: {isClockInitialized}");
            status.AppendLine($"Is Transitioning: {isTransitioning}");
            status.AppendLine($"Job System Active: {projectileJobSystem != null}");
            return status.ToString();
#else
            return string.Empty;
#endif
        }

        #endregion

        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            StartCoroutine(InitializeAfterSceneLoad());
        }

        private IEnumerator InitializeAfterSceneLoad()
        {
            yield return new WaitForSeconds(0.1f);
            ReRegisterEnemiesAndProjectiles();
        }

        private void Update()
        {
            if (!IsInitialized || !isClockInitialized || isTransitioning) return;
            if (projectileJobSystem == null) return;

            try
            {
                UpdateTrails();
                UpdateGroupAttack();

                // Schedule movement update with Epoch time
                // Use Epoch's computed delta time which already includes all time scaling
                float epochDeltaTime = globalClock != null ? globalClock.DeltaTime : Time.deltaTime;
                projectileJobSystem.ScheduleProjectileMovement(epochDeltaTime);

                // Update positions after job completion
                projectileJobSystem.CompleteProjectileUpdate();
                UpdateProjectilesByDistance();

                ProcessProjectileRequests();
                ProcessCollisionResults();
                UpdateProjectileTargets();
            }
            catch (System.Exception e)
            {
                _logger?.LogError(e, "Error in Update");
            }
        }

        private void UpdateProjectilesByDistance()
        {
            if (!IsInitialized || projectileArray == null) return;

            var playerPos = Camera.main?.transform.position ?? Vector3.zero;
            float currentTimeScale = globalClock != null ? globalClock.LocalTimeScale : 1f;
            float scaledUpdateInterval = updateInterval;
            float scaledNearUpdateInterval = nearUpdateInterval;

            // Process all active projectiles
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (!activeSlots[i]) continue;

                var projectile = projectileArray[i];
                if (projectile == null || projectile.GameObject == null || !projectile.GameObject.activeInHierarchy)
                {
                    activeSlots[i] = false;
                    projectileArray[i] = null; // Clear the reference
                    continue;
                }

                Vector3 oldPosition = projectile.Transform.position;

                // Get updated position from job system
                Vector3 newPosition = projectileJobSystem.GetProjectilePosition(i);
                Quaternion newRotation = projectileJobSystem.GetProjectileRotation(i);
                Vector3 newVelocity = projectileJobSystem.GetProjectileVelocity(i);

                // PHASE 5D: Apply job results through interface (new system only)
                projectile.Transform.position = newPosition;
                projectile.Transform.rotation = newRotation;
                if (projectile.Rigidbody != null)
                {
                    projectile.Rigidbody.linearVelocity = newVelocity;
                }

                // Only update grid if moved significantly
                if (Vector3.Distance(oldPosition, newPosition) > POSITION_UPDATE_THRESHOLD)
                {
                    projectileGrid.UpdateProjectileGridPosition(projectile, oldPosition, newPosition);
                }
            }

            UpdateProjectileLists();
        }

        private void UpdateProjectileLists()
        {
            playerProjectileCount = 0;
            enemyProjectileCount = 0;

            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (projectileArray[i] != null && projectileArray[i].GameObject != null && projectileArray[i].GameObject.activeInHierarchy)
                {
                    if (projectileArray[i].IsPlayerShot)
                    {
                        playerProjectileArray[playerProjectileCount] = projectileArray[i];
                        playerProjectileCount++;
                    }
                    else
                    {
                        enemyProjectileArray[enemyProjectileCount] = projectileArray[i];
                        enemyProjectileCount++;
                    }
                }
                else if (projectileArray[i] != null && projectileArray[i].GameObject == null)
                {
                    // Clean up destroyed projectile references
                    activeSlots[i] = false;
                    projectileArray[i] = null;
                }
            }
        }

        private void OnSceneTransitionStart()
        {
            isTransitioning = true;
            ClearAllProjectiles();
        }

        private void OnSceneTransitionEnd()
        {
            isTransitioning = false;
        }

        private void OnSceneUnloaded(Scene scene)
        {
            ClearAllProjectiles();
        }

        // PHASE 5D: Legacy method removed - use RegisterProjectileInterface(IProjectile) instead

        /// <summary>
        /// Main registration method - now works with IProjectile interface
        /// </summary>
        public void RegisterProjectileInterface(IProjectile projectile)
        {
            if (projectile == null || isTransitioning) return;

            int index = projectile.ProjectileIndex;
            if (index < 0 || index >= MAX_PROJECTILES)
            {
                index = GetNextProjectileIndex();
                if (index == -1) return; // No slots available
                projectile.ProjectileIndex = index;
            }

            lock (activeSlots) // Add thread safety
            {
                projectileArray[index] = projectile;
                activeSlots[index] = true;

                // Update job system data using interface properties
                Vector3 targetPos = projectile.CurrentTarget != null ?
                    projectile.CurrentTarget.position :
                    projectile.Transform.position + projectile.Transform.forward * 100f;

                projectileJobSystem.UpdateProjectileMovementData(
                    index,
                    projectile.Transform.position,
                    projectile.Transform.rotation,
                    projectile.Rigidbody.linearVelocity,
                    targetPos,
                    projectile.Homing && projectile.CurrentTarget != null,
                    10f, // Default rotate speed
                    projectile.BulletSpeed,
                    1f,
                    projectile.Lifetime
                );

                // Update player/enemy arrays
                if (projectile.IsPlayerShot)
                {
                    playerProjectileArray[playerProjectileCount] = projectile;
                    playerProjectileCount++;
                }
                else
                {
                    enemyProjectileArray[enemyProjectileCount] = projectile;
                    enemyProjectileCount++;
                }
            }

            // Register with tracking manager for radar support
            if (ProjectileTrackingManager.Instance != null)
            {
                ProjectileTrackingManager.Instance.RegisterProjectileInterface(projectile);
            }
        }

        // Duplicate RegisterProjectileInterface method removed - using unified version above

        // PHASE 5D: Legacy method removed - use UnregisterProjectileInterface(IProjectile) instead

        /// <summary>
        /// Main unregistration method - now works with IProjectile interface
        /// </summary>
        public void UnregisterProjectileInterface(IProjectile projectile)
        {
            if (projectile == null) return;

            int index = projectile.ProjectileIndex;
            if (!IsValidProjectileIndex(index)) return;

            lock (activeSlots)
            {
                // Log enemy projectile deaths
                if (!projectile.IsPlayerShot && ProjectileLogger.Instance != null)
                {
                    ProjectileDeathReason reason = ProjectileDeathReason.Timeout; // Simplified for interface

                    var player = GameObject.FindWithTag("Player")?.GetComponent<PlayerHealth>();
                    float currentHealth = player != null ? player.CurrentHealth : 0;

                    ProjectileLogger.Instance.LogProjectileDeath(
                        projectile.InstanceID.ToString(),
                        reason,
                        projectile.DamageAmount,
                        currentHealth,
                        currentHealth,
                        projectile.Transform.position
                    );
                }

                // Clear from main array and mark slot as available
                projectileArray[index] = null;
                activeSlots[index] = false;

                // Deactivate in job system
                projectileJobSystem?.DeactivateProjectile(index);

                // Remove from player/enemy arrays by shifting remaining elements
                if (projectile.IsPlayerShot)
                {
                    RemoveFromPlayerArray(projectile);
                }
                else
                {
                    RemoveFromEnemyArray(projectile);
                }

                // Remove from grid (if legacy projectile)
                // PHASE 5D: Remove from grid using interface
                projectileGrid?.RemoveProjectile(projectile);

                // Unregister homing projectile
                homingProjectileIds.Remove(projectile.InstanceID);
            }
        }

        private void RemoveFromPlayerArray(IProjectile projectile)
        {
            for (int i = 0; i < playerProjectileCount; i++)
            {
                if (playerProjectileArray[i] == projectile)
                {
                    // Shift remaining elements
                    Array.Copy(playerProjectileArray, i + 1, playerProjectileArray, i, playerProjectileCount - i - 1);
                    playerProjectileArray[playerProjectileCount - 1] = null;
                    playerProjectileCount--;
                    break;
                }
            }
        }

        private void RemoveFromEnemyArray(IProjectile projectile)
        {
            for (int i = 0; i < enemyProjectileCount; i++)
            {
                if (enemyProjectileArray[i] == projectile)
                {
                    // Shift remaining elements
                    Array.Copy(enemyProjectileArray, i + 1, enemyProjectileArray, i, enemyProjectileCount - i - 1);
                    enemyProjectileArray[enemyProjectileCount - 1] = null;
                    enemyProjectileCount--;
                    break;
                }
            }
        }

        public int GetNextProjectileIndex()
        {
            lock (activeSlots) // Add thread safety since this might be called from multiple threads
            {
                // First try to reuse the current index if possible
                if (currentProjectileIndex < MAX_PROJECTILES && !activeSlots[currentProjectileIndex])
                {
                    int index = currentProjectileIndex;
                    currentProjectileIndex = (currentProjectileIndex + 1) % MAX_PROJECTILES;
                    return index;
                }

                // If current index is not available, search for the next available slot
                for (int i = 0; i < MAX_PROJECTILES; i++)
                {
                    if (!activeSlots[i])
                    {
                        currentProjectileIndex = (i + 1) % MAX_PROJECTILES;
                        return i;
                    }
                }

                _logger?.LogWarning("No free projectile slots available!");
                return -1;
            }
        }

        // Add a method to check if an index is valid and active
        public bool IsValidProjectileIndex(int index)
        {
            if (index < 0 || index >= MAX_PROJECTILES) return false;
            return activeSlots[index];
        }

        // Add a method to validate and get a projectile by index
        public IProjectile GetProjectileByIndex(int index)
        {
            if (!IsValidProjectileIndex(index)) return null;
            return projectileArray[index];
        }

        private void OnEnable()
        {
            SceneManager.sceneLoaded += OnSceneLoaded;
        }

        private void OnDisable()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }

        private void ProcessProjectileRequests()
        {
            if (isTransitioning) return;

            // Scale requests based on current time scale from Epoch
            float timeScale = globalClock != null ? globalClock.TimeScale : 1f;
            int requestsToProcess = Mathf.CeilToInt(staticShootingRequestsPerFrame * timeScale);

            for (int i = 0; i < requestsToProcess && ProjectilePool.Instance.HasPendingRequests(); i++)
            {
                if (ProjectilePool.Instance.TryDequeueProjectileRequest(out ProjectileSpawnRequest request))
                {
                    // PHASE 5D: Use interface directly
                    var projectileInterface = projectilePool.GetProjectileInterface();
                    if (projectileInterface != null)
                    {
                        projectileSpawner.ProcessShootProjectileInterface(request, projectileInterface);
                    }
                }
            }
        }

        private void ProcessCollisionResults()
        {
            if (projectileArray == null) return;

            // Clear and reuse the list without LINQ
            projectilesToProcess.Clear();
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (!activeSlots[i]) continue;
                var projectile = projectileArray[i];
                if (projectile != null && projectile.GameObject != null && projectile.GameObject.activeInHierarchy)
                {
                    projectilesToProcess.Add(projectile);
                }
                else if (projectile != null && projectile.GameObject == null)
                {
                    // Clean up destroyed projectile references
                    activeSlots[i] = false;
                    projectileArray[i] = null;
                }
            }

            foreach (var projectile in projectilesToProcess)
            {
                if (projectile == null || projectile.GameObject == null || !projectile.GameObject.activeInHierarchy) continue;

                bool useRaycast = ShouldUseRaycastForProjectile(projectile);
                if (useRaycast)
                {
                    // Use RaycastNonAlloc to avoid allocations
                    Vector3 direction = projectile.Transform.forward;
                    float rayDistance = projectile.BulletSpeed * Time.deltaTime;
                    int layerMask = GetLayerMaskForProjectile(projectile);

                    int hitCount = Physics.RaycastNonAlloc(
                        projectile.Transform.position,
                        direction,
                        raycastHits,
                        rayDistance,
                        layerMask,
                        QueryTriggerInteraction.Collide
                    );

                    for (int i = 0; i < hitCount; i++)
                    {
                        var hit = raycastHits[i];
                        if (hit.collider != null)
                        {
                            // Handle collision through interface
                            HandleProjectileCollision(projectile, hit.collider);
                        }
                    }
                }
                else
                {
                    // PHASE 5D: Use interface-based collision detection for new system
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    _logger?.LogDebug("New system projectile collision detection for projectile ID: {ProjectileInstanceID}", projectile.InstanceID);
#endif
                    // TODO: Implement new collision system using interface
                }
            }
        }

        /// <summary>
        /// Handle collision for any projectile type through the interface
        /// </summary>
        private void HandleProjectileCollision(IProjectile projectile, Collider collider)
        {
            // PHASE 5D: Handle collision using interface (new system only)
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            _logger?.LogDebug("Projectile collision: Projectile ID {ProjectileInstanceID} hit {ColliderName}", projectile.InstanceID, collider.name);
#endif
            // TODO: Implement new collision system using interface
        }

        private bool ShouldUseRaycastForProjectile(IProjectile projectile)
        {
            if (projectile == null || !projectile.GameObject.activeInHierarchy) return false;

            // Always use raycast for high-speed projectiles
            float speedThreshold = 50f; // Adjust based on your game's scale
            if (projectile.BulletSpeed > speedThreshold) return true;

            // Use raycast if projectile moved significantly since last frame
            Vector3 currentPos = projectile.Transform.position;
            if (!lastPositions.TryGetValue(projectile.GameObject, out Vector3 lastPos))
            {
                lastPositions[projectile.GameObject] = currentPos;
                return false;
            }

            float distanceMoved = Vector3.Distance(currentPos, lastPos);
            lastPositions[projectile.GameObject] = currentPos;

            // Use raycast if moved more than half a grid cell
            return distanceMoved > (ProjectileGrid.GRID_CELL_SIZE * 0.5f);
        }

        public void CompleteRunningJobs()
        {
            if (projectileJobSystem != null)
            {
                projectileJobSystem.CompleteProjectileUpdate();
            }
        }

        public void UpdateProjectileTargets()
        {
            if (projectileArray == null) return;

            // Log the contents of the homing set at the start of the update
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (homingProjectileIds.Count > 0 && enableHomingDebugLogs)
            {
                string idList = string.Join(", ", homingProjectileIds);
                _logger?.LogDebug("[PM_UpdateTargets_Start] Current homingProjectileIds: [{HomingIdList}]", idList);
            }
#endif

            var playerTransform = GameObject.FindWithTag("Player")?.transform;
            if (playerTransform == null) return;

            // Create a temporary list to avoid modification issues
            var homingIdsSnapshot = new List<int>(homingProjectileIds); // Use a snapshot for iteration
            foreach (var projectileId in homingIdsSnapshot) // Iterate over snapshot
            {
                // Find the projectile that has this instanceID
                IProjectile projectileToUpdate = null;
                int jobIndex = -1;
                for (int i = 0; i < MAX_PROJECTILES; ++i)
                {
                    if (activeSlots[i] && projectileArray[i] != null && projectileArray[i].InstanceID == projectileId)
                    {
                        projectileToUpdate = projectileArray[i];
                        jobIndex = i;
                        break;
                    }
                }

                if (projectileToUpdate != null && projectileToUpdate.GameObject.activeInHierarchy && !projectileToUpdate.IsPlayerShot)
                {
                    // Apply accuracy to target position
                    Vector3 targetPos = playerTransform.position;
                    if (projectileAccuracy < 1f)
                    {
                        float inaccuracyRadius = (1f - projectileAccuracy) * 5f; // 5 units max spread at 0 accuracy
                        Vector2 randomOffset = UnityEngine.Random.insideUnitCircle * inaccuracyRadius;
                        targetPos += new Vector3(randomOffset.x, 0, randomOffset.y);
                    }

                    // PHASE 5D: Handle virtual target through interface (new system)
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    _logger?.LogDebug("New system projectile targeting - ID: {ProjectileId}", projectileId);
#endif
                    // TODO: Implement new targeting system using interface
                }
                else
                {
                    // Clean up invalid entries from the actual set if the projectile is no longer valid or is a player shot
                    if (projectileToUpdate == null || !projectileToUpdate.GameObject.activeInHierarchy || projectileToUpdate.IsPlayerShot)
                    {
                        // This check is a bit redundant if we iterate a snapshot, but good for direct modification logic
                        homingProjectileIds.Remove(projectileId);
                    }
                }
            }
        }

        // PHASE 5D: Legacy NotifyEnemyHit method removed - use NotifyEnemyHitInterface instead

        /// <summary>
        /// Unified enemy hit notification using IProjectile interface
        /// </summary>
        public void NotifyEnemyHitInterface(GameObject enemy, IProjectile projectile)
        {
            if (projectile.IsPlayerShot)
            {
                PlayerLocking.Instance.RemoveLockedEnemy(enemy.transform);
            }
        }

        public Transform FindNearestEnemy(Vector3 position)
        {
            if (Time.time - lastEnemyUpdateTime > ENEMY_UPDATE_INTERVAL)
            {
                UpdateEnemyTransforms();
            }

            Transform nearestEnemy = null;
            float nearestDistanceSqr = float.MaxValue;
            Vector3 pos = position; // Cache to avoid struct copies

            foreach (var kvp in enemyTransforms)
            {
                Transform enemy = kvp.Value;
                if (enemy != null)
                {
                    float distanceSqr = (enemy.position - pos).sqrMagnitude;
                    if (distanceSqr < nearestDistanceSqr)
                    {
                        nearestDistanceSqr = distanceSqr;
                        nearestEnemy = enemy;
                    }
                }
            }

            return nearestEnemy;
        }

        private void UpdateEnemyTransforms()
        {
            enemyTransforms.Clear();
            var enemies = GameObject.FindGameObjectsWithTag("Enemy");
            foreach (var enemy in enemies)
            {
                enemyTransforms[enemy.GetInstanceID()] = enemy.transform;
            }
            lastEnemyUpdateTime = Time.time;
        }

        public void OnWaveStart()
        {
            lastEnemyUpdateTime = 0f;
            lastPositions.Clear();
            enemyTransforms.Clear();

            // Find and cache the SplineController when wave starts
            GameObject playerPlane = GameObject.FindGameObjectWithTag("PlayerPlane");
            if (playerPlane != null)
            {
                playerSplineController = playerPlane.GetComponent<SplineController>();
                if (playerSplineController != null)
                {
                    _logger?.LogInformation("Found SplineController on PlayerPlane. Spline Ready: {IsReady}, Spline: {HasSpline}", playerSplineController.IsReady, (playerSplineController.Spline != null));
                }
                else
                {
                    _logger?.LogError("PlayerPlane found but missing SplineController component");
                }
            }
            else
            {
                _logger?.LogError("Could not find GameObject with tag 'PlayerPlane'");
            }
        }

        public void OnWaveEnd()
        {
            ClearAllProjectiles();
            lastEnemyUpdateTime = 0f;
            lastPositions.Clear();
            enemyTransforms.Clear();
            playerSplineController = null;  // Clear spline controller reference
        }

        public void RegisterHomingProjectile(int projectileId)
        {
            homingProjectileIds.Add(projectileId);
        }

        public void UnregisterHomingProjectile(int projectileId)
        {
            // Log before removal
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (homingProjectileIds.Contains(projectileId) && enableHomingDebugLogs) // Check if it exists before logging removal attempt
            {
                _logger?.LogDebug("[PM_UnregisterHoming] Attempting to unregister homing for PID: {ProjectileId}", projectileId);
            }
#endif
            homingProjectileIds.Remove(projectileId);
        }

        public IReadOnlyCollection<int> GetActiveHomingProjectileIds()
        {
            return homingProjectileIds;
        }

        public ProjectileJobSystem GetProjectileJobSystem()
        {
            return projectileJobSystem;
        }

        /// <summary>
        /// Spawn a projectile using the unified IProjectile interface (supports both legacy and new systems)
        /// </summary>
        public IProjectile SpawnProjectileInterface(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming = false,
            Material material = null,
            Transform target = null,
            bool isPlayerProjectile = false)
        {
            Debug.Log($"[ProjectileManager] SpawnProjectileInterface called - Position: {position}, IsPlayerProjectile: {isPlayerProjectile}, ProjectileSpawner.Instance: {ProjectileSpawner.Instance != null}");

            if (!ProjectileSpawner.Instance)
            {
                Debug.LogError("[ProjectileManager] ProjectileSpawner.Instance is null - cannot spawn projectile");
                _logger?.LogError("ProjectileSpawner.Instance is null");
                return null;
            }

            if (isPlayerProjectile && target != null)
            {
                // Create visual trail for player shots
                CreatePlayerShotTrail(position, target.position);
            }

            if (isPlayerProjectile)
            {
                // PHASE 4B: Use interface-based method for player projectiles with correct parameters
                var playerProjectileInterface = ProjectileSpawner.Instance.ShootPlayerProjectileInterface(
                    position, rotation, damage, speed, scale, lifetime, enableHoming, target);
                return playerProjectileInterface;
            }

            // PHASE 4B: Use interface-based method for enemy projectiles
            var projectileInterface = ProjectileSpawner.Instance.ShootProjectileFromEnemy(
                position,
                rotation,
                speed,
                lifetime,
                scale,
                damage,
                enableHoming,
                material,
                "Test",
                projectileAccuracy,
                target
            );

            if (projectileInterface != null && enableHoming)
            {
                // Log right before registration
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableHomingDebugLogs)
                {
                    _logger?.LogDebug("[PM_SpawnInterface] Intending to register homing for PID: {ProjectileInstanceID} because enableHoming parameter was true", projectileInterface.InstanceID);
                }
#endif
                RegisterHomingProjectile(projectileInterface.InstanceID);
            }

            // PHASE 5D: Handle job system integration for new system only
            if (projectileInterface != null && projectileJobSystem != null)
            {
                // New system projectiles use interface-based job system integration
                Vector3 targetPosition = projectileInterface.CurrentTarget != null
                    ? projectileInterface.CurrentTarget.position
                    : projectileInterface.Transform.position + projectileInterface.Transform.forward * 100f;

                projectileJobSystem.UpdateProjectileMovementData(
                    projectileInterface.InstanceID,
                    projectileInterface.Transform.position,
                    projectileInterface.Transform.rotation,
                    projectileInterface.Rigidbody?.linearVelocity ?? Vector3.zero,
                    targetPosition,
                    projectileInterface.Homing && projectileInterface.CurrentTarget != null,
                    20f, // Default rotate speed
                    projectileInterface.BulletSpeed,
                    1f, // Initial timeScale
                    projectileInterface.Lifetime
                );
            }

            return projectileInterface;
        }

        // Legacy SpawnProjectile method removed - use SpawnProjectileInterface instead

        // Legacy SpawnProjectile methods removed - use SpawnProjectileInterface instead

        private void FixedUpdate()
        {
            if (!IsInitialized || !isClockInitialized || projectileJobSystem == null) return;

            try
            {
                // Use Epoch's fixed delta time which already includes all time scaling
                float epochFixedDeltaTime = globalClock != null ? globalClock.FixedDeltaTime : Time.fixedDeltaTime;
                projectileJobSystem.ScheduleProjectileMovement(epochFixedDeltaTime);
                projectileJobSystem.CompleteProjectileUpdate();

                // Apply results to all projectiles with time scaling
                for (int i = 0; i < MAX_PROJECTILES; i++)
                {
                    if (projectileArray[i] != null && projectileArray[i].GameObject != null && projectileArray[i].GameObject.activeInHierarchy)
                    {
                        Vector3 newPosition = projectileJobSystem.GetProjectilePosition(i);
                        Quaternion newRotation = projectileJobSystem.GetProjectileRotation(i);
                        Vector3 newVelocity = projectileJobSystem.GetProjectileVelocity(i);

                        // PHASE 5D: Apply job results through interface (new system only)
                        projectileArray[i].Transform.position = newPosition;
                        projectileArray[i].Transform.rotation = newRotation;
                        if (projectileArray[i].Rigidbody != null)
                        {
                            projectileArray[i].Rigidbody.linearVelocity = newVelocity;
                        }
                    }
                    else if (projectileArray[i] != null && projectileArray[i].GameObject == null)
                    {
                        // Clean up destroyed projectile references
                        activeSlots[i] = false;
                        projectileArray[i] = null;
                    }
                }
            }
            catch (System.Exception e)
            {
                _logger?.LogError(e, "Error in FixedUpdate");
            }
        }

        protected override void OnDestroy()
        {
            // Perform ProjectileManager-specific cleanup first
            if (projectileGrid != null && nearbyProjectilesList != null)
            {
                projectileGrid.ReturnResultsList(nearbyProjectilesList);
            }

            if (Cadance.Instance != null)
            {
                Cadance.Instance.UnregisterForEvents(coordinatedAttackEventID, OnCoordinatedAttackBeat);
            }

            // Call base class OnDestroy which will handle Shutdown() and IsInitialized properly
            base.OnDestroy();
        }

        public void ClearAllProjectiles()
        {
            _logger?.LogInformation("Clearing all projectiles");

            // Complete any running jobs first
            if (projectileJobSystem != null)
            {
                projectileJobSystem.CompleteProjectileUpdate();
            }

            // Clear all arrays
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (activeSlots[i] && projectileArray[i] != null)
                {
                    // Properly clean up projectile
                    projectileJobSystem?.DeactivateProjectile(i);

                    // PHASE 5D: Remove from grid using interface - only if GameObject still exists
                    if (projectileArray[i].GameObject != null)
                    {
                        projectileGrid?.RemoveProjectile(projectileArray[i]);
                    }

                    projectileArray[i] = null;
                }
                activeSlots[i] = false;
            }

            // Reset counts
            playerProjectileCount = 0;
            enemyProjectileCount = 0;
            Array.Clear(playerProjectileArray, 0, MAX_PROJECTILES);
            Array.Clear(enemyProjectileArray, 0, MAX_PROJECTILES);

            // Clear grid and other collections
            projectileGrid?.ClearGrids();
            homingProjectileIds.Clear();
            lastPositions.Clear();
        }

        public void ReRegisterEnemiesAndProjectiles()
        {
            ClearAllProjectiles();
            GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
            foreach (GameObject enemy in enemies)
            {
                var enemyCore = enemy.GetComponent<EnemyCore>();
                if (enemyCore != null)
                {
                    enemyCore.RegisterProjectiles();
                }
            }
        }

        private int GetLayerMaskForProjectile(IProjectile projectile)
        {
            return projectile.IsPlayerShot ? enemyLayerMask : playerLayerMask;
        }

        public IProjectile GetProjectileById(int id)
        {
            if (id < 0 || id >= MAX_PROJECTILES || !activeSlots[id] || projectileArray[id] == null)
            {
                return null;
            }
            return projectileArray[id];
        }

        private void InitializeTrailPool()
        {
            GameObject trailContainer = new GameObject("PlayerShotTrails");
            trailContainer.transform.parent = transform;

            for (int i = 0; i < INITIAL_TRAIL_POOL_SIZE; i++)
            {
                GameObject trailObj = new GameObject($"Trail_{i}");
                trailObj.transform.parent = trailContainer.transform;

                LineRenderer line = trailObj.AddComponent<LineRenderer>();
                line.material = trailMaterial;
                line.startWidth = trailStartWidth;
                line.endWidth = trailEndWidth;
                line.positionCount = 2;
                line.useWorldSpace = true;
                line.gameObject.SetActive(false);

                trailPool.Enqueue(line);
            }
        }

        private LineRenderer GetTrailFromPool()
        {
            if (trailPool.Count == 0)
            {
                // Create new trail if pool is empty
                GameObject trailObj = new GameObject("Trail_Dynamic");
                trailObj.transform.parent = transform.Find("PlayerShotTrails");

                LineRenderer line = trailObj.AddComponent<LineRenderer>();
                line.material = trailMaterial;
                line.startWidth = trailStartWidth;
                line.endWidth = trailEndWidth;
                line.positionCount = 2;
                line.useWorldSpace = true;
                return line;
            }

            LineRenderer trail = trailPool.Dequeue();
            trail.gameObject.SetActive(true);
            return trail;
        }

        private void ReturnTrailToPool(LineRenderer trail)
        {
            trail.gameObject.SetActive(false);
            trailPool.Enqueue(trail);
        }

        public void CreatePlayerShotTrail(Vector3 startPosition, Vector3 targetPosition)
        {
            LineRenderer trail = GetTrailFromPool();

            trail.SetPosition(0, startPosition);
            trail.SetPosition(1, startPosition);

            PlayerShotTrail trailData = new PlayerShotTrail
            {
                lineRenderer = trail,
                startTime = Time.time, // Use regular time for trail start
                duration = trailDuration,
                startPos = startPosition,
                endPos = targetPosition,
                startColor = trailStartColor,
                endColor = trailEndColor
            };

            activeTrails.Add(trailData);
        }

        private void UpdateTrails()
        {
            float currentTimeScale = globalClock != null ? globalClock.LocalTimeScale : 1f;

            for (int i = activeTrails.Count - 1; i >= 0; i--)
            {
                PlayerShotTrail trail = activeTrails[i];
                // Use scaled time for trail progress
                float elapsed = (Time.time - trail.startTime) * currentTimeScale;
                float progress = elapsed / (trail.duration / currentTimeScale);

                if (progress >= 1f)
                {
                    ReturnTrailToPool(trail.lineRenderer);
                    activeTrails.RemoveAt(i);
                    continue;
                }

                // Update trail position
                Vector3 currentEnd = Vector3.Lerp(trail.startPos, trail.endPos, progress);
                trail.lineRenderer.SetPosition(1, currentEnd);

                // Update trail color and opacity
                Color currentColor = Color.Lerp(trail.startColor, trail.endColor, progress);
                trail.lineRenderer.startColor = trail.startColor;
                trail.lineRenderer.endColor = currentColor;
            }
        }

        private void UpdateGroupAttack()
        {
            if (!enableGroupAttacks) return;

            if (groupAttackQueue.Count < groupAttackConfig.minProjectilesForGroup)
            {
                // Check for new projectiles to add to queue
                for (int i = 0; i < enemyProjectileCount; i++)
                {
                    IProjectile projectile = enemyProjectileArray[i];
                    if (projectile != null && projectile.CurrentTarget != null)
                    {
                        // PHASE 5D: Group attack coordination for new system
                        // For now, assume all projectiles can participate in group attacks
                        bool canParticipate = true;

                        if (canParticipate)
                        {
                            float distanceToTarget = Vector3.Distance(projectile.Transform.position, projectile.CurrentTarget.position);
                            if (distanceToTarget <= groupAttackConfig.groupAttackRange)
                            {
                                // PHASE 5D: Group attack coordination handled by new system
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                                _logger?.LogDebug("[GroupAttack] Adding projectile {ProjectileInstanceID} to group attack", projectile.InstanceID);
#endif

                                groupAttackQueue.Enqueue(projectile);

                                // If this is the first projectile, start the group attack timer
                                if (groupAttackQueue.Count == 1)
                                {
                                    groupAttackStartTime = Time.time;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                                    _logger?.LogDebug("[GroupAttack] Starting new group attack with {GroupAttackQueueCount} projectiles", groupAttackQueue.Count);
#endif
                                }

                                // PHASE 5D: Add visual effect for group attack
                                if (projectile.GameObject.TryGetComponent<Renderer>(out var renderer))
                                {
                                    renderer.material.color = groupAttackConfig.groupAttackColor;
                                }
                            }
                        }
                    }
                }
            }
            else if (groupAttackQueue.Count > 0)
            {
                var player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    float timeInGroup = Time.time - groupAttackStartTime;

                    // Position projectiles in a circle around the player
                    int index = 0;
                    foreach (var projectile in groupAttackQueue)
                    {
                        if (projectile != null && projectile.GameObject.activeInHierarchy)
                        {
                            // Calculate position in circle
                            float angle = (2f * Mathf.PI * index) / groupAttackQueue.Count;
                            Vector3 offset = new Vector3(
                                Mathf.Cos(angle) * groupAttackConfig.groupAttackDistance,
                                0f,
                                Mathf.Sin(angle) * groupAttackConfig.groupAttackDistance
                            );

                            // Move projectile to position
                            Vector3 targetPos = player.transform.position + offset;
                            Vector3 moveDir = (targetPos - projectile.Transform.position).normalized;
                            float moveSpeed = projectile.BulletSpeed * groupAttackConfig.groupAttackSpeed * Time.deltaTime;
                            projectile.Transform.position += moveDir * moveSpeed;

                            // Face player
                            projectile.Transform.forward = (player.transform.position - projectile.Transform.position).normalized;
                            index++;
                        }
                    }

                    // Start attacking after delay
                    if (timeInGroup >= groupAttackConfig.groupAttackDelay)
                    {
                        var projectile = groupAttackQueue.Dequeue();
                        if (projectile != null && projectile.GameObject.activeInHierarchy)
                        {
                            // PHASE 5D: Reset coordination status for new system
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                            _logger?.LogDebug("[ProjectileManager] Group attack projectile released: {ProjectileInstanceID}", projectile.InstanceID);
#endif

                            // Restore original color (simplified for new system)
                            if (projectile.GameObject.TryGetComponent<Renderer>(out var renderer))
                            {
                                renderer.material.color = Color.white; // Default color restoration
                            }

                            // Point at player
                            projectile.Transform.forward = (player.transform.position - projectile.Transform.position).normalized;
                        }
                    }
                }
            }
        }

        private void OnDrawGizmos()
        {
            if (!enableGroupAttacks || !groupAttackConfig?.showGroupDebugVisualization == true) return;

            var player = GameObject.FindGameObjectWithTag("Player");
            if (player == null) return;

            // Draw group attack range
            Gizmos.color = new Color(groupAttackConfig.groupAttackColor.r, groupAttackConfig.groupAttackColor.g, groupAttackConfig.groupAttackColor.b, 0.3f);
            Gizmos.DrawWireSphere(player.transform.position, groupAttackConfig.groupAttackRange);

            // Draw filled sphere
            Gizmos.color = new Color(groupAttackConfig.groupAttackColor.r, groupAttackConfig.groupAttackColor.g, groupAttackConfig.groupAttackColor.b, 0.1f);
            DrawSphere(player.transform.position, groupAttackConfig.groupAttackRange);

            if (Application.isPlaying && groupAttackQueue != null)
            {
                foreach (var projectile in groupAttackQueue)
                {
                    if (projectile != null && projectile.GameObject != null && projectile.GameObject.activeInHierarchy)
                    {
                        // Draw projectile position
                        Gizmos.color = groupAttackConfig.groupAttackColor;
                        DrawSphere(projectile.Transform.position, 0.5f);

                        // Draw line to player
                        Gizmos.DrawLine(projectile.Transform.position, player.transform.position);
                    }
                }
            }
        }

        // Helper method to draw a filled sphere
        private void DrawSphere(Vector3 center, float radius)
        {
            int segments = 16;
            int rings = 16;

            for (int i = 0; i <= rings; i++)
            {
                float phi = Mathf.PI * i / rings;
                for (int j = 0; j <= segments; j++)
                {
                    float theta = 2f * Mathf.PI * j / segments;

                    float x = radius * Mathf.Sin(phi) * Mathf.Cos(theta);
                    float y = radius * Mathf.Sin(phi) * Mathf.Sin(theta);
                    float z = radius * Mathf.Cos(phi);

                    Vector3 point = center + new Vector3(x, y, z);

                    if (i > 0 && j > 0)
                    {
                        Gizmos.DrawLine(point, center + new Vector3(
                            radius * Mathf.Sin(phi - Mathf.PI / rings) * Mathf.Cos(theta),
                            radius * Mathf.Sin(phi - Mathf.PI / rings) * Mathf.Sin(theta),
                            radius * Mathf.Cos(phi - Mathf.PI / rings)
                        ));
                    }

                    if (j > 0)
                    {
                        Gizmos.DrawLine(point, center + new Vector3(
                            radius * Mathf.Sin(phi) * Mathf.Cos(theta - 2f * Mathf.PI / segments),
                            radius * Mathf.Sin(phi) * Mathf.Sin(theta - 2f * Mathf.PI / segments),
                            radius * Mathf.Cos(phi)
                        ));
                    }
                }
            }
        }

        private void OnCoordinatedAttackBeat(CadanceEvent evt)
        {
            // Keep this method for future implementation
            // Currently using group attack system instead
        }

        // Add method to predict target position using spline
        public Vector3 PredictTargetPositionOnSpline(Vector3 projectilePosition, float projectileSpeed, Vector3 currentTargetPosition)
        {
            if (playerSplineController == null || !playerSplineController.IsReady || playerSplineController.Spline == null)
            {
                return currentTargetPosition;
            }

            try
            {
                CurvySpline spline = playerSplineController.Spline;
                float playerSpeed = playerSplineController.Speed;
                float currentTF = playerSplineController.RelativePosition;

                // Get current player position and direction
                Vector3 playerPos = spline.transform.TransformPoint(spline.Interpolate(currentTF));
                Vector3 playerDir = spline.transform.TransformDirection(spline.GetTangent(currentTF)).normalized;

                // Calculate time until intersection using law of cosines
                Vector3 toPlayer = playerPos - projectilePosition;
                float distanceToPlayer = toPlayer.magnitude;
                Vector3 toPlayerDir = toPlayer / distanceToPlayer;

                // Calculate angle between player's movement and line to projectile
                float cosAngle = Vector3.Dot(playerDir, toPlayerDir);
                float angle = Mathf.Acos(cosAngle);

                // Calculate speed ratio to determine how far ahead to look
                float speedRatio = playerSpeed / projectileSpeed;

                // MODIFICATION: Reduce lookAheadMultiplier for close distances
                float distanceFactor = Mathf.Clamp01(distanceToPlayer / 20f); // Normalize distance up to 20 units

                // Add prediction dampening for close ranges (exponential falloff based on distance)
                // This creates a sharper reduction in prediction as we get closer
                float predictionDampening = 0.5f; // Default value

                // PHASE 5D: Try to get the actual projectile by checking for projectiles near the position (new system)
                var nearbyProjectiles = Physics.OverlapSphere(projectilePosition, 0.5f);
                foreach (var collider in nearbyProjectiles)
                {
                    if (collider.TryGetComponent<IProjectile>(out var projectile))
                    {
                        // TODO: Implement steering config access through interface
                        // For now, use default dampening
                        predictionDampening = 0.5f;
                        break;
                    }
                }

                // Apply dampening effect with distance-based falloff
                float dampening = Mathf.Pow(distanceFactor, 1.0f + predictionDampening);
                float lookAheadMultiplier = Mathf.Lerp(1.0f, 5.0f, dampening * speedRatio);

                // Law of cosines: c² = a² + b² - 2ab*cos(C)
                // c = projectileSpeed * t
                // b = playerSpeed * t
                // Therefore: (projectileSpeed * t)² = distanceToPlayer² + (playerSpeed * t)² - 2 * distanceToPlayer * playerSpeed * t * cos(angle)

                // Solve quadratic equation: at² + bt + c = 0
                float a = projectileSpeed * projectileSpeed - playerSpeed * playerSpeed;
                float b = 2 * distanceToPlayer * playerSpeed * cosAngle;
                float c = -distanceToPlayer * distanceToPlayer;

                // Quadratic formula: t = (-b ± √(b² - 4ac)) / (2a)
                float discriminant = b * b - 4 * a * c;
                if (discriminant < 0)
                {
                    // No solution - player is moving away faster than projectile can catch up
                    return currentTargetPosition;
                }

                float t = (-b + Mathf.Sqrt(discriminant)) / (2 * a);
                if (t < 0)
                {
                    // Negative time - use the other solution
                    t = (-b - Mathf.Sqrt(discriminant)) / (2 * a);
                }

                // Calculate how far the player will travel in this time
                float distancePlayerTravels = playerSpeed * t * lookAheadMultiplier;

                // Get current curvature to adjust prediction
                float nextTF = Mathf.Min(1f, currentTF + 0.01f);
                Vector3 currentTangent = spline.GetTangent(currentTF);
                Vector3 nextTangent = spline.GetTangent(nextTF);
                float curvature = Vector3.Angle(currentTangent, nextTangent) / 0.01f;

                // MODIFICATION: Reduce curvature compensation for close distances
                float distanceScale = Mathf.Clamp01(distanceToPlayer / 10f);
                float curvatureMultiplier = 1f + Mathf.Abs(curvature) * 0.5f * distanceScale;
                distancePlayerTravels *= curvatureMultiplier;

                // Calculate the predicted position on the spline
                float predictedTF = currentTF + (distancePlayerTravels / spline.Length);

                // Handle closed splines
                if (spline.Closed)
                {
                    predictedTF = predictedTF % 1f;
                }
                else
                {
                    predictedTF = Mathf.Clamp01(predictedTF);
                }

                // Get the predicted world position
                Vector3 predictedPosition = spline.transform.TransformPoint(spline.Interpolate(predictedTF));

                // Additional safety check: if the predicted position would cause extreme steering
                // (like swerving away from a direct hit), blend toward direct position
                float angleToCurrentPosition = Vector3.Angle(toPlayerDir, (predictedPosition - projectilePosition).normalized);
                if (angleToCurrentPosition > 60f && distanceToPlayer < 10f)
                {
                    float angleFactor = Mathf.Clamp01((angleToCurrentPosition - 60f) / 30f);
                    float blendToDirectFactor = angleFactor * (1f - distanceFactor);
                    predictedPosition = Vector3.Lerp(predictedPosition, playerPos, blendToDirectFactor);
                }

                return predictedPosition;
            }
            catch (System.Exception e)
            {
                _logger?.LogError(e, "Error predicting spline position");
                return currentTargetPosition;
            }
        }

        // Get the player's eye level height for projectile approach calculations
        public float GetPlayerEyeLevel()
        {
            // Try to find the player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                return 0f;
            }

            // Get player's position
            Vector3 playerPosition = player.transform.position;

            // Get player's height by checking for a character controller or collider
            float playerHeight = 2f; // Default fallback height

            // Try to get player height from collider bounds
            Collider playerCollider = player.GetComponent<Collider>();
            if (playerCollider != null)
            {
                playerHeight = playerCollider.bounds.size.y;
            }

            // Eye level is typically around 90% of character height
            float eyeLevel = playerPosition.y + (playerHeight * 0.9f);

            // Cache the eye level value for efficiency if needed more frequently
            return eyeLevel;
        }

        // Adjust target position to approach from player eye level
        public Vector3 AdjustForVerticalApproach(Vector3 projectilePosition, Vector3 targetPosition, SteeringConfig steeringConfig)
        {
            if (steeringConfig == null || !steeringConfig.usePlayerEyeLevelApproach)
            {
                return targetPosition;
            }

            // Get player eye level
            float playerEyeLevel = GetPlayerEyeLevel();

            // Apply the configured offset
            float approachHeight = playerEyeLevel + steeringConfig.verticalApproachOffset;

            // Get distance to target
            float distanceToTarget = Vector3.Distance(projectilePosition, targetPosition);

            // Calculate strength factor based on distance
            float distanceFactor = Mathf.Clamp01(distanceToTarget / steeringConfig.verticalApproachDistance);

            // Apply vertical approach only when outside of inner arrival distance
            if (distanceToTarget > steeringConfig.innerArrivalDistance)
            {
                // Create a new position with adjusted height
                Vector3 adjustedPosition = targetPosition;

                // Apply height adjustment (weighted by distance and strength)
                float heightBlend = Mathf.Lerp(0, steeringConfig.verticalPathStrength, distanceFactor);
                adjustedPosition.y = Mathf.Lerp(targetPosition.y, approachHeight, heightBlend);

                return adjustedPosition;
            }

            return targetPosition;
        }

        /// <summary>
        /// Get the total count of active projectiles
        /// </summary>
        /// <returns>Number of active projectiles</returns>
        public int GetActiveProjectileCount()
        {
            if (activeSlots == null) return 0;

            int count = 0;
            for (int i = 0; i < MAX_PROJECTILES; i++)
            {
                if (activeSlots[i] && projectileArray[i] != null)
                {
                    count++;
                }
            }
            return count;
        }

        /// <summary>
        /// Clear the trail pool for cleanup
        /// </summary>
        private void ClearTrailPool()
        {
            // Clear active trails
            foreach (var trail in activeTrails)
            {
                if (trail?.lineRenderer != null)
                {
                    Destroy(trail.lineRenderer.gameObject);
                }
            }
            activeTrails.Clear();

            // Clear pooled trails
            while (trailPool.Count > 0)
            {
                var lineRenderer = trailPool.Dequeue();
                if (lineRenderer != null)
                {
                    Destroy(lineRenderer.gameObject);
                }
            }
        }
    }
}
