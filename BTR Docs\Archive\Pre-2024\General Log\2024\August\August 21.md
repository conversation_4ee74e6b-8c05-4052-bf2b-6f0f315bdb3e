# August 21

Working on Projectile accuracy, first improving the debugger to track what the accuracy is

SHow 8 and 15 percent as hit rate, seems really low

THis has increased to roughly 50%, working on making it even better

Significant improvements, at around 70% now, something worht monitorign moving forward. 

Still trying to improve this as it drops to 50% at times.

Enemy Snakes are working, but need refinement. Refinign the damagable parts aspect to work cleaner

Changing Wave patterns for shorter delays

ENemy bullets still have trouble hitting at times, 

Cmaera not acting very well, attempt to fix with old setup

Lookahead attribute is the big factor, cant be too much, screws things up

Adding a score / time boost when cahnging waves / sections. 

Adding gizmos to projecitles to see whats going wrong, what adjustmetns i need to make so that the enemies always get hit. 

Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl

Made things less efficient, need to try to get back to efficiency. I thoguht I kept burst for projectiles… need to revisit these issues. Run a diff one some uploads with cursor

Deleted Projecitel Path gizmo due to potential errors in physics calculations, 

Not entirely sure whats hogging performance, tracking it down - try not to waste too much time on this