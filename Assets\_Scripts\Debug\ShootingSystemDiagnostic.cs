using UnityEngine;
using BTR;
using BTR.Projectiles;
using System.Collections;

/// <summary>
/// Comprehensive diagnostic tool to identify why shooting is not working.
/// Checks all components in the shooting pipeline.
/// </summary>
public class ShootingSystemDiagnostic : MonoBehaviour
{
    [Header("Diagnostic Settings")]
    [SerializeField] private bool enableContinuousMonitoring = true;
    [SerializeField] private float diagnosticInterval = 1f;
    [SerializeField] private bool enableVerboseLogging = true;
    
    [Header("Component Status")]
    [SerializeField] private bool crosshairCoreExists = false;
    [SerializeField] private bool playerLockingExists = false;
    [SerializeField] private bool playerShootingExists = false;
    [SerializeField] private bool projectileManagerExists = false;
    [SerializeField] private bool projectileSpawnerExists = false;
    [SerializeField] private bool projectilePoolExists = false;
    [SerializeField] private bool projectileEffectManagerExists = false;
    
    [Header("Shooting State")]
    [SerializeField] private bool lockButtonPressed = false;
    [SerializeField] private bool triggeredLockFire = false;
    [SerializeField] private int lockedProjectileCount = 0;
    [SerializeField] private bool directShootingEnabled = false;
    [SerializeField] private bool bypassMusicalTiming = false;
    [SerializeField] private float currentTimeScale = 1f;
    
    [Header("System Dependencies")]
    [SerializeField] private string missingComponents = "";
    [SerializeField] private string criticalErrors = "";
    
    private CrosshairCore crosshairCore;
    private PlayerLocking playerLocking;
    private PlayerShooting playerShooting;
    
    private void Start()
    {
        Debug.Log("=== SHOOTING SYSTEM DIAGNOSTIC STARTED ===");
        PerformFullDiagnostic();
        
        if (enableContinuousMonitoring)
        {
            StartCoroutine(ContinuousMonitoring());
        }
    }
    
    private IEnumerator ContinuousMonitoring()
    {
        while (true)
        {
            yield return new WaitForSeconds(diagnosticInterval);
            PerformFullDiagnostic();
        }
    }
    
    [ContextMenu("Perform Full Diagnostic")]
    public void PerformFullDiagnostic()
    {
        Debug.Log("🔍 === SHOOTING SYSTEM DIAGNOSTIC ===");
        
        // Clear previous errors
        missingComponents = "";
        criticalErrors = "";
        
        // Check all components
        CheckCoreComponents();
        CheckProjectileSystem();
        CheckShootingState();
        CheckSystemDependencies();
        
        // Analyze overall status
        AnalyzeShootingReadiness();
        
        if (enableVerboseLogging)
        {
            LogDetailedStatus();
        }
    }
    
    private void CheckCoreComponents()
    {
        Debug.Log("🎯 Checking Core Components...");
        
        // CrosshairCore
        crosshairCore = FindFirstObjectByType<CrosshairCore>();
        crosshairCoreExists = crosshairCore != null;
        Debug.Log($"  CrosshairCore: {(crosshairCoreExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!crosshairCoreExists) missingComponents += "CrosshairCore, ";
        
        // PlayerLocking
        if (crosshairCore != null)
        {
            playerLocking = crosshairCore.GetComponent<PlayerLocking>();
        }
        else
        {
            playerLocking = FindFirstObjectByType<PlayerLocking>();
        }
        playerLockingExists = playerLocking != null;
        Debug.Log($"  PlayerLocking: {(playerLockingExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!playerLockingExists) missingComponents += "PlayerLocking, ";
        
        // PlayerShooting
        if (crosshairCore != null)
        {
            playerShooting = crosshairCore.GetComponent<PlayerShooting>();
        }
        else
        {
            playerShooting = FindFirstObjectByType<PlayerShooting>();
        }
        playerShootingExists = playerShooting != null;
        Debug.Log($"  PlayerShooting: {(playerShootingExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!playerShootingExists) missingComponents += "PlayerShooting, ";
    }
    
    private void CheckProjectileSystem()
    {
        Debug.Log("🚀 Checking Projectile System...");
        
        // ProjectileManager
        projectileManagerExists = ProjectileManager.Instance != null;
        Debug.Log($"  ProjectileManager: {(projectileManagerExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!projectileManagerExists) 
        {
            missingComponents += "ProjectileManager, ";
            criticalErrors += "ProjectileManager.Instance is null! ";
        }
        
        // ProjectileSpawner
        projectileSpawnerExists = ProjectileSpawner.Instance != null;
        Debug.Log($"  ProjectileSpawner: {(projectileSpawnerExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!projectileSpawnerExists) 
        {
            missingComponents += "ProjectileSpawner, ";
            criticalErrors += "ProjectileSpawner.Instance is null! ";
        }
        
        // ProjectilePool
        projectilePoolExists = ProjectilePool.Instance != null;
        Debug.Log($"  ProjectilePool: {(projectilePoolExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!projectilePoolExists) 
        {
            missingComponents += "ProjectilePool, ";
            criticalErrors += "ProjectilePool.Instance is null! ";
        }
        
        // ProjectileEffectManager
        projectileEffectManagerExists = ProjectileEffectManager.Instance != null;
        Debug.Log($"  ProjectileEffectManager: {(projectileEffectManagerExists ? "✅ FOUND" : "❌ MISSING")}");
        if (!projectileEffectManagerExists) 
        {
            missingComponents += "ProjectileEffectManager, ";
            criticalErrors += "ProjectileEffectManager.Instance is null! ";
        }
        
        // Check if ProjectileSpawner is fully initialized
        if (projectileSpawnerExists)
        {
            bool fullyInitialized = ProjectileSpawner.Instance.IsFullyInitialized;
            Debug.Log($"  ProjectileSpawner Fully Initialized: {(fullyInitialized ? "✅ YES" : "❌ NO")}");
            if (!fullyInitialized)
            {
                criticalErrors += "ProjectileSpawner not fully initialized! ";
            }
        }
    }
    
    private void CheckShootingState()
    {
        Debug.Log("🎮 Checking Shooting State...");
        
        currentTimeScale = Time.timeScale;
        Debug.Log($"  Time Scale: {currentTimeScale}");
        
        if (crosshairCore != null)
        {
            // Get direct shooting mode settings via reflection
            var directModeField = typeof(CrosshairCore).GetField("enableDirectShootingMode", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (directModeField != null)
            {
                directShootingEnabled = (bool)directModeField.GetValue(crosshairCore);
            }
            
            var bypassField = typeof(CrosshairCore).GetField("bypassMusicalTiming", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (bypassField != null)
            {
                bypassMusicalTiming = (bool)bypassField.GetValue(crosshairCore);
            }
            
            Debug.Log($"  Direct Shooting Enabled: {(directShootingEnabled ? "✅ YES" : "❌ NO")}");
            Debug.Log($"  Bypass Musical Timing: {(bypassMusicalTiming ? "✅ YES" : "❌ NO")}");
        }
        
        if (playerLocking != null)
        {
            triggeredLockFire = playerLocking.triggeredLockFire;
            lockedProjectileCount = playerLocking.GetLockedProjectileCount();
            
            Debug.Log($"  Triggered Lock Fire: {(triggeredLockFire ? "✅ YES" : "❌ NO")}");
            Debug.Log($"  Locked Projectile Count: {lockedProjectileCount}");
        }
    }
    
    private void CheckSystemDependencies()
    {
        Debug.Log("🔧 Checking System Dependencies...");
        
        // Check for missing GameObjects in scene
        var projectileManagerGO = GameObject.Find("ProjectileManager");
        Debug.Log($"  ProjectileManager GameObject: {(projectileManagerGO != null ? "✅ FOUND" : "❌ MISSING")}");
        
        var projectileSpawnerGO = GameObject.Find("ProjectileSpawner");
        Debug.Log($"  ProjectileSpawner GameObject: {(projectileSpawnerGO != null ? "✅ FOUND" : "❌ MISSING")}");
        
        var projectilePoolGO = GameObject.Find("ProjectilePool");
        Debug.Log($"  ProjectilePool GameObject: {(projectilePoolGO != null ? "✅ FOUND" : "❌ MISSING")}");
        
        // Check execution order issues
        if (projectileManagerExists && !projectileSpawnerExists)
        {
            criticalErrors += "ProjectileManager exists but ProjectileSpawner is missing! ";
        }
    }
    
    private void AnalyzeShootingReadiness()
    {
        Debug.Log("📊 Analyzing Shooting Readiness...");
        
        bool canShoot = true;
        string blockingIssues = "";
        
        // Check critical components
        if (!crosshairCoreExists)
        {
            canShoot = false;
            blockingIssues += "CrosshairCore missing; ";
        }
        
        if (!playerLockingExists)
        {
            canShoot = false;
            blockingIssues += "PlayerLocking missing; ";
        }
        
        if (!playerShootingExists)
        {
            canShoot = false;
            blockingIssues += "PlayerShooting missing; ";
        }
        
        if (!projectileSpawnerExists)
        {
            canShoot = false;
            blockingIssues += "ProjectileSpawner missing; ";
        }
        
        if (!projectileManagerExists)
        {
            canShoot = false;
            blockingIssues += "ProjectileManager missing; ";
        }
        
        if (!projectilePoolExists)
        {
            canShoot = false;
            blockingIssues += "ProjectilePool missing; ";
        }
        
        // Check shooting mode
        if (!directShootingEnabled && !bypassMusicalTiming)
        {
            canShoot = false;
            blockingIssues += "Neither direct shooting nor bypass musical timing enabled; ";
        }
        
        if (currentTimeScale <= 0)
        {
            canShoot = false;
            blockingIssues += "Time scale is 0; ";
        }
        
        // Final verdict
        if (canShoot)
        {
            Debug.Log("🟢 SHOOTING SYSTEM STATUS: READY TO SHOOT!");
        }
        else
        {
            Debug.LogError($"🔴 SHOOTING SYSTEM STATUS: BLOCKED - {blockingIssues}");
        }
    }
    
    private void LogDetailedStatus()
    {
        Debug.Log("📋 Detailed System Status:");
        Debug.Log($"  Missing Components: {(string.IsNullOrEmpty(missingComponents) ? "None" : missingComponents)}");
        Debug.Log($"  Critical Errors: {(string.IsNullOrEmpty(criticalErrors) ? "None" : criticalErrors)}");
    }
    
    [ContextMenu("Try Manual Shooting Test")]
    public void TryManualShootingTest()
    {
        Debug.Log("🧪 Manual Shooting Test...");
        
        if (crosshairCore == null || playerLocking == null || playerShooting == null)
        {
            Debug.LogError("Cannot test shooting - core components missing!");
            return;
        }
        
        // Simulate shooting conditions
        playerLocking.triggeredLockFire = true;
        Debug.Log("Set triggeredLockFire = true");
        
        // Try to trigger shooting directly
        if (playerShooting != null)
        {
            try
            {
                var launchMethod = typeof(PlayerShooting).GetMethod("LaunchProjectilesWithDelay");
                if (launchMethod != null)
                {
                    StartCoroutine((IEnumerator)launchMethod.Invoke(playerShooting, null));
                    Debug.Log("✅ Manual shooting test triggered!");
                }
                else
                {
                    Debug.LogError("LaunchProjectilesWithDelay method not found!");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Manual shooting test failed: {e.Message}");
            }
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 500, 400));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔫 SHOOTING SYSTEM DIAGNOSTIC", GUI.skin.label);
        
        // Component Status
        GUILayout.Label("Components:");
        GUILayout.Label($"CrosshairCore: {(crosshairCoreExists ? "✅" : "❌")}");
        GUILayout.Label($"PlayerLocking: {(playerLockingExists ? "✅" : "❌")}");
        GUILayout.Label($"PlayerShooting: {(playerShootingExists ? "✅" : "❌")}");
        GUILayout.Label($"ProjectileManager: {(projectileManagerExists ? "✅" : "❌")}");
        GUILayout.Label($"ProjectileSpawner: {(projectileSpawnerExists ? "✅" : "❌")}");
        GUILayout.Label($"ProjectilePool: {(projectilePoolExists ? "✅" : "❌")}");
        
        GUILayout.Space(10);
        
        // Shooting State
        GUILayout.Label("Shooting State:");
        GUILayout.Label($"Direct Mode: {(directShootingEnabled ? "✅" : "❌")}");
        GUILayout.Label($"Bypass Musical: {(bypassMusicalTiming ? "✅" : "❌")}");
        GUILayout.Label($"Triggered Fire: {(triggeredLockFire ? "✅" : "❌")}");
        GUILayout.Label($"Locked Count: {lockedProjectileCount}");
        GUILayout.Label($"Time Scale: {currentTimeScale:F2}");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔍 RUN DIAGNOSTIC"))
        {
            PerformFullDiagnostic();
        }
        
        if (GUILayout.Button("🧪 MANUAL SHOOTING TEST"))
        {
            TryManualShootingTest();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
