# BTR System Architecture Overview

*Last Updated: 2025-07-19*
*Status: Current*

## Project Context

**BTR U6 2025 RG** is a Unity 6 game project using URP (Universal Render Pipeline) with Render Graph features. The project demonstrates sophisticated architectural patterns with strong separation of concerns, event-driven design, and modern Unity 6 optimization techniques including the **Stylo Framework** - a comprehensive collection of specialized systems for time manipulation, audio synchronization, visual effects, and object pooling.

## Core Architecture

### System Hierarchy

```mermaid
flowchart TB
    %% Core Management
    GameManager[GameManager]
    EventSystem[Event System]
    SceneManager[Scene Management]
    ScoreManager[Score Manager]
    
    %% Stylo Framework Core
    StyloFramework[Stylo Framework<br/>Specialized Systems]
    StyloEpoch[Stylo.Epoch<br/>Time Manipulation]
    StyloCadance[Stylo.Cadance<br/>Music Synchronization]
    StyloFlux[Stylo.Flux<br/>Visual Effects]
    StyloReservoir[Stylo.Reservoir<br/>Object Pooling]
    
    %% Enhanced Audio System
    FMODAudio[FMOD Advanced Audio<br/>4-Phase Integration]
    ChronosTime[Chronos Time System<br/>Time Manipulation Core]
    
    %% Core Gameplay Systems
    PlayerSystem[Player System]
    EnemySystem[Enemy System]
    ProjectileSystem[Projectile System]
    UISystem[UI System]
    WaveSystem[Wave System<br/>Level Progression]
    
    %% Interfaces & Core
    IEventCompatible[IEventCompatible]
    IDamageable[IDamageable]
    
    %% Core Management Relationships
    GameManager --> EventSystem
    GameManager --> SceneManager
    GameManager --> ScoreManager
    
    %% Stylo Framework Integration
    GameManager --> StyloFramework
    StyloFramework --> StyloEpoch
    StyloFramework --> StyloCadance
    StyloFramework --> StyloFlux
    StyloFramework --> StyloReservoir
    
    %% Time & Audio Integration
    StyloEpoch --> ChronosTime
    StyloCadance --> FMODAudio
    StyloFlux --> FMODAudio
    FMODAudio --> ChronosTime
    
    %% System Implementation
    IEventCompatible --> |implements| GameManager
    IEventCompatible --> |implements| PlayerSystem
    IEventCompatible --> |implements| EnemySystem
    
    %% Enhanced System Interactions
    GameManager --> PlayerSystem
    GameManager --> EnemySystem
    GameManager --> ProjectileSystem
    GameManager --> UISystem
    GameManager --> WaveSystem
    
    %% Stylo System Integration
    PlayerSystem --> StyloEpoch
    PlayerSystem --> FMODAudio
    EnemySystem --> FMODAudio
    EnemySystem --> StyloFlux
    ProjectileSystem --> StyloReservoir
    UISystem --> StyloFlux
    WaveSystem --> StyloCadance
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef stylo fill:#bbf,stroke:#333,stroke-width:2px
    classDef audio fill:#bfb,stroke:#333,stroke-width:2px
    classDef gameplay fill:#fbb,stroke:#333,stroke-width:2px
    
    class GameManager,EventSystem,SceneManager,ScoreManager core
    class StyloFramework,StyloEpoch,StyloCadance,StyloFlux,StyloReservoir stylo
    class FMODAudio,ChronosTime audio
    class PlayerSystem,EnemySystem,ProjectileSystem,UISystem,WaveSystem gameplay
```

## Stylo Framework

The **Stylo Framework** is a comprehensive collection of specialized systems that enhance core Unity functionality with production-ready features:

### **Stylo.Epoch** - Time Manipulation Framework
- **Perfect time synchronization** across all game systems
- **Dynamic timeline control** with Chronos integration
- **Temporal effect support** for rewind, slow-motion, and pause mechanics
- **Thread-safe time operations** with async/await patterns

### **Stylo.Cadance** - Music Synchronization Framework
- **Beat-accurate timing** with FMOD Studio integration
- **Real-time BPM detection** and tempo tracking
- **Music-reactive gameplay** mechanics and effects
- **Adaptive audio** synchronization during time manipulation

### **Stylo.Flux** - Visual Effects Framework
- **Authentic datamoshing** with Unity 6 Render Graph optimization
- **Real-time compression artifacts** and pixel trailing effects
- **Adaptive quality scaling** with mobile optimization
- **Audio-reactive effects** synchronized with Stylo.Cadance

### **Stylo.Reservoir** - Object Pooling System
- **Intelligent pool management** with performance monitoring
- **Dynamic sizing** based on usage patterns
- **Memory optimization** with automatic cleanup
- **Unity 6 optimization** with Burst compilation support

### **FMOD Advanced Audio Integration**
- **4-phase audio enhancement** with unified management
- **Performance optimization** with LOD and monitoring systems
- **Chronos time synchronization** for perfect audio-time sync
- **Backward compatibility** with migration tools and facades

## Key Design Patterns

### 1. Event-Driven Architecture
- **IEventCompatible** interface for consistent event handling
- Centralized event channels: GameEvents, EnemyEvents, SceneEvents, TimeEvents
- Decoupled system communication
- Async event processing

### 2. Component-Based Design
- Modular behaviors for entities
- Composable functionality
- Clear separation of concerns
- Interface-based contracts

### 3. Advanced Object Pooling (Stylo.Reservoir)
- High-performance projectile management with intelligent sizing
- VFX element pooling with automatic optimization
- UI element reuse with memory pressure management
- Burst-compiled performance optimizations

### 4. Time-Synchronized State Management
- Projectile state machines with Stylo.Epoch integration
- Enemy behavior states synchronized with time manipulation
- Game state coordination with temporal consistency
- Time-based state transitions with perfect accuracy

### 5. Configuration-Driven Design
- ScriptableObject-based configurations
- Data-driven entity setup
- Runtime parameter adjustment
- Designer-friendly workflows with validation systems

## System Breakdown

### Management Layer
- **GameManager**: Central game state coordination with Stylo Framework integration
- **SceneManagerBTR**: Scene loading and transitions
- **ScoreManager**: Scoring and progression

### Stylo Framework Layer
- **Stylo.Epoch**: Time manipulation with Chronos integration for perfect temporal control
- **Stylo.Cadance**: Music synchronization with beat-accurate timing and BPM detection
- **Stylo.Flux**: Visual effects with authentic datamoshing and Unity 6 Render Graph optimization
- **Stylo.Reservoir**: Object pooling with intelligent management and Burst compilation

### Enhanced Audio Layer
- **FMOD Advanced Audio**: 4-phase audio system with LOD, monitoring, and time synchronization
- **Chronos Time System**: Core time manipulation engine integrated across all systems
- **AudioLODSystem**: Distance-based audio quality management
- **AudioPerformanceMonitor**: Real-time audio performance tracking

### Player System
- **PlayerHealth**: Health management and damage handling
- **PlayerMovement**: Physics-based movement with Stylo.Epoch time integration
- **PlayerShooting**: Projectile firing using Stylo.Reservoir pooling
- **PlayerLocking**: Target acquisition and lock-on systems
- **PlayerTimeControl**: Time manipulation abilities with perfect Chronos synchronization

### Enemy System
- **EnemyCore**: Central enemy management component
- **BaseEnemyBehavior**: Behavior system foundation with Stylo.Epoch time awareness
- **Combat Behaviors**: Projectile, explosion, shield implementations with FMOD audio integration
- **Movement Behaviors**: Pathfinding and phased movement synchronized with time manipulation
- **Configuration System**: Data-driven enemy setup with ScriptableObject validation
- **EnemyAudioBehavior**: Enhanced audio with spatial positioning and LOD optimization

### Projectile System
- **ProjectileCore**: Core projectile behavior with time synchronization
- **ProjectileManager**: High-level projectile coordination with Stylo.Reservoir integration
- **Enhanced ProjectilePool**: Stylo.Reservoir-powered intelligent pooling
- **Job System Integration**: Multithreaded calculations with Burst compilation

### Wave System
- **WaveManager**: Level progression and enemy spawning coordination
- **WaveConfiguration**: Data-driven wave setup with ScriptableObject architecture
- **Music Integration**: Stylo.Cadance synchronization for beat-accurate enemy spawning
- **Difficulty Scaling**: Adaptive challenge progression with performance monitoring

### UI System
- **PlayerUI**: Player state visualization with Stylo.Flux effects
- **WaveHUD**: Wave progression and scoring with music synchronization
- **LoadingScreen**: Scene transition management
- **Reach UI Integration**: Third-party UI framework with enhanced audio feedback

## Interface Architecture

### Core Interfaces
- **IEventCompatible**: Event system integration
- **IDamageable**: Unified damage handling
- **IEnemy**: Enemy behavior contracts
- **IProjectile**: Projectile system contracts

### Behavior Interfaces
- **IEnemyBehavior**: Enemy behavior patterns
- **ICombatBehavior**: Combat system contracts
- **IMovementBehavior**: Movement system contracts
- **ICleanupHandler**: Resource cleanup management

## Performance Considerations

### Unity 6 Optimization Strategies
- **Unity Job System**: Multithreaded operations with Burst compilation
- **Render Graph**: Unity 6 optimized rendering pipeline for Stylo.Flux
- **Stylo.Reservoir Pooling**: Intelligent memory-efficient resource management
- **Spatial Partitioning**: Optimized collision detection
- **Event Batching**: Reduced frame overhead with async patterns

### Advanced Memory Management
- **Stylo.Reservoir**: Intelligent pool sizing with memory pressure monitoring
- **FMOD Memory Optimizer**: Automatic audio memory cleanup and optimization
- **Unity 6 Addressables**: Enhanced asset loading and memory management
- **Burst Compiled Systems**: High-performance code compilation across all systems
- **Async Disposal Patterns**: Proper resource cleanup with UniTask integration

### Real-time Performance Monitoring
- **AudioPerformanceMonitor**: Real-time audio system performance tracking
- **Stylo.Flux Adaptive Quality**: Dynamic visual quality scaling based on performance
- **Memory Pressure Detection**: Automatic optimization when memory usage is high
- **Frame Time Analysis**: Continuous performance monitoring with automatic adjustments

## Third-Party Integrations

### Audio System
- **FMOD Studio**: Professional audio engine with 4-phase enhancement
- **Koreographer**: Music synchronization integrated with Stylo.Cadance
- **Chronos**: Time manipulation core for perfect audio-time synchronization

### Visual Effects
- **Feel Framework**: Advanced feedback systems integrated with Stylo.Flux
- **Visual Effect Graph**: Particle systems with Render Graph optimization
- **PrimeTween**: High-performance animation with time synchronization
- **Unity 6 Render Graph**: Modern rendering pipeline for Stylo.Flux datamoshing

### Performance & Development Tools
- **Odin Inspector**: Enhanced editor experience
- **Console Pro**: Advanced debugging
- **UniTask**: Modern async/await patterns throughout the framework
- **Burst Compiler**: High-performance compilation for all systems

## Extension Points

The architecture supports extension through:
1. **New Behavior Implementations**: Enemy and projectile behaviors
2. **Additional Event Channels**: System communication
3. **Custom UI Components**: Interface extensions
4. **VFX Integration**: Visual effect systems
5. **Audio Event Handlers**: Sound system integration
6. **Performance Optimizations**: Platform-specific tuning

## Related Documentation

### Stylo Framework Systems
- **[[Stylo.Epoch - Time Manipulation Framework]]** - Perfect time synchronization and temporal effects
- **[[Stylo.Cadance - Music Synchronization Framework]]** - Beat-accurate timing and music-reactive gameplay
- **[[Stylo.Flux - Visual Effects Framework]]** - Authentic datamoshing and Unity 6 Render Graph optimization
- **[[FMOD Advanced Audio Integration]]** - 4-phase audio system with performance optimization

### Core Systems
- [[Enemy System Architecture]] - Enhanced with Stylo integration
- [[Player System Architecture]] - Time manipulation and pooling integration
- [[Projectile System Architecture]] - Stylo.Reservoir pooling system
- [[UI System Architecture]] - Enhanced with Stylo.Flux effects
- [[Wave System Architecture]] - Music-synchronized level progression

### Specialized Systems
- [[Tree System Architecture]] - Procedural tree generation system
- [[Radar System Architecture]] - High-performance entity tracking and visualization
- [[Performance Monitoring System]] - Real-time performance analysis and optimization
- [[Utility Systems]] - Helper functions and extension methods

### Supporting Systems
- [[Chronos Time Manipulation Integration]] - Core time manipulation system
- [[Unity 6 Render Graph Integration]] - Modern rendering pipeline optimization
- [[Audio LOD and Performance Systems]] - Distance-based audio optimization
- [[Memory Management and Pooling]] - Advanced resource management strategies

## Notes

This architecture has evolved from the original "BTR" project into the current **BTR U6 2025 implementation** with the comprehensive **Stylo Framework**. The system demonstrates **professional-grade patterns** suitable for commercial game development while maintaining flexibility for future expansion.

### Key Architectural Achievements

**Stylo Framework Integration**: The addition of specialized frameworks (Epoch, Cadance, Flux, Reservoir) provides production-ready solutions for time manipulation, music synchronization, visual effects, and object pooling that seamlessly integrate with Unity 6's modern features.

**Unity 6 Optimization**: Full utilization of Unity 6 features including Render Graph, enhanced Burst compilation, and modern rendering pipelines for optimal performance across all platforms.

**Perfect Time Synchronization**: The Chronos integration ensures all systems (audio, visual, gameplay) remain perfectly synchronized during time manipulation effects, providing a consistent and immersive experience.

**Performance-First Design**: Every system includes real-time performance monitoring, adaptive quality scaling, and intelligent optimization strategies that automatically adjust based on platform capabilities and current performance metrics.

All major systems implement **modern async patterns**, **event-driven communication**, and **comprehensive performance optimization strategies**. The **modular design** allows for easy testing, debugging, and extension while maintaining **production-ready reliability**.