# May Overview

Initial thoughts - not a lot of progress in April! Pretty burned out on the game right now

Spent a couple weeks trying to debug WaveSpawner issues. Have a solution for moving between waves, but enemies are not capable of being resued properly. Can possibly delay fixing this for a while. Maybe look into Pool Manager more for these issues

Talk to <PERSON> at Ryerson and did various haptic device testing. Latency in controller too high for hi hats in controller to be effective. more blunt haptics seem to work on, less precise

Narrative Idea: 

My lock on system - Can work overhead / first person / third person 

You are an AI auto protection system guarding a being transmitted through earth

Think AI protection in Alien, Prometheus, etc. 

Some notes 

- Camera follows Shooting Reticle rather than Player now
- April 5 - good tips from Tunic dev on Post-Processing
- Random Flow for Blender - cool asset to try on models!
- Upgraded to newest Unity LTS 2021
- Unlockable bullets - not currently using while testing
- Need to reassess music system integration with game systems
    - Look at original core documents
- Mechanics and core narrative pillars - tying these things together
    - Is movement core or is it a method of getting from one puzzle to the next?
- Cannot get alpha of Quibli Shader Outline to work
    - Look into Flat Kit Outline / alpha docs - supposedly better!
- Good basic tips for performance enhancement
    - [https://www.youtube.com/watch?v=v5_RN8o1b3g](https://www.youtube.com/watch?v=v5_RN8o1b3g)
- Coming back to early inspirations
    - [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
- Possibly interesting system reference for music documentation
    - [https://mixolumia.com/makingmusic/](https://mixolumia.com/makingmusic/)
    - 
    

Did some decent work for over half the month, but due to confusion / lack of inspo / etc I moved to hitting web dev a lot harder.

Need to find a balance moving through May