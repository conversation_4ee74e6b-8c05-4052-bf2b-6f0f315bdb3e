# Core Design

Core Design - June 17

Rhythmic lock on and shooting

Firing bullets / enemies back at targets

lock on holds them in static space in air

Rotation allows you to move them to another view and fire them

Lumines "Erase Blocks at X Ti

- Lumines BUT move object to new area quickly
- position not super important
- dont stack too high

me" Type of stationary mode?

Rubiks Cube but you're in the cube?

Think About Intelligent Qube - Kurushi

Moving objects along a 3d grid - voxel like movement?

Could this be set per object?