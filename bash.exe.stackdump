Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FE8E
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210286019, 0007FFFFAAB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC00  000210068E24 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEE0  00021006A225 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB8B80000 ntdll.dll
7FFEB6B00000 KERNEL32.DLL
7FFEB5CD0000 KERNELBASE.dll
7FFEB6DB0000 USER32.dll
7FFEB66D0000 win32u.dll
7FFEB7E20000 GDI32.dll
000210040000 msys-2.0.dll
7FFEB60C0000 gdi32full.dll
7FFEB64A0000 msvcp_win.dll
7FFEB6350000 ucrtbase.dll
7FFEB6CF0000 advapi32.dll
7FFEB7C80000 msvcrt.dll
7FFEB7340000 sechost.dll
7FFEB7B60000 RPCRT4.dll
7FFEB51E0000 CRYPTBASE.DLL
7FFEB6700000 bcryptPrimitives.dll
7FFEB6BD0000 IMM32.DLL
