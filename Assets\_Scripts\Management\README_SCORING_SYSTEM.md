# BTR Centralized Scoring System

## Overview

The BTR Centralized Scoring System provides a unified approach to handling all scoring mechanics in the game. It consists of three main components that work together to create a flexible, extensible, and maintainable scoring architecture.

## System Components

### 1. KillStreakSystem.cs
**Purpose**: Manages kill streak mechanics, multipliers, and milestone tracking.

**Key Features**:
- Time-based kill streaks with configurable windows
- Progressive multiplier scaling
- Milestone tracking with bonus rewards
- Automatic streak expiration
- Event-driven integration

**Configuration**:
```csharp
[SerializeField] private float streakTimeWindow = 3f;      // Time window for consecutive kills
[SerializeField] private float baseMultiplier = 1.5f;      // Starting multiplier at 2 kills
[SerializeField] private float multiplierIncrement = 0.5f; // Multiplier increase per kill
[SerializeField] private float maxMultiplier = 5f;         // Maximum multiplier cap
```

### 2. ScoreManagerIntegration.cs
**Purpose**: Central hub for all scoring logic, event processing, and system coordination.

**Key Features**:
- Centralized score event processing
- Event queuing for performance optimization
- Multiplier calculation and application
- Integration with kill streak system
- Extensible event types
- Validation and debugging support

**Score Event Types**:
- `EnemyKill` - Enemy defeated (applies kill streak multipliers)
- `PlayerDamage` - Player takes damage (negative scoring)
- `TimeDecay` - Score decay over time
- `BonusPickup` - Bonus items/milestones
- `WaveCompletion` - Wave/section completion
- `Milestone` - Kill streak milestone bonuses

### 3. ScoreManager.cs (Updated)
**Purpose**: Core score storage and management with integration hooks.

**Key Features**:
- Maintains backward compatibility
- Integration toggle for gradual migration
- Core score storage and UI updates
- Game over condition handling
- Statistics tracking

## Architecture Flow

```
Game Event → ScoreManagerIntegration → Apply Logic/Multipliers → ScoreManager → UI Update
     ↓              ↓                        ↓                ↓
Kill Streak → Multiplier Applied → Final Score → Events Fired
```

## Setup Instructions

### 1. Scene Setup

1. **Add Scoring System to Scene**:
   - Create an empty GameObject named "ScoringSystem"
   - Attach the `KillStreakSystem` component
   - Attach the `ScoreManagerIntegration` component
   - Configure the inspector settings: multipliers and references

2. **Configure ScoreManager**:
   - Select existing ScoreManager GameObject
   - Enable "Use Integrated Scoring" in Integration Settings
   - Assign ScoreManagerIntegration reference

### 2. Inspector Configuration

**KillStreakSystem Settings**:
```
[Kill Streak Settings]
- Streak Time Window: 3 seconds
- Base Multiplier: 1.5
- Multiplier Increment: 0.5
- Max Multiplier: 5.0
- Enable Debug Logs: false

[Milestones] (Auto-configured)
- Double Kill (2 kills) - 5 bonus points
- Triple Kill (3 kills) - 10 bonus points
- Multi Kill (4 kills) - 15 bonus points
- Mega Kill (5 kills) - 25 bonus points
```

**ScoreManagerIntegration Settings**:
```
[Integration Settings]
- Enable Debug Logs: false
- Enable Score Validation: true
- Max Events Per Frame: 5

[Score Multipliers]
- Enemy Kill Base Score: 10
- Damage Score Multiplier: 1
- Time Decay Multiplier: 1
- Wave Completion Base Score: 50
- Milestone Base Score: 5

[System References]
- Kill Streak System: (auto-found on same GameObject)
- Game Events: (auto-found via GameEventsManager)
- Score Manager: (accessed as singleton)
```

**ScoreManager Integration**:
```
[Integration Settings]
- Use Integrated Scoring: true
- Score Integration: (drag ScoreManagerIntegration GameObject)
```

## Usage Examples

### Basic Score Events

```csharp
// Enemy killed
ScoreManagerIntegration.Instance.TriggerScoreEvent(
    ScoreEventType.EnemyKill, 
    10f  // base score - multipliers applied automatically
);

// Player damaged
ScoreManagerIntegration.Instance.TriggerScoreEvent(
    ScoreEventType.PlayerDamage, 
    -25f  // negative score
);

// Wave completed
ScoreManagerIntegration.Instance.TriggerScoreEvent(
    ScoreEventType.WaveCompletion, 
    50f  // base wave bonus
);
```

### Custom Score Events

```csharp
// Headshot bonus
ScoreManagerIntegration.Instance.TriggerScoreEvent(
    ScoreEventType.BonusPickup, 
    25f,  // base score
    1.5f, // custom multiplier
    0,    // no streak
    "Headshot Bonus"
);
```

### Event Subscription

```csharp
// Subscribe to score events
ScoreManagerIntegration.OnScoreCalculated += (score) => {
    Debug.Log($"Score calculated: {score}");
};

// Subscribe to specific event processing
ScoreManagerIntegration.OnScoreEventProcessed += (eventData) => {
    if (eventData.eventType == ScoreEventType.EnemyKill)
    {
        ShowScorePopup(eventData.finalScore);
    }
};

// Subscribe to kill streak events
gameEvents.OnStreakMilestone += (streakCount, milestoneName) => {
    ShowMilestoneEffect(milestoneName);
};
```

## Migration Guide

### From Legacy System

The system maintains full backward compatibility. Migration can be done gradually:

**Phase 1**: Add new components to scenes
**Phase 2**: Enable integration in ScoreManager
**Phase 3**: Test and validate behavior
**Phase 4**: Remove legacy code (optional)

### Legacy Mode

To use legacy scoring, set `useIntegratedScoring = false` in ScoreManager. The system will automatically fall back to the original scoring logic.

## Performance Considerations

### Optimizations

- **Event Queuing**: Score events are queued and processed over multiple frames
- **Batch Processing**: Multiple events processed per frame (configurable)
- **Cached References**: System references cached at startup
- **Struct-based Data**: Minimal memory allocations

### Memory Management

- **Static Event Cleanup**: Proper cleanup in OnDestroy
- **Reference Validation**: Automatic validation of system references
- **Pool-Friendly**: Compatible with object pooling systems

## Debugging

### Debug Features

Enable debug logging in components to monitor:
- Score event queuing and processing
- Kill streak changes and multipliers
- Milestone achievements
- System initialization

### Debug Methods

```csharp
// Manual testing (Editor only)
killStreakSystem.DebugTriggerKill();
scoreIntegration.DebugTriggerEnemyKill();

// Get system status
string streakInfo = killStreakSystem.GetDebugInfo();
string integrationInfo = scoreIntegration.GetDebugInfo();
```

### Common Issues

1. **Integration Not Working**:
   - Check `useIntegratedScoring` is enabled in ScoreManager
   - Verify ScoringSystem GameObject exists with both components
   - Check console for initialization errors

2. **Kill Streaks Not Working**:
   - Ensure KillStreakSystem is on the ScoringSystem GameObject
   - Check kill streak time window settings
   - Verify enemy death events are firing

3. **Events Not Firing**:
   - Check GameEventsManager initialization
   - Verify event subscriptions

## Extension Points

### Adding New Score Event Types

1. Add new enum value to `ScoreEventType`
2. Add handling in `ScoreManagerIntegration.ProcessScoreEvent()`
3. Add trigger method if needed
4. Update documentation

### Custom Multiplier Systems

```csharp
// Example: Difficulty-based multipliers
public class DifficultyMultiplier : MonoBehaviour
{
    void Start()
    {
        float difficultyMultiplier = GetDifficultyMultiplier();
        ScoreManagerIntegration.Instance.SetScoreMultiplier(
            ScoreEventType.EnemyKill, 
            difficultyMultiplier
        );
    }
}
```

## Events Reference

### Kill Streak Events

```csharp
// GameEvents.cs
public event Action<int, float> OnEnemyKilledWithStreak; // streak count, multiplier
public event Action<int, float> OnStreakChanged;         // streak count, multiplier
public event Action<int, string> OnStreakMilestone;     // streak count, milestone name
public event Action OnStreakEnded;
```

### Integration Events

```csharp
// ScoreManagerIntegration.cs
public static event Action<float> OnScoreCalculated;
public static event Action<ScoreEventData> OnScoreEventProcessed;
public static event Action<ScoreEventType, float, float> OnScoreEventQueued;
```

## Best Practices

### Code Organization

1. **Single Responsibility**: Each component handles one aspect
2. **Event-Driven**: Use events for loose coupling
3. **Configurable**: Expose settings through inspector
4. **Testable**: Design for easy unit testing

### Performance

1. **Batch Updates**: Process multiple events per frame
2. **Cache References**: Avoid FindObjectOfType in Update()
3. **Minimize Allocations**: Use structs for event data
4. **Profile Regularly**: Monitor performance impact

### Maintainability

1. **Clear Documentation**: Document all public APIs
2. **Consistent Naming**: Follow project conventions
3. **Error Handling**: Graceful degradation on errors
4. **Version Compatibility**: Maintain backward compatibility

---

## Summary

The BTR Centralized Scoring System provides:

✅ **Centralized Logic** - All scoring through one system  
✅ **Kill Streak Integration** - Automatic multiplier application  
✅ **Extensible Events** - Easy to add new scoring types  
✅ **Backward Compatible** - Gradual migration support  
✅ **Performance Optimized** - Event queuing and batching  
✅ **Debug Friendly** - Comprehensive logging and validation  

This system creates a solid foundation for all scoring mechanics while maintaining the flexibility to add new features as the game evolves.
