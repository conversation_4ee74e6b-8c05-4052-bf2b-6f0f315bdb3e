<PERSON>tro

The creator of the content is <PERSON>, a 26-year-old game developer who is still learning how to start videos.

<PERSON> has been watching video game development vlogs, where the initial stages often involve getting the character controller working and running around in the world.

As the project grows, more problems tend to arise, which is a common challenge many game developers face.

A particular YouTuber, <PERSON>, had a difficult time with a big project despite having success with smaller games.

<PERSON> was inspired by <PERSON>'s experience and decided to create content discussing the main points to consider when developing a big game.

Hardware

To create a big game, a good PC is necessary, as it is crucial for handling the game's demands.

A game is optimized when it is built, with numerous systems running in the background to provide maximum performance.

In the Unity editor, most of the systems that run in the background during gameplay are not running, so a PC that can barely run the game at 60fps is too weak to run smoothly in the editor.

Using a weak PC can result in a poor experience when navigating the game world in the editor.

Lighting

Lighting is a significant challenge in Unity, particularly for large games with complex environments featuring landscapes, towns, and caves, as baking lighting becomes impossible due to the large number of meshes involved.

Baked lighting generates textures by UV unwrapping all meshes, surfaces, and triangles in the game, which can lead to memory issues and extremely long baking times.

The process of baking lighting involves emitting light rays that bounce off surfaces and are written to textures, but in big games, these textures do not fit in memory, and baking would take an unfeasible amount of time.

Even if the memory problem were resolved, the baking process would still disrupt the entire workflow, as minor changes, such as moving a small object, would require rebaking, which could take up to 48 hours.

Additionally, the textures generated by baked lighting would significantly increase the build size of the game, adding many gigabytes.

Pro Builder

Pro Builder is a useful tool for prototyping and quickly adding meshes to a scene, especially for small scenes where it's feasible to keep the meshes and add textures to them.

However, the code of Pro Builder reveals that the generated meshes are not suitable for large scenes, as they regenerate frequently, such as on undo, redo, closing and opening the editor, entering play mode, and loading or unloading scenes.

This regeneration process involves throwing away the existing mesh and recreating all vertices, triangles, normals, and UVs, which can cause significant performance issues if there are hundreds of such objects in the scene.

Similar performance issues can also occur with other assets and tools that generate meshes, such as Text Mesh and Line Renderer.

To avoid these performance issues, it's recommended to either export Pro Builder measurements as assets, allowing them to act like regular meshes, or avoid using Pro Builder altogether and opt for modular meshes instead.

Editor Performance

When working on big games, it is essential to care about editor performance and handle it on your own, as the basics for in-game and editor performance are similar, and not displaying everything at once is a key approach.

Using multiple scenes is the best approach for collaboration, keeping things sorted, clean, and structured, but it comes with the major downside of not being able to reference anything from one scene in another scene.

Not being able to reference between scenes is also a good thing, as it helps avoid dependencies, which should be avoided whenever possible.

When dependencies cannot be avoided, using the cross-scene reference Unity build is an option, as seen in the big shooter game example.

Profiling can be done in the editor to identify what makes things slow, and it is recommended to do so when the editor starts to feel slower.

Attributes like InitializeOnLoadMethod and InitializeOnLoad should be avoided as much as possible, as they can cause code to execute whenever a script is saved or recompiled.

Methods like FindAssetOfType and similar methods that search through the entire asset database should be avoided in big games, as they can cause performance issues.

Code that searches through all open scenes should also be avoided, and warnings should be made for buttons that perform such actions.

Runtime Performance

Runtime performance in a game is about seeing the game from the player's perspective, focusing on what is relevant to them at a given time, rather than trying to handle the entire game world at once.

A player only cares about what is happening in their immediate surroundings, not about distant events or objects, which can be ignored or simplified to improve performance.

By zooming in on the player's view, it is possible to create a scene with ease that runs at over 100 frames per second, even in a large open-world game.

The number of game objects that need to be loaded can be kept small, similar to a linear game, by only loading objects that are currently around the player.

To achieve good runtime performance, the game world should be broken up into small chunks, and only the active content should be kept at a size similar to a small level.

This approach makes it easier to create a game that runs at 60 frames per second, even in a large open-world game with many objects and complex scenes.

Planning for Performance

When developing a huge game, it's essential to consider performance right after the prototype phase, rather than waiting until problems arise, to make the development process easier.

Embedding performance considerations into the game design can help simplify the process, such as using level design elements to reduce rendering requirements.

Using walls to surround a city can help reduce the need to render distant areas, making the game more efficient.

Adding mountains or hills to a level can help hide distant objects, reducing the need for complex rendering.

Incorporating vertical offsets in level design can also help hide objects and improve performance.

In linear games, using fog gates or similar techniques can help hide loading content and improve performance.

Having a plan for level of detail (LOD) implementation is crucial, as it will likely be needed for most game objects to maintain performance.

No Automatic Systems

Automatic systems such as occlusion culling should be avoided in big game development in Unity, as they can consume a lot of CPU time and may not improve performance, especially when rotating the camera.

Static batching is also not recommended if the game handles which content is rendered by itself, as it can break the system and slow down the editor.

Level of Detail (LOD) systems can be useful, but they have limitations, such as only being able to switch meshes, and may not be able to handle more complex scenarios like switching entire areas.

Unity's built-in LOD system may not be sufficient for all use cases, and may require custom solutions or third-party assets to achieve the desired results.

The team behind the game Firewatch used a sector-based system, which they discussed in a GDC talk, and achieved good results.

It is recommended to create a custom solution tailored to the specific needs of the game, rather than relying on pre-made assets or automatic systems.

Finding Close Objects

Optimizing game performance involves considering both GPU and CPU performance, with the latter being crucial in huge games with a large number of objects and systems.

In large games, each city or area can be populated with numerous NPCs, stores, triggers, item pickups, and other objects, which can negatively impact performance if not managed properly.

Iterating through all objects in the game world each frame to check for interactions, such as adding items to the player's inventory, can be detrimental to performance.

To improve performance, it's possible to iterate over objects only in loaded areas, rather than the entire game world.

Using data structures like grids or octrees can help avoid searching through every object in the map, reducing the performance impact of large game worlds.

Physics

There is a limited number of active rigid bodies that can be used in a game, so it's essential to deactivate them whenever possible to avoid performance issues.

Deactivating physics for objects that are not in the player's view can help improve performance, but this depends on how the content loading system works.

Non-moving colliders also contribute to the physics calculation, so it's recommended to use primitive colliders where possible and deactivate colliders that are not needed.

Special cases, such as when the player is dragging a physical object between sectors, need to be solved in the content loading system to prevent objects from disappearing or behaving unexpectedly.

One solution to this problem is to use a system where the object is part of a null sector when the player picks it up, so it won't unload, and when the player drops the object, it gets assigned to the sector it collides with.

If the object falls through the map, it can be reset to its initial position to prevent it from getting lost.

Non Player Characters (NPCs)

NPCs are a complex topic, especially in large games, where having all enemies active at all times is not feasible, unlike in smaller games like Portal.

A common technique to manage NPCs in big games is to spawn people and vehicles around corners or behind the player and despawn them at a certain distance, creating the illusion of an infinite population.

NPCs do not need to decide what to do on every single frame, and their actions can be queued up, with only a few NPCs calculated per frame, such as 5 NPCs per frame for navigation.

When using Navmesh for navigation, it is recommended to split the navigation into regions using the Navmesh components from Unity, rather than the built-in navigation tab.

The Navmesh component allows for the creation of a Navmesh surface, which is a component on a game object in the scene, and can be set to collect objects by volume to create a regional Navmesh.

Using the Navmesh component eliminates the need for the static checkbox and allows for more efficient navigation, as changes to one part of the game do not require reloading and rebaking the Navmesh for the entire game world.

The Navmesh surface code is open source, allowing for modifications, such as ignoring trigger colliders, and Navmesh surfaces can be connected using Navmesh links.

Memory Management

Memory management is an important topic in huge games, particularly when dealing with large amounts of data and assets.

Reusing assets over and over can help minimize the need to unload assets, reducing memory management complexity.

Scenes in a game can potentially be unloaded, but if they are relatively small in size, such as 500 megabytes uncompressed in the editor, it may not be necessary.

Unity provides a package called Addressables for memory management, which can be used to optimize memory usage.

There are tutorials available that provide guidance on how to use Addressables for memory management.

The Memory Profiler is a tool that allows developers to see what is filling up their memory, helping to identify areas for optimization.

Reuse Assets

Reusing assets as much as possible is beneficial, especially with instance rendering, as it saves performance and reduces data memory usage.

A slower-growing asset database is also beneficial for editor performance, and it results in a smaller build size, leading to faster download times and more disk space for end-users and play testers.

Reusing assets reduces the need to constantly create new meshes, resulting in numerous benefits.

The material property override feature allows for altering specific material fields per object without creating a new material asset, and it does not break GPU instancing.

Coding

When coding a big game, it is essential to have encapsulated systems and a unified communication system that connects them, likely in the form of an event system.

Encapsulated systems and a unified communication system are crucial for managing complexity in game development.

A detailed explanation on writing code for a complex game can be provided if requested, as the topic is too extensive to cover briefly.

Profiling

Profiling is essential whenever frame drops are noticed, and it can be done in the editor to identify the cause of the issue.

Profiling does not work when the game is GPU-bound, and there is no tool that can identify which meshes or shaders are causing performance problems.

To detect performance issues caused by meshes or shaders, it is necessary to manually turn off game objects while the game is running and observe the frame rate.

In most cases, the terrain or particle systems are the cause of performance problems, with Unity's terrain being a common culprit due to its detail rendering features.

Unity's terrain can cause massive performance problems, particularly with features like trees, rocks, or grass, and it is recommended to avoid using this feature entirely.

A better approach is to use prefabs with level of detail (LODs) instead of Unity's terrain, which provides more artistic freedom and prevents performance problems.

Keep Things Simple

Staying on the safe side is crucial when working on a big game in Unity, as inspired by Robert Thompson's devlog experience.

Robert Thompson tried to create a shader to place windows in his buildings, but it did not work out, highlighting the potential issues with complex solutions.

Complex solutions might work initially but can lead to more problems down the road, emphasizing the importance of keeping things simple.

Summary

To work efficiently on a big game in Unity, it is essential to have the right hardware.

When using lighting, it is recommended to use real-time lighting only as baked lighting won't work.

Avoid using Pro Builder and other mesh generating components in your scenes.

Take care of the editor performance by splitting your game into chunks that can be easily displayed.

Profile the editor to identify performance issues and see the world from the perspective of your player.

Plan your world with performance in mind, and forget automatic systems like occlusion culling or static batching.

Use areas or a grid to find nearby objects, and limit the number of active rigid bodies.

Only calculate relevant NPCs and spread calculations over multiple frames.

Spawn and despawn NPCs around the player to optimize performance.

Use Nav Mesh components to create regional Nav Meshes.

Utilize Addressables for memory management, and reuse your assets to reduce memory usage.

Write encapsulated code, and use a unified communication system to simplify development.

Profile often to identify performance issues, and deactivate objects for pseudo GPU profiling.

Keep things simple to avoid unnecessary complexity and optimize performance.