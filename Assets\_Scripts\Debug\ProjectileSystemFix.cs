using UnityEngine;
using BTR;
using BTR.Projectiles;
using System.Collections;

/// <summary>
/// One-click fix for the projectile system initialization issues.
/// Creates all missing components and ensures proper initialization order.
/// </summary>
public class ProjectileSystemFix : MonoBehaviour
{
    [Header("Fix Settings")]
    [SerializeField] private bool autoFixOnStart = true;
    [SerializeField] private bool enableDetailedLogging = true;
    
    [Header("Status")]
    [SerializeField] private bool systemFixed = false;
    [SerializeField] private string lastFixResult = "";
    
    private void Start()
    {
        if (autoFixOnStart)
        {
            StartCoroutine(FixProjectileSystemDelayed());
        }
    }
    
    private IEnumerator FixProjectileSystemDelayed()
    {
        // Wait a frame to ensure all Awake methods have run
        yield return null;
        
        FixProjectileSystem();
    }
    
    [ContextMenu("Fix Projectile System")]
    public void FixProjectileSystem()
    {
        Debug.Log("🔧 === FIXING PROJECTILE SYSTEM ===");
        
        lastFixResult = "";
        systemFixed = false;
        
        try
        {
            // Step 1: Create ProjectileManager if missing
            if (ProjectileManager.Instance == null)
            {
                CreateProjectileManager();
            }
            
            // Step 2: Create ProjectilePool if missing
            if (ProjectilePool.Instance == null)
            {
                CreateProjectilePool();
            }
            
            // Step 3: Create ProjectileEffectManager if missing
            if (ProjectileEffectManager.Instance == null)
            {
                CreateProjectileEffectManager();
            }
            
            // Step 4: Create ProjectileSpawner if missing
            if (ProjectileSpawner.Instance == null)
            {
                CreateProjectileSpawner();
            }
            
            // Step 5: Ensure all components are on the same GameObject for proper initialization
            EnsureComponentsOnSameGameObject();
            
            // Step 6: Force re-initialization
            StartCoroutine(ForceReinitialization());
            
            systemFixed = true;
            lastFixResult = "✅ Projectile system fixed successfully!";
            Debug.Log(lastFixResult);
        }
        catch (System.Exception e)
        {
            lastFixResult = $"❌ Fix failed: {e.Message}";
            Debug.LogError(lastFixResult);
        }
    }
    
    private void CreateProjectileManager()
    {
        Debug.Log("🛠️ Creating ProjectileManager...");
        
        GameObject managerGO = new GameObject("ProjectileManager");
        managerGO.AddComponent<ProjectileManager>();
        
        Debug.Log("✅ ProjectileManager created");
    }
    
    private void CreateProjectilePool()
    {
        Debug.Log("🛠️ Creating ProjectilePool...");
        
        // Find ProjectileManager GameObject or create one
        GameObject managerGO = GameObject.Find("ProjectileManager");
        if (managerGO == null)
        {
            managerGO = new GameObject("ProjectileManager");
        }
        
        // Add ProjectilePool to the same GameObject
        if (managerGO.GetComponent<ProjectilePool>() == null)
        {
            managerGO.AddComponent<ProjectilePool>();
        }
        
        Debug.Log("✅ ProjectilePool created");
    }
    
    private void CreateProjectileEffectManager()
    {
        Debug.Log("🛠️ Creating ProjectileEffectManager...");
        
        // Find ProjectileManager GameObject or create one
        GameObject managerGO = GameObject.Find("ProjectileManager");
        if (managerGO == null)
        {
            managerGO = new GameObject("ProjectileManager");
        }
        
        // Add ProjectileEffectManager to the same GameObject
        if (managerGO.GetComponent<ProjectileEffectManager>() == null)
        {
            managerGO.AddComponent<ProjectileEffectManager>();
        }
        
        Debug.Log("✅ ProjectileEffectManager created");
    }
    
    private void CreateProjectileSpawner()
    {
        Debug.Log("🛠️ Creating ProjectileSpawner...");
        
        // Find ProjectileManager GameObject or create one
        GameObject managerGO = GameObject.Find("ProjectileManager");
        if (managerGO == null)
        {
            managerGO = new GameObject("ProjectileManager");
        }
        
        // Add ProjectileSpawner to the same GameObject
        if (managerGO.GetComponent<ProjectileSpawner>() == null)
        {
            managerGO.AddComponent<ProjectileSpawner>();
        }
        
        Debug.Log("✅ ProjectileSpawner created");
    }
    
    private void EnsureComponentsOnSameGameObject()
    {
        Debug.Log("🔗 Ensuring all projectile components are on the same GameObject...");
        
        // Find or create the main ProjectileManager GameObject
        GameObject managerGO = GameObject.Find("ProjectileManager");
        if (managerGO == null)
        {
            managerGO = new GameObject("ProjectileManager");
        }
        
        // Ensure all components are on this GameObject
        if (managerGO.GetComponent<ProjectileManager>() == null)
            managerGO.AddComponent<ProjectileManager>();
        
        if (managerGO.GetComponent<ProjectilePool>() == null)
            managerGO.AddComponent<ProjectilePool>();
        
        if (managerGO.GetComponent<ProjectileEffectManager>() == null)
            managerGO.AddComponent<ProjectileEffectManager>();
        
        if (managerGO.GetComponent<ProjectileSpawner>() == null)
            managerGO.AddComponent<ProjectileSpawner>();
        
        Debug.Log("✅ All components ensured on same GameObject");
    }
    
    private IEnumerator ForceReinitialization()
    {
        Debug.Log("🔄 Forcing re-initialization...");
        
        // Wait a few frames for all components to initialize
        yield return new WaitForSeconds(0.5f);
        
        // Check if ProjectileSpawner is fully initialized
        if (ProjectileSpawner.Instance != null)
        {
            bool isInitialized = ProjectileSpawner.Instance.IsFullyInitialized;
            Debug.Log($"ProjectileSpawner IsFullyInitialized: {isInitialized}");
            
            if (!isInitialized)
            {
                Debug.LogWarning("ProjectileSpawner not fully initialized - may need manual restart");
            }
        }
        
        // Verify all instances are available
        Debug.Log($"ProjectileManager.Instance: {(ProjectileManager.Instance != null ? "✅" : "❌")}");
        Debug.Log($"ProjectileSpawner.Instance: {(ProjectileSpawner.Instance != null ? "✅" : "❌")}");
        Debug.Log($"ProjectilePool.Instance: {(ProjectilePool.Instance != null ? "✅" : "❌")}");
        Debug.Log($"ProjectileEffectManager.Instance: {(ProjectileEffectManager.Instance != null ? "✅" : "❌")}");
        
        Debug.Log("🔄 Re-initialization complete");
    }
    
    [ContextMenu("Enable Direct Shooting Mode")]
    public void EnableDirectShootingMode()
    {
        Debug.Log("🎯 Enabling Direct Shooting Mode...");
        
        var crosshairCore = FindFirstObjectByType<CrosshairCore>();
        if (crosshairCore != null)
        {
            // Use reflection to set the private fields
            var directModeField = typeof(CrosshairCore).GetField("enableDirectShootingMode", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (directModeField != null)
            {
                directModeField.SetValue(crosshairCore, true);
                Debug.Log("✅ enableDirectShootingMode = true");
            }
            
            var bypassField = typeof(CrosshairCore).GetField("bypassMusicalTiming", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (bypassField != null)
            {
                bypassField.SetValue(crosshairCore, true);
                Debug.Log("✅ bypassMusicalTiming = true");
            }
            
            Debug.Log("🎯 Direct shooting mode enabled!");
        }
        else
        {
            Debug.LogError("❌ CrosshairCore not found!");
        }
    }
    
    [ContextMenu("Test Shooting System")]
    public void TestShootingSystem()
    {
        Debug.Log("🧪 Testing Shooting System...");
        
        var crosshairCore = FindFirstObjectByType<CrosshairCore>();
        var playerLocking = FindFirstObjectByType<PlayerLocking>();
        
        if (crosshairCore == null || playerLocking == null)
        {
            Debug.LogError("❌ Required components not found for testing!");
            return;
        }
        
        // Set up test conditions
        playerLocking.triggeredLockFire = true;
        Debug.Log("✅ Set triggeredLockFire = true");
        
        // Try to call OnMusicalShoot directly
        try
        {
            var onMusicalShootMethod = typeof(CrosshairCore).GetMethod("OnMusicalShoot", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (onMusicalShootMethod != null)
            {
                // Create a dummy CadanceEvent (or use null if method handles it)
                onMusicalShootMethod.Invoke(crosshairCore, new object[] { null });
                Debug.Log("✅ OnMusicalShoot called directly");
            }
            else
            {
                Debug.LogError("❌ OnMusicalShoot method not found!");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Test failed: {e.Message}");
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 320, Screen.height - 250, 310, 240));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔧 PROJECTILE SYSTEM FIX", GUI.skin.label);
        
        GUILayout.Label($"Status: {(systemFixed ? "✅ FIXED" : "❌ NEEDS FIX")}");
        
        if (!string.IsNullOrEmpty(lastFixResult))
        {
            GUILayout.Label($"Result: {lastFixResult}");
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔧 FIX PROJECTILE SYSTEM"))
        {
            FixProjectileSystem();
        }
        
        if (GUILayout.Button("🎯 ENABLE DIRECT SHOOTING"))
        {
            EnableDirectShootingMode();
        }
        
        if (GUILayout.Button("🧪 TEST SHOOTING SYSTEM"))
        {
            TestShootingSystem();
        }
        
        GUILayout.Space(10);
        
        GUILayout.Label("Settings:");
        autoFixOnStart = GUILayout.Toggle(autoFixOnStart, "Auto Fix On Start");
        enableDetailedLogging = GUILayout.Toggle(enableDetailedLogging, "Detailed Logging");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
