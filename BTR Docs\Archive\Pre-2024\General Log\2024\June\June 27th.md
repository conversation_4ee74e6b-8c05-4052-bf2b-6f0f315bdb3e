# June 27th

Some optimization and refinement of enemy bullets. Struggling with interesting movement here, so I’ve pulled it back to more direct hits. Need to refine this. Get dissolve or disappearing effect working better. 

THinknig about reticle, have it adjust size / shape with upgrades in number of lock ons? How can I pull back on things to allow for greater weight of what you lock on to? 

Difficult to choose the bullets fired at you / easy to choose the ones that are shot up from environment. Is there a solution to this?

Testing

- Running through scenes, making notes of whats currently wrong

Notes

- force player to face forward on each song section change
- radar should be adjusted per level / scale is off

Ouroboros Section 3

- Mobius Multi Var 1 Needs an A* scan - FIXED
- Mobius Tube 5 needs model flipped and fixed
    - then new A* scan
- Is there anything interesting to be done on an interior section? Can use this model if so?

How do i Feel about crazy glitch looking infinite snake?

Ourobors Section 5

- does not load in properly, stuck on start sound - FIXED

If i use n to change sections then there are some game objects that need to be reactivted, Scene Switch cleanup is supposed to do this, but its not working properly.