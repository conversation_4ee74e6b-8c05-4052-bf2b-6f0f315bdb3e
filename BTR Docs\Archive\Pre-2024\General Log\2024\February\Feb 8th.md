# Feb 8th

Turnign the player on and off fixes issues with the reticle/shooter movement

Its one of the scripts on the player object - maybe?

Investigate this!

Implemented Temporary basic fix

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled.png)

Also projectile not shooting from reticle - its in front but not moving on button release

Investigate this!

important

locked = determines whether a bullet will damage enemy when it hits it

must be true when shot

cleaning up the console a bit so i can use debugs easier

Found a bullet stuck to reticle, noticed this is happening

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%201.png)

Should be disabled, need to reassess how this is disabled so nothing gets stuck 

The eyeballs seem to stop shooting, need to find out why…. its the same reason! many of the bullets are stuck at this 

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%202.png)

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%203.png)

A bunch of projectiles are staying locked, therefore lifetime is not decreasing and they’re not being pooled. they are not parented to reticle. Need to follow through the whole cycle of them to understand whats going wrong. 

They are static and regular enemy projectiles, so i think that part of it doesnt make a difference. 

I think this issue also explain why when i lock on some projectiles seem to disappear completey

I think they are going to this spot

May be able to go back to DOMove in PlayerShotState - need to reassess - the async nature of it may be the problem, but maybe not, may be offset?

Set the target to null - now projectiles remaining frozen in environment. I think this is a a sign of what’s happening but im not sure exactly what yet

chjanged things so launchback launches projectiles now, but its not holding on to them, so that needs to be addressed