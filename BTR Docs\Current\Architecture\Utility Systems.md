# BTR Utility Systems

*Created: 2025-07-18*
*Status: Current*
*System Location: `Assets/_Scripts/Utilities/`*

## Overview

The BTR Utility Systems provide **essential helper functions, extension methods, and common operations** used throughout the project. These utilities enhance code reusability, maintainability, and development efficiency by providing standardized solutions for common programming patterns and Unity-specific operations.

## System Architecture

```mermaid
flowchart TB
    %% Core Utility Categories
    EXT[Extension Methods]
    HELP[Helper Classes]
    MATH[Math Utilities]
    UNITY[Unity Utilities]
    
    %% Specific Utilities
    VE[Vector Extensions]
    TE[Transform Extensions]
    CE[Component Extensions]
    COL[Collection Extensions]
    
    %% Helper Systems
    OBJ[Object Utilities]
    TIME[Time Utilities]
    DEBUG[Debug Utilities]
    FILE[File Utilities]
    
    %% Math Operations
    GEOM[Geometry Utils]
    INTERP[Interpolation Utils]
    RAND[Random Utils]
    CURVE[Curve Utils]
    
    %% Unity Specific
    COMP[Component Utils]
    SCENE[Scene Utils]
    ASSET[Asset Utils]
    GIZMO[Gizmo Utils]
    
    %% Relationships
    EXT --> VE
    EXT --> TE
    EXT --> CE
    EXT --> COL
    
    HELP --> OBJ
    HELP --> TIME
    HELP --> DEBUG
    HELP --> FILE
    
    MATH --> GEOM
    MATH --> INTERP
    MATH --> RAND
    MATH --> CURVE
    
    UNITY --> COMP
    UNITY --> SCENE
    UNITY --> ASSET
    UNITY --> GIZMO
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef extensions fill:#bbf,stroke:#333,stroke-width:2px
    classDef helpers fill:#bfb,stroke:#333,stroke-width:2px
    classDef math fill:#fbb,stroke:#333,stroke-width:2px
    classDef unity fill:#ffb,stroke:#333,stroke-width:2px
    
    class EXT,HELP,MATH,UNITY core
    class VE,TE,CE,COL extensions
    class OBJ,TIME,DEBUG,FILE helpers
    class GEOM,INTERP,RAND,CURVE math
    class COMP,SCENE,ASSET,GIZMO unity
```

## Extension Methods

### **Vector Extensions**
- **Location**: `Assets/_Scripts/Utilities/VectorExtensions.cs`
- **Purpose**: Enhanced Vector3 and Vector2 operations

**Key Features**:
```csharp
// Distance and direction utilities
public static float DistanceTo(this Vector3 from, Vector3 to)
public static Vector3 DirectionTo(this Vector3 from, Vector3 to)
public static Vector3 DirectionToNormalized(this Vector3 from, Vector3 to)

// Angle calculations
public static float AngleTo(this Vector3 from, Vector3 to)
public static float SignedAngleTo(this Vector3 from, Vector3 to, Vector3 axis)

// Clamping and constraints
public static Vector3 ClampMagnitude(this Vector3 vector, float minMagnitude, float maxMagnitude)
public static Vector3 ClampToPlane(this Vector3 vector, Vector3 planeNormal)

// Utility operations
public static Vector3 WithX(this Vector3 vector, float x)
public static Vector3 WithY(this Vector3 vector, float y)
public static Vector3 WithZ(this Vector3 vector, float z)
public static Vector3 Abs(this Vector3 vector)
```

### **Transform Extensions**
- **Location**: `Assets/_Scripts/Utilities/TransformExtensions.cs`
- **Purpose**: Enhanced Transform operations and hierarchy management

**Key Features**:
```csharp
// Hierarchy operations
public static void DestroyAllChildren(this Transform transform)
public static void DestroyAllChildrenImmediate(this Transform transform)
public static List<Transform> GetAllChildren(this Transform transform)
public static Transform FindChildRecursive(this Transform transform, string name)

// Position and rotation utilities
public static void SetPositionAndRotation(this Transform transform, Vector3 position, Quaternion rotation)
public static void LookAt2D(this Transform transform, Vector3 target)
public static void SetGlobalScale(this Transform transform, Vector3 globalScale)

// Utility operations
public static void Reset(this Transform transform)
public static void CopyTransform(this Transform transform, Transform source)
public static Bounds GetBounds(this Transform transform)
```

### **Component Extensions**
- **Location**: `Assets/_Scripts/Utilities/ComponentExtensions.cs`
- **Purpose**: Enhanced Component operations and validation

**Key Features**:
```csharp
// Component validation
public static bool HasComponent<T>(this GameObject gameObject) where T : Component
public static bool HasComponent<T>(this Component component) where T : Component
public static T GetComponentInChildrenExcludeParent<T>(this Component component) where T : Component

// Component operations
public static T GetOrAddComponent<T>(this GameObject gameObject) where T : Component
public static T GetOrAddComponent<T>(this Component component) where T : Component
public static void RemoveComponent<T>(this GameObject gameObject) where T : Component

// Utility operations
public static void SetActive(this Component component, bool active)
public static void SetLayer(this Component component, int layer, bool includeChildren = false)
```

### **Collection Extensions**
- **Location**: `Assets/_Scripts/Utilities/CollectionExtensions.cs`
- **Purpose**: Enhanced collection operations and LINQ-like functionality

**Key Features**:
```csharp
// Collection utilities
public static T GetRandomElement<T>(this IList<T> list)
public static T GetRandomElement<T>(this T[] array)
public static void Shuffle<T>(this IList<T> list)
public static void Shuffle<T>(this T[] array)

// Null-safe operations
public static bool IsNullOrEmpty<T>(this ICollection<T> collection)
public static bool IsNotNullOrEmpty<T>(this ICollection<T> collection)

// Batch operations
public static void ForEach<T>(this IEnumerable<T> enumerable, Action<T> action)
public static void ForEach<T>(this IEnumerable<T> enumerable, Action<T, int> action)
public static IEnumerable<T> WhereNotNull<T>(this IEnumerable<T> enumerable) where T : class
```

## Helper Classes

### **Object Utilities**
- **Location**: `Assets/_Scripts/Utilities/ObjectUtilities.cs`
- **Purpose**: Object lifecycle and validation utilities

**Key Features**:
- Null-safe object operations
- Object pooling utilities
- Lifecycle management helpers
- Validation and error checking

### **Time Utilities**
- **Location**: `Assets/_Scripts/Utilities/TimeUtilities.cs`
- **Purpose**: Time manipulation and formatting utilities

**Key Features**:
- Time format conversions
- Delta time calculations
- Timer implementations
- Time-based interpolation

### **Debug Utilities**
- **Location**: `Assets/_Scripts/Utilities/DebugUtilities.cs`
- **Purpose**: Enhanced debugging and logging utilities

**Key Features**:
- Conditional logging
- Debug drawing utilities
- Performance timing
- Error reporting helpers

### **File Utilities**
- **Location**: `Assets/_Scripts/Utilities/FileUtilities.cs`
- **Purpose**: File system operations and path management

**Key Features**:
- Path validation and sanitization
- File existence checking
- Directory operations
- Asset path utilities

## Math Utilities

### **Geometry Utils**
- **Location**: `Assets/_Scripts/Utilities/GeometryUtils.cs`
- **Purpose**: Geometric calculations and spatial operations

**Key Features**:
```csharp
// Distance calculations
public static float PointToLineDistance(Vector3 point, Vector3 lineStart, Vector3 lineEnd)
public static float PointToPlaneDistance(Vector3 point, Vector3 planeNormal, Vector3 planePoint)

// Intersection tests
public static bool LineIntersectsPlane(Vector3 lineStart, Vector3 lineEnd, Vector3 planeNormal, Vector3 planePoint)
public static bool SphereIntersectsBox(Vector3 sphereCenter, float sphereRadius, Bounds box)

// Geometric operations
public static Vector3 ProjectPointOnLine(Vector3 point, Vector3 lineStart, Vector3 lineEnd)
public static Vector3 ProjectPointOnPlane(Vector3 point, Vector3 planeNormal, Vector3 planePoint)
public static Vector3 GetClosestPointOnBounds(Vector3 point, Bounds bounds)
```

### **Interpolation Utils**
- **Location**: `Assets/_Scripts/Utilities/InterpolationUtils.cs`
- **Purpose**: Advanced interpolation and easing functions

**Key Features**:
```csharp
// Easing functions
public static float EaseInQuad(float t)
public static float EaseOutQuad(float t)
public static float EaseInOutQuad(float t)
public static float EaseInCubic(float t)
public static float EaseOutCubic(float t)

// Interpolation utilities
public static float SmootherStep(float t)
public static float Hermite(float t)
public static Vector3 BezierCurve(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
public static Vector3 CatmullRom(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
```

### **Random Utils**
- **Location**: `Assets/_Scripts/Utilities/RandomUtils.cs`
- **Purpose**: Enhanced random number generation and utilities

**Key Features**:
```csharp
// Random operations
public static float RandomRange(float min, float max)
public static int RandomRange(int min, int max)
public static bool RandomBool(float probability = 0.5f)
public static Vector3 RandomVector3(float min, float max)
public static Vector3 RandomVector3InSphere(float radius)
public static Vector3 RandomVector3OnSphere(float radius)
public static Color RandomColor()
public static Color RandomColor(float alpha)
```

### **Curve Utils**
- **Location**: `Assets/_Scripts/Utilities/CurveUtils.cs`
- **Purpose**: Animation curve utilities and spline operations

**Key Features**:
- Curve evaluation utilities
- Spline interpolation
- Curve modification helpers
- Animation curve presets

## Unity Utilities

### **Component Utils**
- **Location**: `Assets/_Scripts/Utilities/ComponentUtils.cs`
- **Purpose**: Unity component operations and management

**Key Features**:
- Component caching systems
- Component validation
- Component lifecycle management
- Component serialization utilities

### **Scene Utils**
- **Location**: `Assets/_Scripts/Utilities/SceneUtils.cs`
- **Purpose**: Scene management and navigation utilities

**Key Features**:
- Scene loading utilities
- Scene validation
- Object finding across scenes
- Scene state management

### **Asset Utils**
- **Location**: `Assets/_Scripts/Utilities/AssetUtils.cs`
- **Purpose**: Asset loading and management utilities

**Key Features**:
- Asset loading utilities
- Resource management
- Asset validation
- Asset path utilities

### **Gizmo Utils**
- **Location**: `Assets/_Scripts/Utilities/GizmoUtils.cs`
- **Purpose**: Enhanced Gizmo drawing utilities

**Key Features**:
- Advanced Gizmo shapes
- Colored Gizmo utilities
- Conditional Gizmo drawing
- Performance-optimized Gizmos

## Usage Examples

### **Vector Extensions**
```csharp
// Enhanced vector operations
Vector3 playerPos = player.transform.position;
Vector3 enemyPos = enemy.transform.position;

float distance = playerPos.DistanceTo(enemyPos);
Vector3 direction = playerPos.DirectionToNormalized(enemyPos);
float angle = playerPos.AngleTo(enemyPos);

// Utility operations
Vector3 newPos = playerPos.WithY(0f); // Set Y to 0
Vector3 absPos = playerPos.Abs(); // Absolute values
```

### **Transform Extensions**
```csharp
// Hierarchy management
transform.DestroyAllChildren();
List<Transform> children = transform.GetAllChildren();
Transform childFound = transform.FindChildRecursive("SpecificChild");

// Position and rotation utilities
transform.LookAt2D(target.position);
transform.SetGlobalScale(Vector3.one * 2f);
transform.Reset();
```

### **Component Extensions**
```csharp
// Component validation and operations
if (gameObject.HasComponent<Rigidbody>())
{
    Rigidbody rb = gameObject.GetOrAddComponent<Rigidbody>();
    rb.isKinematic = true;
}

// Utility operations
component.SetActive(false);
component.SetLayer(LayerMask.NameToLayer("UI"), true);
```

### **Collection Extensions**
```csharp
// Collection utilities
List<Enemy> enemies = enemyManager.GetAllEnemies();
Enemy randomEnemy = enemies.GetRandomElement();
enemies.Shuffle();

// Batch operations
enemies.ForEach(enemy => enemy.TakeDamage(damage));
var activeEnemies = enemies.WhereNotNull().Where(e => e.IsActive);
```

## Performance Considerations

### **Extension Methods**
- Use `[MethodImpl(MethodImplOptions.AggressiveInlining)]` for simple operations
- Avoid allocations in frequently called extensions
- Consider caching results for expensive operations
- Use nullable reference types for null safety

### **Helper Classes**
- Implement object pooling for frequently used objects
- Use static classes for stateless utilities
- Consider lazy initialization for expensive operations
- Implement proper disposal patterns

### **Math Utilities**
- Use Unity.Mathematics for performance-critical operations
- Cache expensive calculations when possible
- Consider lookup tables for complex functions
- Use appropriate precision for calculations

## Best Practices

### **Development**
- Use descriptive names for utility methods
- Provide XML documentation for all public methods
- Include usage examples in documentation
- Implement proper error handling and validation

### **Performance**
- Profile utility methods for performance bottlenecks
- Use appropriate data structures for collections
- Consider memory allocation patterns
- Implement lazy evaluation where appropriate

### **Integration**
- Follow established naming conventions
- Use consistent parameter ordering
- Implement proper null checking
- Provide overloads for common use cases

## Testing and Validation

### **Unit Testing**
- Test all utility methods with unit tests
- Include edge cases and error conditions
- Test performance characteristics
- Validate null handling and error cases

### **Integration Testing**
- Test utilities in actual game scenarios
- Validate performance under load
- Test compatibility with different Unity versions
- Verify proper memory management

## Future Enhancements

### **Potential Additions**
- **Async Utilities**: Task-based async operations
- **Network Utilities**: Networking helper methods
- **Serialization Utilities**: Custom serialization helpers
- **Localization Utilities**: Internationalization helpers

### **Performance Improvements**
- **Burst Compilation**: Burst-compatible math utilities
- **Job System**: Job-based utility operations
- **SIMD Operations**: Vectorized math operations
- **Memory Optimization**: Zero-allocation utilities

## Related Systems

- **[[System Overview]]** - Used throughout all core systems
- **[[Enemy System Architecture]]** - Extensive utility usage
- **[[Projectile System Architecture]]** - Math and vector utilities
- **[[Player System Architecture]]** - Transform and component utilities

## Notes

The BTR Utility Systems provide **essential infrastructure** that enhances code quality, maintainability, and development efficiency throughout the project. These utilities follow Unity best practices and modern C# conventions while providing performance-optimized solutions for common operations.

The system's strength lies in its comprehensive coverage of common programming patterns and Unity-specific operations, reducing code duplication and improving consistency across the project. The modular design allows for easy extension and customization while maintaining backward compatibility.