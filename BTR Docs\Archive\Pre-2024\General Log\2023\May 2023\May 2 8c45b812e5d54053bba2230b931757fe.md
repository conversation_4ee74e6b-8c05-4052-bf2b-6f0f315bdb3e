# May 2

Object Placer - <PERSON><PERSON>t
May need to implement this - has a ALign to Surface feature - needs to look at player and do the same?

Clamp them to the navmesh using e.g. AstarPath.active.GetNearest. See AIPath.ClampToNavmesh for an example.

Fixed triangles problem on mesh scanned, used a more detailed mesh, but this did not solve the falling issues exactly. Seems better though?

Tried ClamptoNavmesh script in A* and it cause problems with enemy movement, getting stuck.

Looking into this advice

Clamp them to the navmesh using e.g. AstarPath.active.GetNearest. See AIPath.ClampToNavmesh for an example.

Cannot get Torodial working, not sure why! Something to do with the game object? What am I missing? 

GOT IT WORKING WOOO

Need to change these to enemies and get shoot and scoot working

Or another attack / move combo

Ouroboros 3 - have music and other things working. Best template so far - can refence others for minor issues

Some things to fix

- Enemies are shooting but not moving
- Player running anim direction gets wonky - why?
- <PERSON><PERSON><PERSON> Reticle sideways and wonky - why?

_ Moonlight - check firewall and port forwarding for UDP 47998, 48000_