# September

**Sept. 1st**

Experimenting with this on objects

Problems: Mesh that requires multiple materials - how to fix????

Problems: StackOverflow errors when glitch duration is too low - sub 0.2 I think

What is the issue? Does rrestructing this help?

IDEA - Playing with Shapes + Tessera manipulation!!

Place them alll around randomly

Really just —- make a level! abandoned places - failing archetecture - do this! move through

Use Mesh Combiner and then apply joost on top of that? Might be best solution

Reduce in Blender first though!

September 6th

Modify Offset Matrix in Joost scripts

Want to have this done to children objects

Made #script Joost Mod Group

Allows random offset vectors for child objects with set timing

Also made #script Joost Music Mod Group

Also Joost Music Mod Grow Shrink Group

- need to implement scale factor in a better place here. not effecting counter
- int / float issue!

**September 7th**

[http://codebetter.com/patricksmacchia/2008/11/19/an-easy-and-efficient-way-to-improve-net-code-performances/](http://codebetter.com/patricksmacchia/2008/11/19/an-easy-and-efficient-way-to-improve-net-code-performances/)

For instead of for each when possible?

#script Joost Music Exp Deep Child 

Attempting to get Renders and adjust mats of all children of children as well!

Also simplifying some code - it works!!

Change scaleFactor to incrementValue

Changed beginning / end of loop so it doesnt look like it stutters

**Sept 13th**

Mixed post processing objects with non-post!

[https://www.youtube.com/watch?v=LAopDQDCwak](https://www.youtube.com/watch?v=LAopDQDCwak)

Use this for glitching techniques