# UniTask Implementation Guide - Zero Allocation Async Operations

## Overview
This technical recommendation outlines the implementation of UniTask in BTR to improve performance and reduce memory allocations in async operations. UniTask provides a zero-allocation async/await integration specifically designed for Unity.

## Current System Analysis

### Scene Management System
- Fully converted to UniTask
- Implements proper cancellation support
- Uses progress tracking and error handling
- Optimized memory management

### Audio System
- Converted to async operations with UniTask
- Maintains backward compatibility
- Implements proper cleanup and error handling
- Thread pool utilization for FMOD operations

### Time Control System
- Converted to async operations
- Implements proper cancellation support
- Enhanced error handling and state management
- Improved QTE integration

## Key Areas for Improvement

1. **Scene Loading Operations** ✓
   - Completed: Uses UniTask-based scene loading with proper cancellation
   - Location: `LoadAdditiveSceneAsync`, `LoadSceneAsync`
   - Features: Zero-allocation async operations, progress tracking

2. **Event Handling** ✓
   - Completed: Async event management with proper cleanup
   - Location: Scene transition events and wave management
   - Features: Enhanced error handling, memory leak prevention

3. **Progress Tracking** ✓
   - Completed: Integrated with UniTask progress reporting
   - Location: Scene loading progress updates
   - Features: Efficient progress updates, proper cleanup

4. **Audio System** ✓
   - Completed: Async FMOD operations with thread pool
   - Location: `AudioManager.cs`
   - Features: 
     - Async instance management
     - Thread pool utilization
     - Proper cleanup
     - Backward compatibility

5. **Time Control** ✓
   - Completed: Async time manipulation with proper cancellation
   - Location: `PlayerTimeControl.cs`
   - Features:
     - Async QTE integration
     - Enhanced state management
     - Proper cleanup

## Implementation Examples

### Audio System Implementation

```csharp
// Modern Async Implementation
public async UniTask<EventInstance> GetOrCreateInstanceAsync(
    string eventPath, 
    CancellationToken cancellationToken = default)
{
    try
    {
        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
            cancellationToken, 
            _destroyToken.Token
        );
        
        if (!audioPool.ContainsKey(eventPath))
            audioPool[eventPath] = new Queue<EventInstance>();

        if (audioPool[eventPath].Count > 0)
            return audioPool[eventPath].Dequeue();

        var instance = await UniTask.RunOnThreadPool(
            () => FMODUnity.RuntimeManager.CreateInstance(eventPath),
            cancellationToken: linkedCts.Token
        );
        
        return instance;
    }
    catch (Exception e)
    {
        Debug.LogError($"Error creating audio instance: {e.Message}");
        throw;
    }
}

// Backward Compatibility Method
public EventInstance GetOrCreateInstance(string eventPath)
{
    if (!audioPool.ContainsKey(eventPath))
        audioPool[eventPath] = new Queue<EventInstance>();

    if (audioPool[eventPath].Count > 0)
        return audioPool[eventPath].Dequeue();

    return FMODUnity.RuntimeManager.CreateInstance(eventPath);
}
```

### Time Control Implementation

```csharp
public async UniTaskVoid HandleRewindToBeat()
{
    if (!crosshairCore.CheckRewindToBeat() || 
        Time.time - lastRewindTime <= rewindCooldown)
        return;

    try
    {
        lastRewindTime = Time.time;
        if (IsQTEConditionMet())
        {
            await TriggerQTEAsync(rewindDuration, 3);
        }
        else
        {
            await RewindToBeatAsync();
        }
    }
    catch (OperationCanceledException)
    {
        Debug.Log("Rewind operation cancelled");
    }
    catch (Exception e)
    {
        Debug.LogError($"Error during rewind: {e.Message}");
    }
}
```

## Benefits

1. **Performance Improvements**
   - Zero-allocation async/await operations
   - Efficient thread pool usage for audio operations
   - Reduced GC pressure
   - Better frame time consistency

2. **Code Quality**
   - Consistent async patterns
   - Better error handling
   - Proper cancellation support
   - Enhanced state management

3. **Maintainability**
   - Clear async/await patterns
   - Better resource cleanup
   - Enhanced error propagation
   - Backward compatibility support

## Implementation Steps

1. **Package Installation** ✓
   ```
   // Via UPM
   https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask
   ```

2. **Conversion Priority** ✓
   1. Scene loading operations
   2. Wave system async operations
   3. Audio system integration
   4. Time control system
   5. Event system integration

3. **Testing Considerations**
   - Use UniTaskTracker window for monitoring
   - Test cancellation scenarios
   - Verify memory allocations
   - Profile frame times

## Migration Strategy

1. **Phase 1: Core Systems** ✓
   - Convert SceneManagerBTR
   - Update loading screen integration
   - Implement new progress tracking

2. **Phase 2: Supporting Systems** ✓
   - Convert audio management
   - Update time control
   - Enhance error handling

3. **Phase 3: Optimization** ✓
   - Profile and optimize
   - Implement pooling configurations
   - Add comprehensive error handling

## Monitoring and Debugging

1. **UniTask Tracker Usage**
   - Monitor active tasks
   - Track memory leaks
   - Debug task status

2. **Performance Profiling**
   - Use Unity Profiler
   - Monitor GC allocations
   - Track frame times

## Notes

- Maintain CancellationToken propagation throughout async chains
- Use PlayerLoopTiming appropriately for different operations
- Consider implementing custom TaskPool settings for optimization
- Document any specific error handling patterns
- Maintain backward compatibility where needed
- Use proper cleanup in OnDestroy
- Handle edge cases in async operations
- Test cancellation scenarios thoroughly