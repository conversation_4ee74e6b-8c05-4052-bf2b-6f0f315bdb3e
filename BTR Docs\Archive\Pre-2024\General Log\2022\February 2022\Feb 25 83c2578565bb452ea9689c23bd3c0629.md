# Feb 25

Having issues with current version, unsure why things aren’t working

Grabbing a pre-major Ubisoft weekend overhaul version of the project (Feb 15th) to convert to FMOD

Copying files to 5TB from a backup - in main folder

Levels and audio should be working fine in that one

Also watching FMOD + Koreographer video implementation again

Back to current interation of project.

Enemy Shooting didn’t work with Unity’s Audio Engine Disabled

Means the shooting is tied to the audio engine - most sounds are tied to Koreogrpaher

Need to recouple with FMOD instead

![Untitled](Feb%2025%2083c2578565bb452ea9689c23bd3c0629/Untitled.png)

FMOD Koregraphy Set is link between FMOD names with the Koreography stuff in Unity

Music is playing! Forgot about this essential part !-!

![Untitled](Feb%2025%2083c2578565bb452ea9689c23bd3c0629/Untitled%201.png)

CHronos doesn’t affect audio anymore- Worked for AudioSource 

Physics Rewind - [https://www.youtube.com/watch?v=eqlHpPzS22U&t=47s](https://www.youtube.com/watch?v=eqlHpPzS22U&t=47s)

**Ask <PERSON> about rewind and glitch in FMOD? - Others like Master Audio? Fabric?**

**Try AudioSource Implementation Again**

Analyze possibilities for internal system vs Middleware

Tried using instances with projectiles but not hearing anything

Notes on Master AAA

- Has a randomization feature built in, no need for coded array
- Set sounds to events - wonder if custom options exist?