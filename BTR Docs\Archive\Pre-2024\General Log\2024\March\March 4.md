# March 4

Working on Infinite track / snake chasing

snake eveballs shooting exist so far

Create basic class for chasing snake - considered MidBoss

Have a callback class on colliders that can be hit. 

They call back to MidBoss with their names

<PERSON>lider Hit Back’s send the gaemobejct name and the damage amount to the boss script

Some basic implementation of enemy lock on for the call back collider class

Also written into Crosshair so it sees it properly

Not working for lock on, need to take a look at why