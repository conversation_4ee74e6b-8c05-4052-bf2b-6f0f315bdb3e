# Sept 18

Updates on [Sept 4](Sept%204%20897b9f685c254d90a4890ed785a55635.md) List

SceneManagement

- ~~Current state is better than before - any more improvements worth doing?~~
- ~~Long standing issue that pops up every few weeks - important to solidify this system and make it error proof~~

Fmod 

- ~~rebuild automation on Slow / Rewind to see if that fixes things~~
- ~~try global vs local setting for both, see if that works~~
- ~~try new automation field Time, see if that works~~
    - ~~Ultimately better to move to this regardless~~
- Should slow time cause doppler effects? How would this work?

Koreographer

- Chart out my approach with appropriate naming, will make things easier moving forward

Projectile Spawner

- ~~Verify being effected by time is not breaking the functionality, doesnt seem to behave entirely correct~~
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl
- Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it

Camera

- Game Manager / Cinemachine Camera Switching relationship needs to be cleaned up, its messy and could be much nicer!
- higher lookahead time seems to help with camera in reverse position , but causes issues elsewhere. Need to refine its movement
- Camera needs to be smoother / not move when reversing directions

Random

- ~~Look into DestroyEffect.cs - what is it really doing, is this good?~~
- Need effect that shows wave end - explosion, burst at center of level?
    - STARTED - Event Manager system started for this too
    - Is it effective? does it make sense?l
- Death → Restart cycle needs to be built out more
- Add trails to enemy snakes - will look nicer
- Refine Debug Settings scriptable object
- Can I have lock on sound progressively pitch up? Or is Fmod not capable of this logic?
- resetting projecitle lifetimes, whats the best values? need to refine this so you cant just hold bullets forever - i think, could be a mechanic - like bomberman sort of
- Odd issue where Gme Widnow has a differnet camera angel in editor depending on where and how it’s placed. Really strange!
- Finish DoTween → PrimeTween conversion
- Better Material for player - want to see shape contours - test toon shaders
- Implement various enemy shooting patterns again? Assess where this will be used first
- Where are interesting places to use Deformer?
- radar should be adjusted per level / scale is off
- Transition camera not currently in use, need to integrate at some point
    - Related, do i want to turn off player anymore or just use transition cam pointed elsewhere?
- AdjustSongParamters is free to delete - remember! dont need it
- Make more levels by alternating path player is on / flipping things upside down or sideways. Currently most relevant to Ouroboros structures
- More FX needed, more feedbacks, more sounds. Think about what player needs to know - maximum locks, projetiles are close by, etc
- Current Feedbacks not entirely working? Verify all of them.

QTE

- System needs refinement
    - also enemy lock list needs fixing due to being called enemy qte locklist, fix naming

Resources

https://www.tiktok.com/@pennywhistlestudios/video/7404455391200857390?embed_source=121374463,121451205,121439635,121433650,121404359,121351166,121331973,120811592,120810756;null;embed_masking&refer=embed&referer_url=cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.tiktok.com%2Fembed%2Fv2%2F7404455391200857390&display_name=tiktok&url=https%3A%2F%2Fwww.tiktok.com%2F%40pennywhistlestudios%2Fvideo%2F7404455391200857390&image=https%3A%2F%2Fp16-sign.tiktokcdn-us.com%2Fobj%2Ftos-useast5-p-0068-tx%2F9e89bc7ca8d442c4bfe8692cfff7e9af_1723984137%3Flk3s%3Db59d6b55%26x-expires%3D1724158800%26x-signature%3D7vJ%252BmktoLMpUG42yvD3SlYW9B%252Bc%253D&key=2aa3c4d5f3de4f5b9120b660ad850dc9&type=text%2Fhtml&schema=tiktok&referer_video_id=7404455391200857390.

https://www.youtube.com/watch?v=oxeD8kuCT_g

Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Build Issues - 2024-09-4

- Material / Shader for first structure is bugged - need to change
    - Removing wireframe to see if that is the issue

[Enemy Types](../../Ultimate%20BTR%20Dev%20Guide%20f9bf438fed974cc8b5f9b81a42a01296/Enemy%20Types%20877a54217fc845778f00204ffddc01d5.md) 

Level Assessment - Ophanim and others

[July 5th](../July%208fb5fd56583f43249ff8b3be6bfe7658/July%205th%205c9292d93b634fcb91521b76bbda468b.md) 

Previous task list - anything relevant?

[May 15](../May%209ac535a1c587434abe88186ee2b96d25/May%2015%20b4778e7f23ba46e39d3f0f43659cc83d.md) 

Attempted Collective project notes

[May 22](../May%209ac535a1c587434abe88186ee2b96d25/May%2022%20d0383e67d7af4be28bed0b866cd41c5c.md) 

Projectiles idea

- Make a system where they once they reach a certain radius, they hit the player in time?

more rhythmic?

- Have them surround in semi circle? Lots of different patterns possible here
- Would Flexalon help with this?
- Should each locked projectile add to time as well?

**Major Game Design Issues**

- Need an end boss to Ouroboros
- Need proper interesting progression along the way
- Look at how systems combine
    - How many different approaches are available to the player at a time? Can the construct a playstyle or is there one way to play?