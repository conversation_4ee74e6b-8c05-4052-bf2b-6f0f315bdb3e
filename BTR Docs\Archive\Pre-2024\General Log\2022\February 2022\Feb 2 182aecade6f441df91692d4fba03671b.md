# Feb 2

Bug found - Issues with Chrono persist in Level Test 4

Looking at A* movement problems

- moved enemy prefab location in scene - may need to change back? 0 0 0
    - In ex: Ship and target are on ignore raycast layer - remeber this!
- trying changing BD to Seek to see if it changes error
    - Same errors with other BD - not a Scoot and Shoot error specifically
- Looking at earlier scenes to see differences

Takeaways to try!

- Layermask only Enemy Plane 1
- 

![Untitled](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled.png)

Local Space Rich AI is setting to Enemy Plane as graph

![Untitled](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%201.png)

![Untitled](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%202.png)

Look at Auto Repath Settings...

Nothing there

Setting walkable height/walkable climb too low causes movement issues cause enemy to stop moving around? Gets stuck on bridge terrain. Walkable slope important too, new settings for all seem to work. Rasterize terrain needs to be on as well

Now working, but movement is slow and jagged while platform is moving

Appears t o be a scale thing, once adjust height and radius of local space rich ai, things are good again!

Not perfect but promising!

Worked on Death Particle effect, refine process / look

Working on player / reticle jumpnig around while moving

- aim parent cant be disabled safely
- aim parent doesnt need to be on paritcular layer, neighter does camera

Is it a chrono thing? check time is working properly

Disabled shooting and still a problem with player

Player game object transform is issue, being updated somehow

Disabled player to see if shooting would exhibit behvaior - nope!

It’s in the Player GameObject

Issue is Local Move function - needs to be adjusted!

Bug found - bullets not reparenting properly, still attached to enemy at times when they’re locked by the player

Bug found - bullets circle back on itself

![Untitled](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%203.png)

Negative lifetime but still circling

laucnhing is the problem

- need to chart out all bools and the way they’re used so that all cases are covered

reparenting of bullets needs to be fixed as well

Looking into reparenting now

START
original parent = EP1
object is parented to EP1
new parent = EP1

UNPAUSED, in a moment was reparented to Pool

Used a general Enemy Bullet Pool for all enemy bullets, located under enemy plane. This fixes enemy rotating issues since it’s not attached to the enemy.

lots of bullets released from system / no parent now, see if this is necessary, may fix other issues

LaunchBack and LaunchAtEnemy methods both set parent to null, removing for testing

suspect there are issues with targetCube implementation

Improving bullet movement partile effect 

IDEA: Sensor toolkit on bullets so if they are in range of target they auto target?

Added Warp Effect into game - finish this tutorial

[https://youtu.be/VsGG5JYlqIU?t=369](https://youtu.be/VsGG5JYlqIU?t=369)

Posted to Aron about A* issues in forum, hopefully resolve them!