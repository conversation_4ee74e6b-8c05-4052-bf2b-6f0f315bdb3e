# Dec 18/19

May try to enact this struct or enum based approach to projectiles - need to fix version control first

fix homing for enemy shot projectiles - not homing properly

Mostly working - summary of changes we implemented

[Projectile System Changes](Dec%2018%2019%20Projectile%20System%20Changes.md)

Debug scripts

- Pathfinding debugger for testing alternate A* values on enemies and such
- Projecitle Pool Debugger for chekicng for thrashing (large pool size changes)

Moving movement system to jobs, some considerations

**Steps to Implement Chronos-Compatible Job Optimizations:**

1. **Identify Chronos-Controlled Logic:** Determine which parts of your projectile behavior are directly influenced by Chronos (movement, lifetime, etc.).
2. **Access the Relevant Clock:** In your ProjectileManager or wherever you schedule your jobs, get the Clock instance associated with each projectile.
3. **Pass Time Scale to Jobs:** Include the clock.timeScale as a parameter in the data you pass to your Job structs. Also, pass UnityEngine.Time.unscaledDeltaTime (or Time.fixedUnscaledDeltaTime if it's a fixed update job).
4. **Apply Time Scale in Jobs:** Multiply the base delta time by the passed timeScale within the Execute() method of your jobs.
5. **Test Thoroughly with Time Manipulation:** Test your projectile behavior with different Chronos time scales (slow motion, fast forward, pausing) to ensure everything works as expected.

Might have finally got jobs working for projectile movement - debugging needed