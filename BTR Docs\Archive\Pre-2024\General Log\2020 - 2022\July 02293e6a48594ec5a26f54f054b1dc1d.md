# July

July 1

Level Building - Sci Fi GreyBox 12

Implementing Ethereal Urp - Unsure on proper Forward Rendering to choose for camera

Need Fog to cover up LOD pop in issues?

Need to try Mesh Bakery for optimization as well

Need to construct moving enemies - possibly with Emerald AI? or overkill?

- Line of Sight attack, move along waypoint path, ranged attack options available
- Also option to have them become ally - interesting!

Also - reconnect to Source Tree!

**July 9** 

Adding keyboard controls - spinning and mouse work 

Fire not working

Synchronic film - music - drug is the needle on the record idea

**July 19**

Early level is not performant - Greybox 12

Need a better building → Polyfew → TessGen pipeline

Need to use more performant shaders that can be batched

Look at mobile transparency shaders?

Build a new level

Greybox 13

THING TO TRY - Polyfew Combine Meshes for level layouts - only when finalized?

Also maybe figure out <PERSON><PERSON>? Seems to be most popular

July 20

Ghost Shader - Studio CS Ghost UPR Advanced Always Visible

Make buildings one Mesh, some sevenral?

July 21

Optimized buildings in Blender - much better!

Game running much faster - no crazy draw calls

Need to try using ghost shader now too

construct more of level, see what I can do

Can build a few TessGen tiles before performance tanks

Can BatchFew the TessGen tiles - bit of placement required after

HUGE decrease in batch calls but performance isn't better?

MESH takes up too much memory it seems

How to reduce? Look into this. Can blender do this?

TIP 1 

![July%2002293e6a48594ec5a26f54f054b1dc1d/Untitled.png](July%2002293e6a48594ec5a26f54f054b1dc1d/Untitled.png)

"If enabled, Mesh data is kept in memory so that a custom script can read and change it. Disabling this option saves memory, because Unity can unload a copy of Mesh data in the game. However, in certain cases when the Mesh is used with a Mesh Collider, this option must be enabled. These cases include:

- Negative scaling (for example, (–1, 1, 1)).
- Shear transform (for example, when a rotated Mesh has a scaled parent transform)."

**July 23**

Keep seeing that number of verts and triangles could be the issue for batching. Trying severly reduced models to see how that fairs

SRP Batcher problems / benchmarking

[https://forum.unity.com/threads/gpu-instancing-not-working-properly-with-hdrp.809109/](https://forum.unity.com/threads/gpu-instancing-not-working-properly-with-hdrp.809109/)

Using a different shader for buildings cut batching by ALMOST half!

Using LOD's also cut batching down, by about half

July 24

Try Greybox 8 - Moving shapes movement

with Greybox 13 electrical platforms? or buidlings?

Movement layers - refer to VJ videos

Koreo Movement Script - use this for some movement?

Looking deeper into RealEffects4 - How to have random movement in shaders etc

Used Lighting Shot Material on platform

SineVFX may be better for this?

Reduced Far Clip Plane works for populated dense building area - likely not for less populated areas

July 26

Script animations for interesting movement of enivorment objects

Mesh Combine for scene optimazation

Can use Occlusion culling when full scene is decided upon

July 27

Debugging gameplay mostly

Rotating needs to adjust for rotation of Dolly path

If dolly turns causes issues for TriggerL/R rotations

Removed DOTween for rotation, hard coded, rotation working better now

Changed bullet lock on to Horizontal Plane for rotation of lock on bullets

Fixed aiming for 4 rotation positions - raycast works in all of them now

July 28

Moved rotate view to Player Movement from Shooter - doing some cleanup! Added back some animation to it (no DOTWEEN)

**NEEDS WORK - Rotate HARD to one of four positions**

[https://answers.unity.com/questions/1250587/how-can-i-rotate-an-object-to-a-specific-angle-ove.html](https://answers.unity.com/questions/1250587/how-can-i-rotate-an-object-to-a-specific-angle-ove.html)

Went back to old basic rotate- issue flagged for consultation!

Adding radar to bullet enemies - working!