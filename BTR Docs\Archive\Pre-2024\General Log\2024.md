# Combined Markdown Files
Generated on: 2025-02-07 14:17:43

---

## From: 2022.md

# 2022

January 2022:

- Explored using a spawner system and dynamic waypoints for enemy movement.
- Experimented with different approaches for player aiming and locking onto enemies.
- Encountered issues with enemy AI and bullet behavior, such as enemies getting stuck and bullets colliding with each other.
- Investigated using Behavior Designer for more advanced enemy AI patterns like "Scoot and Shoot" and "Flank".
- Worked on implementing a functional menu system with controller support.
- Optimized the project by compiling shader variants and using GPU Instancer.
- Explored using Ethereal URP for visual effects.

February 2022:

- Fixed issues with the rewind system and input processing when the game is paused.
- Continued work on the menu system and controller integration.
- Experimented with different approaches to handle the rotation and movement of the player and the level.
- Investigated using <PERSON><PERSON>ographer for better audio and music integration.
- Worked on improving the lock-on and shooting mechanics, including adjusting the timing and visual feedback.
- Explored using Tessera Pro and other tools for level design and optimization.
- Investigated using Polarith AI Pro for more advanced enemy AI.

March 2022:

- Implemented a Tetris-like gameplay mechanic, including piece movement and rotation.
- Worked on integrating the Tetris gameplay with the existing enemy and bullet mechanics.
- Explored using Jelly Cube and other puzzle-based mechanics.
- Continued optimizing the project, including investigating the use of Mesh Baker and Automatic LOD.
- Experimented with different visual styles and effects, such as the use of Chroma Pro Shaders.
- Encountered issues with the enemy spawning and pooling system, leading to further investigation and troubleshooting.

April 2022:

- Focused on resolving issues with the wave spawning system, working with the Trivial Interactive team to find solutions.
- Implemented additional UI elements, such as a wave hint and enemy hint, to improve the player's understanding of the game state.
- Continued experimenting with level design and enemy AI, including the use of the Ultimate Spawner and Waypoint systems.
- Explored ways to better integrate the music and audio with the gameplay, such as using Koreographer events.
- Investigated potential issues with the camera and player movement, particularly related to the rotation and perspective.

May 2022:

- Worked on improving the visual quality and effects of the game, including the use of Beautify 3 and other post-processing tools.
- Continued experimenting with different enemy and boss designs, exploring the use of geometry nodes and deconstructed shapes.
- Encountered issues with importing rigged characters into Blender, leading to investigations into using 3DS Max and Maya as alternative tools.
- Focused on solidifying the overall game design and creative direction, including decisions around boss and level structure.

June 2022:

- Explored integrating Tetris-like gameplay mechanics more deeply into the core experience.
- Continued work on enemy and boss designs, experimenting with different geometric shapes and deconstructed visual styles.
- Investigated issues with character rigging and importing into Blender, trying various workflows with 3DS Max and Maya.
- Worked on improving the camera and player movement, particularly related to rotation and perspective.
- Explored the use of Radar and other UI elements to enhance the player's spatial awareness and orientation.

July 2022 - December 2022:

- The documentation does not contain any specific details about the project's progress during these months. It's possible that development continued, but the available information does not provide a clear summary for this time period.

---

## From: 2023.md

# 2023

January 2023:

- Continued work on the ground check script, trying to fix issues with the player character randomly spinning the wrong way.
- Explored ways to improve the camera movement and rotation when the reticle moves towards the edges of the screen.
- Investigated methods to reduce build times, such as using Assembly Definitions.
- Identified and worked on fixing various small problems with gameplay, audio, and other aspects that were thought to be previously resolved.

February 2023:

- Implemented a "hacky" LookAt solution to stop the player game object from rotating.
- Figured out an issue with the orientation anchor in the Curvy Spline component, which helped fix orientation problems.
- Experimented with color-based projectile effects, but decided against a global material change approach in favor of a material swap.
- Recognized the need for a proper issue tracker or Kanban board to manage game bugs and development tasks.

March 2023:

- Adjusted the shooting movement to prevent the camera from going directly through the ground as much.
- Fixed issues with the audio filter no longer being applied when hitting the start button.
- Continued making small adjustments and fixes to improve the overall gameplay experience.

April 2023:

- Worked on integrating the project with Perforce Helix for source control and build automation.
- Investigated and troubleshot issues with the A *pathfinding system, particularly related to the Ouroboros level.*
- Explored the use of new URP features and visual effects, such as the Blender Tree Generator asset for creating a Yggdrasil-inspired environment.
- Discussed ideas for a boss encounter design, incorporating elements like time manipulation and Fresnel-based effects.

May 2023:

- Made adjustments to the Snake Head movement script in the Ouroboros level, which could be useful for other timed movement scenarios.
- Added ground collision detection on the main camera to improve the player's perspective and experience.
- Experimented with the idea of a "ricochet/shield" element, but ended up creating a trap-laying mechanic instead.
- Focused on balancing the speed of player movement and bullet speed to create a sense of the player being moments away from being hit.

June 2023 - December 2023:

- The available documentation does not contain any specific details about the project's progress during these months. It's possible that development continued, but the information provided does not include a clear summary for this time period.

Overall, the project's development in 2023 seems to have focused on resolving technical issues, improving the core gameplay mechanics, and exploring new visual and design ideas. The team continued to iterate on the player controls, camera, enemy AI, and level design, while also investigating ways to optimize the project's performance and build process.

---

## From: April 1.md

# April 1

Two weeks to job - whats important - whats next?

Looking at issues with player charcater bopping and rotating

rotating aimlessly is a A* issue?

- when A* disabled on player it doesnt fix it

jittery / bopping is a ground detection issue

Seemed to have smoothed out the jittery isssue with lerping on ground detection

Also enabling the rigidbody as kinematic has seemed to fix my intermittent rotation issues

wel… not quite! still debugging

seems to help only if i have player movement script disabled

A* stuff seems to not effect this issue

---

## From: April 10.md

# April 10

Need better planning for the boss stage - gameplay and sequence

[Boss Info](April%2010%2022a37e323c914880b27b78f172101be8/Boss%20Info%200f28c13082b44ff39083c8166ae6e499.md)

power ups like rez - upgrade character?

**IDEA**

Inspired by x men sentinel fight, flashes of color and light in time with the music

dark and violent

---

## From: April 12 13 14.md

# April 12 / 13 / 14

Twin snake - on each side

Polyrhythm shooting tied with music!

3 against two?

when landing on the beat - dodge!

otherwise can shoot back

- how can i do a parry option that fires in direction instead of lock on?
    - could be fun!

Attempting to add this parry shot to the game now, quickly tapping the lock button

Having problems with implementing this - removed most of the functionality but left quick tap features for trying this again 

Will need some rethinking and adjustment

Need to come back to this

Successful material swap on projectiles nows

trying to get a lock of VFX /  particle effect happening now

Added Movement sensor script to twin snakes to make sure they move a little bit if they’re about to animate / hit into the player track. 

- disabled cause it’s causing problems , will see if worthwhile in the future

Also adding fog density volumes to try and hide tails of snakes

SNake animations

- could use get hit left / right for firign things wheh nchasing on left / right

Have implemented this, i like it. 

Trying to get better projectiles accuracy at high speeds, adjustin g projectile logic for this 

Setting target to ‘Player Aim Target’ for my precise target for projectiles

twin snake boss did not have homing enabled….. 

very annoying

Need to clean up these classes a bunch

Now player appears to be getting hit

Fixed! Shoot speed effecting homing and non homing now

Now can set player and enemies much faster, and it’s not a problem for the bullets

need to chart out my relevant methods, slim down everything to make things clearer / work

For background

- Put tail animator snake on curvy spline, have it wiggle around in places

Need to properly plan out Ouroboros! AND other levels + tech needed

---

## From: April 15.md

# April 15

Going through files, finding things to help with Ouroboros Planning

- Snake as rings wrapped around a planet - ouroboros
- Think ouroboros - surround an orb - shoot any bullets at that orb to take out the health of the current orb and when you are successful a kill all enemies event occurs , per wave
- Ouroboros - A serpentine creature that encircles the player, creating an ever-shrinking arena. Its attacks are wave-based and must be dodged to the beat.

Adding koreographer to enemy twin snake boss does not work properly, requires further refinement

Should i have a speed dash that affects tempo / music?

Created background snake level object

Movement mostly figured out, minor adjustments needed

still need to figure out gameplay over time

---

## From: April 16.md

# April 16

Thinking about what i wrote here, in Mid march, again

> Thinking about how I avoid making music for the game… It's more than just procrastination. Generating sounds is easier now than ever. Anybody can make music IMO. But what is the MEANING? This is what drives this game, and why I so rarely work on the music for it. Because all of these systems, the second to second gameplay, is what drives the sound and when that becomes clear, the sound follows. The sound informs too! But I’m so acquainted with making sounds, it doesn’t need to be actively worked on constantly. I can imagine my way through the part of the audio process. This is why I avoid writing music in general. What am I writing about? Its not always words, but a feeling, and a general vibe and notion that drives the creation. And through video games and interactivity, I'm looking for new sources of meaning to drive creation, and ultimately the experience. This idea also fuels the point of the game. I see rhythm games, and the promise of Rez, and I think - the sounds could be informed with more meaning. Different meanings. More focus. More than just easily fading between stems and battle tracks. Though these are useful and effective.
>

---

## From: April 19.md

# April 19

Im sick it sucks

Writing out ideas for how to setup and optimize game

- Use scriptable objets to define attributes
    - Projectiles
    - Enemies
    
    Events - public event Action
    
    register for these changes
    
    Seems like the easiest - best way to use Events in Unity
    
    This is an Observer pattern?
    
    If data is constantly changing, this is not a great pattern to use
    
    Ideas for use 
    
    - register projectiles? Register enemies?
    
    Singleton
    
    Using for Game Manager currently
    
    May be useful for other stuff
    
    Code Monkey Intermediate course
    
    Shortcuts
    
    Ctrl + left / right for cursor movement
    
    Home and end keys, beginning and end of lines
    
    Visual Commander OR some other tool to allow easy skipping of X lines at a time.
    
    Maybe 5 lines at a time? Would be better movement then pg Up / Down
    
    Ctrl + SHift + up / Down - when asymbol is selected, move between instacnes of that symbol
    
    example - variable, method, etc
    
    Make sure my refactor affects all instances across the project, and then refactor!
    
    A lot of stuff could be named better
    
    Should I put all my classes I've written in a custom name space? Would this be good?
    
    No! Does t work that way
    
    Should extend my use of interfaces, would be helpful for organization 
    
    What are some good ways I can use Flexalon?
    
    Projectile factory - useful to me?
    
    https://assetstore.unity.com/packages/tools/particles-effects/projectile-factory-behavior-based-projectile-system-281692

---

## From: April 2.md

# April 2

Adjusted ensure player upright to be relative to parent object and that seems to have fixed seemingly random rotation of Player game object on wave/level object change

---

## From: April 22.md

# April 22

Very Cool GDC Talks

How to Make Things "POP" with Audio and Color

[Independent Games Summit: How to Make Things](https://gdcvault.com/play/1034550/Independent-Games-Summit-How-to)

Notes

- Limitations to help with an identity
    - voice only for hidden folks
    - how a child perceives for among the sleep (dragging chair feels like a HUGE chair)
- Find your themes - derive and essense
    - establish patterns, consistency trumps quality (in some ways it IS quality)
- audio as the player-whisperer
    - gun sound is powerful or gun is weak
- colours can do this as well
    - saturation of a color for example

![Untitled](April%2022%2000bc14fc0d244621a6b364245350909c/Untitled.png)

Still picking through more 

Other notes

Plan out game and levels

Include more rhythm elements??

Find the fun!!

---

## From: April 24.md

# April 24

Crosshair refactored to LockAndShoot

lockandshoot

- playerlocking on and player locking off events
    - use these to trigger feedbacks? currently private
    - Can assign them to some list or object?
    - Are they causing errors once ive edited them / commented out?

slowTime variable not assigned in boss twinsnake scene- what should it be?

THIS IS WHATS BREAKING SLOW TIME - FIND APPROPRIATE EFFECT

Adjusting the Boss Time’s that are indepedent of standard time, to be affected by slow time. 

In the parent Boss script

Adjust SlowToBeat so it effect player movement on spline time as well

Bullets getting stuck around player - not sure why - is this only in the Twin enemy snake level?

```jsx

```

Getting all these errors when i shoot

![Untitled](April%2024%20fea7a6ff4e544d4a93b93d5391502e1d/Untitled.png)

Seems like Look direction getting 0 or NaN values given, need to avoid those situations?

Bullet speeds too slow in some circumstances. maybe default is too slow? Sped u, it helped

At some point, bullets are getting stuck on kinematic, and arent able to move, that’s why i cant shoot properly at times.

Not sure what triggers this, but also trips up FMOD as well

Seems something like being stuck in locked by player state, some transition not working properly

May have to do with updates made dureing twin boss bullet lock on decisions, to make them transparent and follow reticle. Test other scenes, see how this behaviour manifests there

Is it the same problem that occurs in regular ouroboros scene?

---

## From: April 28.md

# April 28

Loading up a previous version of the project

Log of errors that occur

- Beat Render missing features
    - Buto issues
- Rebuild FMOD project
    - Do a build within FMOD software
- Lighting needs to be generated again
    - Bakery
- Regenrate A* navmeshes

Assessing Differences Between April 14th and Current version

- Create background snake with tail animator
- Koreographeron Enemy Twin snake boss needs fixed
- Errors likely stem from April 24th changes

---

## From: April 30.md

# April 30

Working with Mid April now → forward from here

**Back To**

Assessing Differences Between April 14th and Current version

- Create background snake with tail animator
- Koreographeron Enemy Twin snake boss needs fixed
    - Now working! Added Koreos for alternating patterns
- Errors likely stem from April 24th changes

Need to assess if deleting wait for it koreo sets breaks anything in current iteration of game

Replaced Enemy Twin Snake boss and Controller, now shooting not working properly? Unsure what’s broken this time around?

I SEE!

I accidentally deleted Perc!!! Thats what broke it! Just a dumb mistake in the Koreographer interface

Be aware of this when creating a new track / events

---

## From: April 4.md

# April 4

Started on Idea of Infinite track but following a spline

This is potentially how we can have some randomization in the boss battle but restrain to a specific area

May be useful in general!

OuroBossInfiniteTrack is the script, needs more logic from original script

---

## From: April 5 6.md

# April 5 / 6

Attempting a roughly set / randomized path for the boss fighting. A variation on OuroborosInfinite

Not fully working but making progress

Rough things laid down for the twin snakes

Working on the rough forward / reverse time feature of the twin snakes

Issue with the snakes not finding their time line components properly, need to company to wroking example and see whats wrong here

**April 6**

Made adjustments for Controller and Enemy Twin Snake Boss so that they can tie into the time keeper / global clock and move forward and backward

---

## From: April 7.md

# April 7

Working on boss ideas of Twin Snakes / Time

Also optimization

**2. Extending ProjectileStateBased for Specific Enemy Behaviors**

If EnemyTwinSnakeBoss requires specific projectile behaviors not covered by the current states in ProjectileStateBased, consider adding new states. For example, if the twin snake boss shoots a special type of projectile that splits into two after a certain time, you might need to create a new ProjectileState subclass to handle this behavior.

### 4. Handling Projectile Pool Types

If EnemyTwinSnakeBoss uses a unique type of projectile not shared with other enemies, consider adding a new ProjectilePoolType enum value and adjusting the pooling logic in ProjectileManager to accommodate this. This would involve creating and managing a new pool specifically for these projectiles.

`ProjectileStateBased.cs`

`ReplyApplyCopy`

`public enum ProjectilePoolType`

`{`

`StaticEnemy,`

`EnemyBasicSetup,`

`TwinSnakeBoss *// New pool type for TwinSnakeBoss projectiles*`

`}`

### Conclusion

The current implementation of ProjectileManager and ProjectileStateBased appears flexible enough to support the integration with EnemyTwinSnakeBoss without immediate changes. However, based on specific gameplay requirements or desired projectile behaviors, the optional adjustments outlined above can help ensure a robust and flexible projectile system that accommodates the unique aspects of the EnemyTwinSnakeBoss.

```csharp
 //This doesnt work! Error with assinging a new clock at runtime. Need to look into Chronos.
           // newTimeline.clock = clock;
```

---

## From: April 8 9.md

# April 8 / 9

Some adjustments to organization for prefabs and pooling

THinking about what the boss fight will actually be.

Time rewind mechanic in action + music reacting 

Have a successful rotation of twin snakes looking at play,er but they do intersect in weird ways. 

Possible idea - use A* for snakes on  a platform if moving around to follow player? Move slow

Maybe not A* for this, not sure if truly a benefit though it does work.

---

## From: August 1.md

# August 1

Minor adjustments - most importantly, need a proper asset list !

Priority for tomorrow / the weekend. 

While still available

---

## From: August 12.md

# August 12

COnverting some DOtWeen to Prime Tween 

Need to look over and make sure all work evenetually

Scripts caught as having DoTween, upgrading some

DestroyEffect

- done

LookAtGameObject

- done

UILockOnEffect

- done

ColliderHitCallback 

- done

PlayerMovement

- done, very important to verify!!!

EnemySnakeMidBoss

- wrongly thought it was there, but it isnt

DestroyEffect

- done

Also, trying to fix previous issue with getting locked in rewind time, unsure exactly how that’s happening. Possibly FMOD issue as well

Play test and see if it pops up again after FMOD fixes

August 13

Issue popped up again. Code base scan showing these potential issues 

-In the Crosshair script, check the EndRewind method to ensure it's properly resetting all rewind-related variables and states.

-In the ProjectileStateBased script, ensure that the lifetime is properly managed during rewind:

-In the GameManager script, make sure the time scale is properly managed during and after rewind:

-In the ProjectileStateBased script, add safety checks to prevent unexpected behavior:

-Implement a global reset function: Create a method in GameManager to reset the game state, including projectiles, when switching between normal and rewind modes:

Should move time features to Game Manager and just have everything talk to that. 

Considering priority system so time interrupts are possible. 

Make sure adjustments to timescale by gamemanager are global when needed, local when needed

Shifted time features to Game Manager - not fully working but needed to be done!

Need to implement the player / crosshair time features properly now, they dont quite work as they used to

---

## From: August 14.md

# August 14

Transfer of time control to game manager seem to be working for Crosshair functionality. 

Other classes may need adjustment. 

Slowly making it throgu hthem

Also fixing ricochet dodge

WOrks again now! Refine again

Odd issue where Gme Widnow has a differnet camera angel in editor depending on where and how it’s placed. Really strange!

Uploading build with all these adjustments changes, though need to test a bit more

Thoughts on sound design

https://www.youtube.com/watch?v=oxeD8kuCT_g

Bookends and Highway method

Use FilterFreak and Speakerphone for processing your SFX

Plan creatures as Blue / Red - Soft / Angry

Pre-attack sounds

- design by making the first bookend (front)
- Copy it and it can be your pre attack

Granulate - seems like a cool Granular synth

---

## From: August 16 17.md

# August 16/17

Fixed score issues where values displayed for player getting hit were not accurate

Enemy Killer script not fully workign, something wrong in Enemy class I think, debugging

- Fixed

Section 3 music not looping properly

Enemy spawning issue

Make tail aniamtor work when off screen as well? or some thing issue ossuring?

Enemy bullets too slow? NOt getting hit

Major code revisions in Game Manager to account for async scene loads

Seem to work! 

Upgraded Editor version in hopes of solving lack of memory issues occuring during deep profile

so far so good! but Memory Allocation tracking in prime tween not as straightforward as i thought

Fixed some issues with Enemy Damagable parts, and managed to maintain Burst 

A LOT of time on resetting projecitle lifetimes, necessary work. need to refine this so you cant just hold bullets forever - i think, could be a mechanic

like bomberman sort of

May need to rebuild Section 3, weird issue with not being able to shoot there. Not sure what the problem is exactly :(

Likely a koreography problem - track missing for shooting? Seems reasonable

THis was the problem! I need to chart out all of these things so i know and dont get stuck on stuff liek this again

Need to be more organized

---

## From: August 19th.md

# August 19th

Fixing looping issues with Section 3 - FMOD adjustments needed

Do these same fixes help with Shooting? It isnt workign for some reason

- No! SOme other reason i cant shoot. Can lock on to enemies and bullets but wont fire?
- These are snake enemies, so something could be amiss

---

## From: August 2.md

# August 2

Feel like i need to inject a bit more speed in the game

Movement being one - rest to follow

Made things faster in first ouroboros level, aslo adjusting camera

higher lookahead time seems to help with camera in reverse position 

Play testing earlier

- Bounce sound could use more note choices, more of a sense of harmony and surprise? Drive some melody? Or refer to unfold Gabor Lazar for change ideas
- 
-

---

## From: August 20.md

# August 20

Built extended debugging tools for ConditionalDebug

Can select specific classes and see debug messages

Might go further with this or just look into standard unity debugger to see if im reiventining the wheel

Point of this is to see properly if Projectiles are hitting enemies. Might build some of those messages into the tool? 

- Did this, basic attributes for testing added

Some extended features like Debug Time Control (not working)

---

## From: August 21.md

# August 21

Working on Projectile accuracy, first improving the debugger to track what the accuracy is

SHow 8 and 15 percent as hit rate, seems really low

THis has increased to roughly 50%, working on making it even better

Significant improvements, at around 70% now, something worht monitorign moving forward. 

Still trying to improve this as it drops to 50% at times.

Enemy Snakes are working, but need refinement. Refinign the damagable parts aspect to work cleaner

Changing Wave patterns for shorter delays

ENemy bullets still have trouble hitting at times, 

Cmaera not acting very well, attempt to fix with old setup

Lookahead attribute is the big factor, cant be too much, screws things up

Adding a score / time boost when cahnging waves / sections. 

Adding gizmos to projecitles to see whats going wrong, what adjustmetns i need to make so that the enemies always get hit. 

Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl

Made things less efficient, need to try to get back to efficiency. I thoguht I kept burst for projectiles… need to revisit these issues. Run a diff one some uploads with cursor

Deleted Projecitel Path gizmo due to potential errors in physics calculations, 

Not entirely sure whats hogging performance, tracking it down - try not to waste too much time on this

---

## From: August 22.md

# August 22

Various optimizations to Projectile Manager and ProjectileStateBased

Maybe worth not having timelines on every Projectile? Maybe that’s not correct

Tonight, should architect a better transition system. 

Consider all parts, work it out - will make long run easier!!

Strangely, performance is showing a locked 60 and i hav no ida why. there are dips but gnereally it’s at 60, seems strange to me. Requires investigating. 

**August 23**

This seems to have disappeared. Interesting!

Need a better transition system, not happy wiith the current one

---

## From: August 26.md

# August 26

Analyzing transition system

On Wave Started 

Do we need OnSwitchScene event script? Im nto sure its neceaary on enemy Spawn Controller

Making wave event subscriptions the only way for spline manager to increment, in an effort to simplify process and understand what’s happening better 

This is working now

Wave → Spline Manager → Game Manager if last spline

Various graphics / effect broken in build - unsure why

Need to verify 

- Fixed JPG effects but not sure why initial texture isnt working and gasme volumes are at 0
- Try without main menu - see if it fixes things?
- Need to figure out resolution is low, settings messed up in builds

Read docs on Reach menu system, figure it all out 

**August 27**

Been updating this and figuring out whats up 

QTE Implemented, but errors, need to bug test but it might be interesting!

Stuck in rewind music phasem, need to address this

other action seem find, feels like its just the music?

---

## From: August 3 5.md

# August 3/5

Minor bug fixing. Issues with FMOD that i’m addressing

Speeding some things up in the game to increase intensity

- Snake shouldn’t do hit anaimation and then death animation, just death
    - Done!
- Disbale Y rotate direction in cahsed by snake level
    - Done! Disable Player Features script
- Cant shoot in section after snake, Koreogrpaher issue?
    - Beleive i fixed this once before

August 5

Really dont have it in my to work on this today

SNake gets hit sound, as well as Death sound needed

- Done! Needs adjustments but a good start

Section 3 music not looping properly

Section 3 need the enemies and bullet speeds adjusted for faster movement. 

Consider second snake encounter - maybe not the crazy graphics? WHat else ot make it different 

Enemies flashing could be good, whats a good way to implement that? let’s look into it.

---

## From: August 30.md

# August 30

Some recommendations on Game Design

- GDC talks
- Designer Notes Podcast
- Google Scholar
    - games and culture
    - gamestudies.org
- Abstract - The Art of Design

Between different levels, highlight difference - think of how it plays with your mechanics

---

## From: August 6 7.md

# August 6/7

Here's a bullet-pointed list of suggestions to tackle one by one, based on the provided code snippets:

- Optimize ProjectileManager.cs:
    - Replace Queue<ProjectileStateBased> with Stack<ProjectileStateBased> for better performance
        - potentially minor improvement? Have not implemented
    - Implement Unity's C# Job System for projectile updates
        - Job System is being used but only for processing new projectile requests, not for updating existing projectiles.
    - Use NativeArray or NativeList for projectiles and projectileLifetimes
        - Done!
    - Apply Burst compilation to performance-critical methods
        - Done!
- Enhance ParticleSystemPooler.cs:
    - Implement Unity's C# Job System for particle system updates
        - Done!
    - Use NativeArray for the pool instead of List<ParticleSystem>
        - Done!
    - Apply Burst compilation to GetFromPool and ReturnToPool methods
        - Done!
- Optimize GameManager.cs:
    - Convert frequently updated properties to structs
    - Use NativeCollections for spawnedEnemies and lockedEnemies
    - Implement a custom memory allocator for frequently allocated/deallocated objects
- Improve Crosshair.cs:
    - Utilize Unity's new Input System for more efficient input handling
    - Use NativeCollections for enemyTargetList, projectileTargetList, and LockedList
- Optimize SplineManager.cs:
    - Implement Unity's Job System for spline calculations
    - Use NativeArrays for storing spline data
- Enhance OuroborosInfiniteTrack.cs:
    - Use Unity's Job System for procedural generation
    - Implement object pooling for spawned prefabs
    - Use NativeCollections for activePrefabs and despawnedPrefabs
- Improve PlayerHealth.cs:
    - Optimize hit effects pool using NativeArray or NativeList
    - Implement a custom update loop to reduce update frequency for non-critical objects
- Optimize SceneSwitchCleanup.cs:
    - Use more efficient scene traversal methods
    - Implement asynchronous object finding to reduce performance impact
- Enhance EnemyKiller.cs:
    - Use Unity's Job System for enemy and projectile destruction
    - Implement more efficient methods for finding objects by layer
- General optimizations:
    - Implement custom update loops across all scripts to reduce update frequency where possible
    - Use structs for small, short-lived data structures throughout the project
    - Implement a centralized object pooling system for all frequently created/destroyed objects
    - Use Unity's Burst Compiler for all performance-critical code sections
    - Implement Unity's new Input System across all input-handling scripts
    - Implement memory-efficient data structures (e.g., NativeCollections) across all scripts
    - Optimize scene loading and unloading processes
    - Implement asynchronous operations where possible to improve performance

Made various improvements to Custom AI Path ALgined to surface, unsure how I feel on these. May or may not be helping. SOme bugs in behaviour of this script need to be fixed (how an error is handled)

Added GPU Instancing to Ty Fast Ghost Shader

August 7

Some more optimizations

Also, working on FMOD 

Improving some sounds

Upgraded to 2.03 for new features that may be useful, like multiband, frequency sidechain, etc

Some issues with hard coded fmod events pointing to the wrong things, fixing this. 

Removing firing blasts as it seems unecessary

- Enemy Lock on sound is not apparent enough, need something better
    - Improve with CLAP but maybe could be better

- Enemy Death creating stuck time rewind / pause - need to fix this
    - reverted fmod inspector assignment changes because something is breaking this, and i dont want to look into it too deeply
    - Seemed to have fixed this i think… monitoring

Is this missing from Crosshair and needs to be added again?

Under LockedList.RemoveAt(i);

```jsx
 // Instantiate and animate the lock-on prefab for each projectile shot
                    if (lockOnPrefab != null)
                    {
                        GameObject lockOnInstance = Instantiate(lockOnPrefab, Reticle.transform);
                        lockOnInstance.SetActive(true);
                        lockOnInstance.transform.localPosition = Vector3.zero; // Center it on the Reticle
                        lockOnInstance.transform.localScale = Vector3.zero; // Set the initial scale to zero

                        // Assuming the prefab has a SpriteRenderer component
                        SpriteRenderer spriteRenderer = lockOnInstance.GetComponent<SpriteRenderer>();
                        if (spriteRenderer != null)
                        {
                            Color initialColor = spriteRenderer.color;
                            initialColor.a = 0f; // Set initial transparency to 0
                            spriteRenderer.color = initialColor;

                            // Animate transparency to fully visible and then back to not visible
                            spriteRenderer.DOFade(1f, 0.25f).OnComplete(() => spriteRenderer.DOFade(0f, 0.25f));
                        }

                        // Scale up and destroy
                        lockOnInstance.transform.DOScale(Vector3.one * initialScale, 0.5f).OnComplete(() => Destroy(lockOnInstance)); // Scale up
                    }
```

Think i took care of that

FINALLL split up Crosshair to 3 classes

Work to be done but its mostly working!

---

## From: August 8.md

# August 8

COnversion of Crosshair to three speerate scripts seems to mostly work!
Montioring…..

Need to also monitor previous error of being stuck on reverse time

- Have not figured out exactly why this occurs

Adjusting see through material for enemies - making it less visible

Want chained enemy type in Section 3

Makeing them move in a snake like fasion…. unsure this can happen. Will require further deep experiments. Right now it might make the most sense to try something in line with what works right now. have the other parts spn around? use PrimeTween? 

setting them up as orbitals! testing

Seems the previous snake prefab works ok, need to figure out quick turning or other issues, but could potentially do something cool with this. 

Use both enemy type in different areas, play with these concepts

Types

- Snakes
- Orbitals

More ideas 

- Fractured - destroying it breaks into multiple smaller enemies - or projectiles! That are flynig towards the player to hit them.
- Tesselated Wall - A large enemy composed of many interlocking polyhedrons that form a wall or barrier. As the player attacks, the polyhedrons detach and become independent attackers.
- Elastic - think vertices - A shape that stretches and morphs its shape, making it difficult to predict its movement. It can extend its sides to form spikes or turn into a flat sheet to dodge attacks. Also maybe these vertices are the only lock on poitns?
- Mines - these need to be taken out before the player hits them and gets hurt
- Phased movement - disappear and reappear in different spots
- Large guardians - maybe static in their positon and slow to move, but deal big damage
- Reflective shields - attacks boucne off them at differnet times. this could be the time ability in action or just a timed shield based on the music
- Cloaked Stalkers: move between invisible and visible! radar comes in handy for this especially

---

## From: Boss Info.md

# Boss Info

---

## From: Build Optimization.md

# Build Optimization

1. Shader Variant Reduction:
- Current shader stripping: 27.52% (50,718/184,286 variants)
- Target: Increase stripping to >50% by:
a) Removing unused features
b) Consolidating similar shaders
c) Using shader_feature instead of multi_compile
1. Fix Shader Safety Issues:
- Add abs() for pow() operations in:
    - SineVFX/GalaxyMaterials
    - Ciconia Studio/CS_Ghost
    - Shader Graphs/Transparent-ReceiveFullFog-Shader
1. Update Deprecated Paths:
- Replace ScriptableRenderer.cameraColorTargetHandle with Render Graph API
- Update ReAllocateIfNeeded calls to ReAllocateHandleIfNeeded

```csharp
// 1. Replace FindObjectOfType calls with cached references
private void Awake()
{
  // Cache at startup instead of searching repeatedly
  _cachedManager = FindFirstObjectByType<GameManager>();
}

// 2. Use FindObjectsByType with better performance
var objects = FindObjectsByType<Enemy>(FindObjectsSortMode.None);
```

Fix

Fix required for animation transition:
'Standing Run Forward -> Running Jump' in 'Joostman Controller'

- Add either an Exit Time or condition to prevent transition being ignored

---

## From: Code Documentation.md

# Code Documentation

Advice on Scene Hierarchy

- GlobalEffects
    - Camera Target Group
    - Camera
    - CM StateDrivenCamera
    - Global Volume
    - Lighting
        - Directional Light
        - SkyLight
- Player
    - PlayerPlane
    - Horizontal Rotate
    - Shooting
        - Reticle
        - Projectile Locks
        - Enemy Locks
        - UI Crosshair
- Enemies
    - Enemy Plane
    - Enemy Spawn Controller
    - Enemy Bullet Pool
- Level
    - Mobius Tubes
    - Transition Points
- Environment
    - VFX_InkBlots_Burst_Big
- Testing
    - Eye Portal 1
    - Neutron Stars
- Tools
    - Game Manager
    - Projectile Manager
    - Input System
- FX
    - Rewind Particles
    - Temporal Blast
    - VFX Graphs

---

## From: Codebase Restructure.md

# Codebase Restructure

Scriptable Object Architecture (Primary Recommendation):

- Scriptable Object Architecture (Primary Recommendation):
- Use ScriptableObjects as data containers and event channels
- Decouples systems while maintaining Unity's component-based design
- Great for runtime configuration
- Excellent for designer workflows
- Better memory management than pure service locator

Event System (Secondary Layer):

- Build on top of your existing EventManager
- Decouple systems through events rather than direct references
- Better for runtime communication between systems

Minimal Service Location (Tertiary Layer):

- Use for truly global systems (Audio, Input, Save System)
- Implement through a simple service container

Assets/_Scripts/
├── Core/
│   ├── Bootstrap/                    # Initialization and scene loading
│   │   ├── GameBootstrapper.cs
│   │   └── SceneLoader.cs
│   ├── Management/                   # Global managers
│   │   ├── TimeManager.cs           # Your existing time control
│   │   ├── EventManager.cs          # Keep your current event system
│   │   └── UpdateManager.cs         # New: Custom update manager
│   └── Services/                    # Global services
│       ├── AudioService.cs
│       └── InputService.cs
├── Data/
│   ├── ScriptableObjects/          # Configuration data
│   │   ├── Projectiles/
│   │   │   ├── StandardProjectile.asset
│   │   │   └── BossProjectile.asset
│   │   ├── Enemies/
│   │   │   └── EnemyTypes/
│   │   └── Player/
│   │       └── PlayerStats.asset
│   └── Runtime/                    # Runtime data containers
│       ├── GameState.cs
│       └── WaveState.cs
├── Gameplay/
│   ├── Player/
│   │   ├── Logic/                  # Separate logic from visuals
│   │   │   ├── PlayerController.cs
│   │   │   └── PlayerCombat.cs
│   │   └── Visual/
│   │       └── PlayerAnimator.cs
│   ├── Projectiles/
│   │   ├── Core/
│   │   │   ├── ProjectilePool.cs   # Object pooling
│   │   │   └── ProjectileManager.cs
│   │   └── States/
│   │       ├── BaseProjectileState.cs
│   │       └── ProjectileStates/
│   └── Enemy/
│       ├── AI/
│       └── Combat/
└── UI/
├── HUD/
└── Menus/

**Migration Strategy:**

- Create new folder structure
- Move existing files without changing them
- Gradually convert to ScriptableObjects starting with:
- ProjectileManager (high ref count)
- EnemyBasicSetup (multiple instances)
- PlayerConfig (frequently modified)
- Implement UpdateManager for heavy Update users
- Separate logic from visuals incrementally

This revised plan:

- Maintains existing EventManager
- Adds performance optimization with UpdateManager
- Separates logic from visuals
- Uses ScriptableObjects strategically
- Preserves current scene loading
- Reduces circular dependencies
- Improves memory management

**More detailed**

1. Create new folder structure
├── Core/
│ ├── Bootstrap/
│ ├── Management/
│ └── Services/
└── Data/
└── ScriptableObjects/
2. Move core managers
    - Move EventManager.cs to Core/Management/
    - Move TimeManager.cs to Core/Management/
    - Create UpdateManager.cs in Core/Management/
3. Create bootstrap system
    - Create GameBootstrapper.cs
    - Move scene loading logic to SceneLoader.cs

1. Create ScriptableObject configs
├── Data/ScriptableObjects/
│ ├── Projectiles/
│ │ └── ProjectileConfig.cs
│ ├── Enemies/
│ │ └── EnemyConfig.cs
│ └── Player/
│ └── PlayerConfig.cs
2. Convert existing MonoBehaviour configs
    - Start with ProjectileManager (high priority)
    - Convert enemy configurations
    - Convert player settings
3. Create runtime data containers
    - GameState.cs
    - WaveState.cs

Reorganize player systems
├── Gameplay/Player/
│   ├── Logic/
│   │   ├── PlayerController.cs
│   │   └── PlayerInput.cs
│   └── Visual/
│       └── PlayerAnimator.cs

1. Refactor projectile system
├── Gameplay/Projectiles/
│ ├── Core/
│ │ ├── ProjectilePool.cs
│ │ └── ProjectileManager.cs
│ └── States/
│ └── ProjectileState.cs
2. Update enemy systems
├── Gameplay/Enemy/
│ ├── AI/
│ └── Combat/

1. Update event subscriptions
    - Replace string events with constants
    - Implement UnsubscribeFromAllEvents
2. Implement UpdateManager integration
    - Convert heavy Update users
    - Profile performance improvements
3. Test and optimize
    - Profile memory usage
    - Test scene transitions
    - Verify event system

---

## From: Data Analysis.md

# Data Analysis

Only up to April 2023 According to Cursor, suspect of this but making note

Okay, after thoroughly reviewing all the files provided, here is a breakdown of the key aspects of the project that have presented the biggest challenges, recurring issues, and the most significant changes over time:

1. Player Movement and Camera Control:

- This seems to be one of the most persistent and challenging areas of the project. The documentation mentions numerous issues and iterations related to the player's movement, rotation, and the camera behavior.
- Problems include the player character randomly spinning, camera jerking or not rotating properly, and difficulties in clamping the camera movement and reticle position.
- The team tried various approaches, such as using A *pathfinding, Curvy Spline integration, and custom scripts, but continued to encounter issues that required ongoing adjustments and refinement.*

2. Enemy AI and Pathfinding:

- Implementing robust and reliable enemy AI, including pathfinding, movement patterns, and targeting, has been a significant challenge throughout the project's development.
- The team experimented with different systems, including Behavior Designer, Sensor Toolkit, and A *pathfinding, but faced problems with enemies getting stuck, not moving correctly, or not properly engaging the player.*
- Integrating the enemy AI with the level design and wave spawning system also proved to be an ongoing challenge.

3. Bullet and Projectile Behavior:

- The behavior and interaction of the game's projectiles, including bullets, missiles, and other ranged attacks, have been a recurring issue.
- Problems include bullets colliding with each other, not moving in the intended direction, and not properly interacting with or damaging enemies.
- The team tried various approaches, such as adjusting bullet speeds, implementing homing mechanics, and experimenting with different collision and damage systems, but continued to face challenges in this area.

4. Audio and Music Integration:

- Integrating the game's audio, including sound effects, music, and audio-based gameplay mechanics, has been an ongoing challenge.
- The team explored using tools like Koreographer and Master Audio, but encountered issues with audio timing, synchronization, and overall integration with the gameplay.
- Maintaining a cohesive and responsive audio experience that enhances the player's engagement seems to have been a persistent challenge.

5. Visual Effects and Rendering:

- The game's visual style, including lighting, shaders, and special effects, has gone through multiple iterations and adjustments.
- The team experimented with various rendering pipelines, such as the Universal Render Pipeline (URP) and custom shaders, but faced issues with performance, visual quality, and maintaining a consistent aesthetic.
- Integrating advanced visual effects, like distortion, glitching, and particle-based elements, also presented ongoing challenges.

6. Level Design and Optimization:

- Designing engaging and cohesive levels, while also ensuring optimal performance, has been a significant challenge.
- The team explored techniques like mesh combining, LOD generation, and GPU Instancing to improve performance, but continued to face issues with large, complex scenes.
- Balancing the level design, enemy placement, and overall gameplay flow seems to have been an iterative process.

Throughout the documentation, these areas appear to be the most persistent challenges, with the team constantly revisiting and refining their approaches to address the various issues that arose. The documentation highlights the team's dedication to iterating on these core aspects of the project, as they strive to create a polished and engaging gameplay experience.

## **Advice**

Based on the recurring challenges identified in the project documentation, here are some suggestions and advice that I would provide:

For the Programmer:1.

**Modularize and Decouple Systems**

: The codebase would likely benefit from a more modular and decoupled architecture. This would allow individual systems, like player movement, enemy AI, and projectile behavior, to be developed, tested, and iterated on more independently. Identify clear boundaries and interfaces between these core systems.2.

**Implement Robust Testing**

: Establish a comprehensive suite of automated tests, including unit tests, integration tests, and end-to-end tests. This will help catch regressions and ensure that changes in one area don't inadvertently break other parts of the system.3.

**Leverage Design Patterns**

: Explore and apply well-established design patterns, such as the Observer pattern, State pattern, or Flyweight pattern, to address some of the recurring challenges. These patterns can help improve code organization, flexibility, and maintainability.4.

**Optimize for Performance**

: Continuously profile the codebase and identify performance bottlenecks. Utilize Unity's profiling tools and consider techniques like object pooling, batching, and asynchronous processing to improve overall performance, especially in areas like enemy AI and visual effects.5.

**Improve Debugging and Logging**

: Implement robust logging and debugging mechanisms to better understand the runtime behavior of the systems. This can help identify and reproduce issues more effectively, especially for complex interactions between different components.6.

**Refactor and Simplify**

: Regularly review the codebase and identify opportunities for refactoring. Look for areas where the code can be simplified, duplicated logic can be eliminated, and overall complexity can be reduced. This can improve maintainability and make it easier to address recurring issues.For Game Design and Project Management:1.

**Prioritize and Iterate**

: Establish a clear prioritization process to focus on the most critical and impactful features and issues. Adopt an iterative development approach, where you can quickly test and validate ideas before investing significant time and resources.2.

**Improve Feedback Loops**

: Establish regular playtesting sessions and gather feedback from both internal and external stakeholders. Use this feedback to inform design decisions and identify areas that require further refinement.3.

**Leverage Prototyping**

: Encourage the team to explore and experiment with different ideas through rapid prototyping. This can help validate concepts, identify potential issues, and inform the overall design direction before investing heavily in implementation.4.

**Enhance Documentation and Knowledge Sharing**

: Ensure that the project's documentation is comprehensive and up-to-date. This will help new team members quickly understand the codebase and the rationale behind design decisions. Consider implementing knowledge-sharing practices, such as regular team discussions or code reviews.5.

**Adopt a Flexible Mindset**

: Embrace the iterative nature of game development and be open to making changes and adjustments as needed. Avoid getting too attached to specific solutions or features, and be willing to explore alternative approaches when faced with recurring challenges.By addressing these suggestions, the programming team can work towards a more robust, maintainable, and flexible codebase, while the game design and project management efforts can help ensure that the team is focused on the right priorities and iterating effectively on the core gameplay and user experience.

---

## From: Dec 11 Build Optimization.md

# Build Optimization

1. Shader Variant Reduction:
- Current shader stripping: 27.52% (50,718/184,286 variants)
- Target: Increase stripping to >50% by:
a) Removing unused features
b) Consolidating similar shaders
c) Using shader_feature instead of multi_compile
1. Fix Shader Safety Issues:
- Add abs() for pow() operations in:
    - SineVFX/GalaxyMaterials
    - Ciconia Studio/CS_Ghost
    - Shader Graphs/Transparent-ReceiveFullFog-Shader
1. Update Deprecated Paths:
- Replace ScriptableRenderer.cameraColorTargetHandle with Render Graph API
- Update ReAllocateIfNeeded calls to ReAllocateHandleIfNeeded

```csharp
// 1. Replace FindObjectOfType calls with cached references
private void Awake()
{
  // Cache at startup instead of searching repeatedly
  _cachedManager = FindFirstObjectByType<GameManager>();
}

// 2. Use FindObjectsByType with better performance
var objects = FindObjectsByType<Enemy>(FindObjectsSortMode.None);
```

Fix

Fix required for animation transition:
'Standing Run Forward -> Running Jump' in 'Joostman Controller'

- Add either an Exit Time or condition to prevent transition being ignored

---

## From: Dec 11.md

# Dec 11

[Featured Blog | How to create a 'fair' auto-aiming system in a robot shooter](https://www.gamedeveloper.com/design/how-to-create-a-fair-auto-aiming-system-in-a-robot-shooter-)

Idea- Shrink and grow cursor to indicate target locking 

Change Reticle over to a system based on Shapes asset pack

Some parts done, working on proejcitle and enemy lock on aspects / animations 

- some difficulties implementing the same shriking box again

Need proper effect for ricochet - blast no longer works?

- may also need to revert to earleir version of how this works - verify
- need adjustments to this in general

Should i just have all projectiles aim for the direction the player is currently facing? will this mean less mvoement? is this worth playnig with?

[Build Optimization](Dec%2011%20Build%20Optimization.md)

---

## From: Dec 18 19 Projectile System Changes.md

# Projectile System Changes

### **Major Changes Made**

1. **Projectile System Architecture**:
    - Centralized movement logic in **`ProjectileMovement.cs`**
    - Removed duplicate homing logic from **`ProjectileStateBased.cs`**
    - Introduced proper state management and registration for homing projectiles
2. **Chronos Integration**:
    - Fixed timeline/clock integration
    - Ensured projectiles use the correct "Test" global clock
    - Properly handle time scaling in movement calculations
3. **Homing Behavior Improvements**:
    - Added virtual target system for accuracy-based targeting
    - Implemented adaptive rotation speeds
    - More frequent target position updates
    - Better accuracy implementation using offset calculations

### **Performance Analysis**

**More Performant**:

- ✅ Reduced duplicate calculations by centralizing movement logic
- ✅ Better memory management with object pooling
- ✅ Optimized grid updates with position thresholds
- ✅ Batch processing for projectile updates

**Less Performant**:

- ❌ More frequent target updates (now every frame)
- ❌ Additional GameObject creation for virtual targets
- ❌ More complex rotation calculations

### **Areas for Further Improvement**

1. **Virtual Target Optimization**:
    
    ```
    
    - Replace GameObject-based virtual targets with a simple Vector3 position
    - Use a struct-based target system to avoid garbage collection
    
    ```
    
2. **Spatial Partitioning**:
    
    ```
    
    - Implement distance-based update frequencies more aggressively
    - Only update targets for projectiles within relevant range
    
    ```
    
3. **Job System Integration**:
    
    ```
    
    - Move target position calculations to the job system
    - Batch process rotation calculations
    - Parallelize accuracy offset calculations
    
    ```
    
4. **Memory Management**:
    
    ```
    
    - Pool transform components for virtual targets
    - Implement object pooling for all projectile effects
    - Use non-allocating physics queries
    
    ```
    
5. **Update Optimization**:
    
    ```
    
    - Add dynamic update frequency based on projectile density
    - Implement priority queue for critical projectiles
    - Add LOD system for distant projectiles
    
    ```
    
### **Current State Assessment**

**Working Well**:

- ✅ Homing behavior is more accurate and responsive
- ✅ Better integration with time system
- ✅ More predictable projectile behavior
- ✅ Proper accuracy implementation

**Could Be Better**:

- ⚠️ Memory allocation from virtual targets
- ⚠️ Update frequency could be more optimized
- ⚠️ Job system could be better utilized
- ⚠️ Some calculations could be batched

Would you like me to focus on implementing any of these suggested improvements? The most impactful would probably be replacing the GameObject-based virtual targets with a simpler system and implementing better spatial partitioning for updates.

---

## From: Dec 18 19.md

# Dec 18/19

May try to enact this struct or enum based approach to projectiles - need to fix version control first

fix homing for enemy shot projectiles - not homing properly

Mostly working - summary of changes we implemented

[Projectile System Changes](Dec%2018%2019%20Projectile%20System%20Changes.md)

Debug scripts

- Pathfinding debugger for testing alternate A* values on enemies and such
- Projecitle Pool Debugger for chekicng for thrashing (large pool size changes)

Moving movement system to jobs, some considerations

**Steps to Implement Chronos-Compatible Job Optimizations:**

1. **Identify Chronos-Controlled Logic:** Determine which parts of your projectile behavior are directly influenced by Chronos (movement, lifetime, etc.).
2. **Access the Relevant Clock:** In your ProjectileManager or wherever you schedule your jobs, get the Clock instance associated with each projectile.
3. **Pass Time Scale to Jobs:** Include the clock.timeScale as a parameter in the data you pass to your Job structs. Also, pass UnityEngine.Time.unscaledDeltaTime (or Time.fixedUnscaledDeltaTime if it's a fixed update job).
4. **Apply Time Scale in Jobs:** Multiply the base delta time by the passed timeScale within the Execute() method of your jobs.
5. **Test Thoroughly with Time Manipulation:** Test your projectile behavior with different Chronos time scales (slow motion, fast forward, pausing) to ensure everything works as expected.

Might have finally got jobs working for projectile movement - debugging needed

---

## From: Dec 21.md

# Dec 21

[Google Thinking _Scripts Reccommendations](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Google%20Thinking%20_Scripts%20Reccommendations.md)

Sorted some build issues, but need to rebuild render layers and assess, get it all working properly again

something bad with vsync logic on load, fix

Things to investigate editor vs build code

1. **Subtle Code Issues Exposed in Build:**
    - **Unintentional Boxing:** Operations that cause value types to be converted to reference types (boxing) can be more prevalent or noticeable in a built player due to optimizations.
    - **String Concatenation:** Excessive string concatenation within loops or frequently called functions can create a lot of temporary string objects, leading to GC pressure. This might be less noticeable in the editor's more forgiving environment.
    - **LINQ on Value Types:** Using LINQ queries on collections of value types can often lead to boxing and allocations, especially if not carefully optimized.
    - **Closures and Anonymous Delegates:** While convenient, closures can sometimes lead to unexpected object allocations if not managed carefully.

---

## From: Dec 26.md

# Dec. 26

**Major Changes:**

**Shader Modifications:**

- Extensive changes to the Shapes package shaders, including modifications to various blend modes (Additive, ColorBurn, ColorDodge, etc.) for different shape types (Cone, Cuboid, Disc, Line 2D/3D, etc.).
- Updates to core shader files like Shapes Config.cginc, Shapes Math.cginc, and Shapes.cginc.

**Scene and Lighting Changes:**

- Deletion of multiple lightmap and reflection probe files from the "Ouroboros 19" and "Ouroboros 22" scenes.
- Modifications to scene files:
    - Ouroboros - Base.unity
    - Ouroboros - Scene 1.unity

**Script Updates:**

- Modified core gameplay scripts:
    - PlayerMovement.cs
    - StaticEnemyShooting.cs
    - ParticleSystemManager.cs
    - MotionExtractionBaseEffect.cs

**Configuration Updates:**

- Changes to wave configuration assets:
    - Multiple "WaveConfig" scriptable objects for Ophanim and Ouroboros.
- Modified project settings:
    - EditorBuildSettings.asset
    - GraphicsSettings.asset
    - ProjectSettings.asset
    - QualitySettings.asset
    - URPProjectSettings.asset

**New Files Added:**

- New shader analysis tools:
    - ShaderAnalyzer.cs
- New gameplay features:
    - FSRSettingsApplier.cs
    - FSRPreferencesData.cs

**Asset Package Updates:**

- Changes to various third-party packages including:
    - FlatKit
    - Quibli
    - SensorToolkit
    - TransitionsPlus

**Impact Analysis:**

**Graphics and Performance:**

- The shader modifications suggest optimization work or visual improvements.
- FSR (FidelityFX Super Resolution) integration indicates focus on performance optimization.
- Changes to quality and graphics settings suggest overall graphics pipeline adjustments.

**Gameplay:**

- Wave configuration changes indicate balancing or content updates.
- Player movement and enemy shooting modifications suggest gameplay refinements.
- Particle system changes might affect visual feedback and game feel.

**Level Design:**

- Removal of old lightmap data and addition of new lighting configurations suggests level visual improvements.
- Scene modifications indicate level design iterations.

**Technical Infrastructure:**

- New shader analysis tools suggest focus on optimization and performance monitoring.
- Project settings changes indicate engine-level configurations being tuned.

The changes appear to be focused on optimization, visual improvements, and gameplay refinements, with a particular emphasis on shader and graphics pipeline updates.

**Specific Script Updates:**

**PlayerMovement.cs Changes:**

- Added new fields for rotation cooldown and duration (lines 48-51).
- Modified dodge mechanics with cooldowns and effects (lines 55-61).
- Added new direction reversal method (lines 618-630).

**Impact:** These changes affect player movement mechanics and control responsiveness.

**StaticEnemyShooting.cs Changes:**

- Implemented a structured shooting system with:
    - Configurable shooting parameters (speed, lifetime, scale, damage).
    - Shooting request structure for position/direction/speed.
    - Cooldown system with a minimum shot interval (0.1s).
    - Integration with EnemyManager for registration/unregistration.
    - Performance optimizations for projectile spawning.

**Impact:** More controlled and optimized enemy shooting behavior.

**ParticleSystemManager.cs Changes:**

- Added comprehensive particle system monitoring and optimization:
    - Performance metrics tracking (particle count, emission rate, etc.).
    - Warning system for high-impact features.
    - Auto-refresh functionality for real-time monitoring.
    - Debug visualization tools.

**Impact:** Better performance monitoring and optimization of particle effects.

**Project Settings Changes:**

**GraphicsSettings.asset:**

- Updated shader configurations.
- Modified URP (Universal Render Pipeline) settings.
- Added new preloaded shaders.

**Impact:** Enhanced graphics rendering and performance.

**ProjectSettings.asset:**

- Updated scripting define symbols for different platforms.
- Modified build settings and platform configurations.
- Added new input system settings.

**Impact:** Platform-specific optimizations and input handling improvements.

**QualitySettings.asset:**

- Modified PC Default quality preset:
    - Adjusted shadow settings.
    - Updated texture streaming parameters.
    - Modified LOD settings.

**Impact:** Better balance between visual quality and performance.

**URPProjectSettings.asset:**

- Updated to material version 9.

**Impact:** Compatibility with latest URP features.

**Key Impact Summary:**

**Performance Optimizations:**

- Improved particle system management.
- Enhanced shooting mechanics efficiency.
- Better graphics settings optimization.

**Gameplay Mechanics:**

- Enhanced player movement control.
- More refined enemy shooting behavior.
- Better dodge and rotation mechanics.

**Visual Improvements:**

- Updated shader and graphics configurations.
- Optimized particle effects.
- Better quality settings for the PC platform.

**Technical Infrastructure:**

- Updated build configurations.
- Enhanced platform support.
- Improved input system integration.

The changes represent a significant update focusing on performance optimization, gameplay refinement, and visual improvements while maintaining compatibility across platforms.

---

## From: Dec 4.md

# Dec 4

Have made several major optimizations over the past few days

Need to test gameplay but seems functional so far!

- Ricochet may not be working - need to verify
    - Is it the spatial optimziation system messing this up?

Also need to optimize rendering - this is the bottleneck now 

Made a texture analyser - use to delete unused textures. 

Player Movement split to Player Movement and Player Rotation

Bug test this. Also putting ricochet dodge improvements/fixing within

- ricochet dodge implemented and needs testing

Turning Enemies into more managable structure of EnemyBase and EnemyBehavior

Review structure and implement in game

Ideas for making the ricochet effect easier to use
- Implement a "bullet time" slow-down effect when projectiles get within a certain range
- Use a subtle glow/bloom effect that increases as projectiles get closer

- Add leading lines or subtle visual guides that appear briefly when projectiles spawn
- Add a brief time-freeze frame when projectiles first spawn
- Add a "danger zone" visualization that shows where projectiles will be in the next few frames
• Include a brief warning period before projectiles start moving

References

[Ricochet Refs](Beat%20Game%20Notion/Learning%20and%20Research/Ricochet%20Research/Ricochet%20Refs.md)

For Rendering optimization, try windsurf with CatlikeCoding docs, see what suggestions we can implement in our case. what would majorly improve performance - look at frame debugger for this as well

May also be a good idea to debloat project at this point - can import new assets if needed, keep some of the old ones around for future level development

---

## From: Degenerate Geometry A Error.md

# Degenerate Geometry A* Error

Exception: Caught in a potentially infinite loop. The navmesh probably contains degenerate geometry.
Pathfinding.PathTracer.IsInnerVertexTriangleMesh (Pathfinding.Collections.CircularBuffer`1[T] nodes, Pathfinding.Funnel+PathPart part, System.Int32 portalIndex, System.Boolean rightSide, System.Collections.Generic.List`1[T] alternativeNodes, Pathfinding.NNConstraint nnConstraint, System.Int32& startIndex, System.Int32& endIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1278)
Pathfinding.PathTracer.IsInnerVertex (Pathfinding.Collections.CircularBuffer`1[T] nodes, Pathfinding.Funnel+PathPart part, System.Int32 portalIndex, System.Boolean rightSide, System.Collections.Generic.List`1[T] alternativeNodes, Pathfinding.NNConstraint nnConstraint, System.Int32& startIndex, System.Int32& endIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1166)
Pathfinding.PathTracer.FirstInnerVertex (Unity.Collections.NativeArray`1[T] indices, System.Int32 numCorners, System.Collections.Generic.List`1[T] alternativePath, System.Int32& alternativeStartIndex, System.Int32& alternativeEndIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1296)
Pathfinding.PathTracer.GetNextCornerIndices (Unity.Collections.NativeArray`1[System.Int32]& buffer, System.Int32 maxCorners, Unity.Collections.Allocator allocator, System.Boolean& lastCorner, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1406) Pathfinding.ECS.JobRepairPath.Execute (Unity.Transforms.LocalTransform& transform, Pathfinding.ECS.MovementState& state, Pathfinding.ECS.AgentCylinderShape& shape, Pathfinding.ECS.AgentMovementPlane& movementPlane, Pathfinding.ECS.DestinationPoint& destination, Unity.Entities.EnabledRefRW`1[T] readyToTraverseOffMeshLink, Pathfinding.ECS.ManagedState managedState, Pathfinding.ECS.MovementSettings& settings, Unity.Collections.NativeList`1[T] nextCornersScratch, Unity.Collections.NativeArray`1[System.Int32]& indicesScratch, Unity.Collections.Allocator allocator, System.Boolean onlyApplyPendingPaths) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Core/ECS/Jobs/JobRepairPath.cs:182)
Pathfinding.ECS.JobRepairPath.Execute (Unity.Entities.ArchetypeChunk& chunk, System.Int32 unfilteredChunkIndex, System.Boolean useEnabledMask, Unity.Burst.Intrinsics.v128& chunkEnabledMask) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Core/ECS/Jobs/JobRepairPath.cs:123)
Pathfinding.ECS.JobRepairPath.Unity.Entities.IJobChunk.Execute (Unity.Entities.ArchetypeChunk& chunk, System.Int32 unfilteredChunkIndex, System.Boolean useEnabledMask, Unity.Burst.Intrinsics.v128& chunkEnabledMask) (at <9d3e9f9e6b8946f9abda6538b875606e>:0)
Unity.Entities.JobChunkExtensions+JobChunkProducer`1[T].ExecuteInternal (Unity.Entities.JobChunkExtensions+JobChunkWrapper`1[T]& jobWrapper, System.IntPtr bufferRangePatchData, Unity.Jobs.LowLevel.Unsafe.JobRanges& ranges, System.Int32 jobIndex) (at ./Library/PackageCache/com.unity.entities@1.3.2/Unity.Entities/IJobChunk.cs:420)
Unity.Entities.JobChunkExtensions+JobChunkProducer`1[T].Execute (Unity.Entities.JobChunkExtensions+JobChunkWrapper`1[T]& jobWrapper, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, Unity.Jobs.LowLevel.Unsafe.JobRanges& ranges, System.Int32 jobIndex) (at ./Library/PackageCache/com.unity.entities@1.3.2/Unity.Entities/IJobChunk.cs:363)

---

## From: Dots Notes.md

# Dots Notes

Converting a traditional Unity MonoBehaviour system like ProjectileStateBased.cs to DOTS involves several steps and considerations. From the provided code snippets, you've made a significant start in transitioning to DOTS. However, there are a few areas that need attention to ensure the systems are properly linked and functional. Here's an overview of potential gaps and suggestions for improvement:

### 1. **Component Registration and Entity Conversion**

Ensure that all necessary components are registered with the EntityManager and that your GameObjects are being converted to entities correctly. This typically involves using a GameObjectConversionSystem or the ConvertToEntity component in the Editor. It seems you have an initialization system 1, but make sure it's correctly converting your GameObjects and assigning initial component values.

### 2. **State Management**

Your original ProjectileStateBased.cs script uses a state pattern for managing projectile behavior. In DOTS, state management can be achieved by adding or removing components to represent different states. It looks like you've started this with ProjectileDotsStateTransitionSystem, but ensure that:

- Each state (e.g., homing, launched) has a corresponding component.
- Systems check for these components to determine behavior.
- State transitions are handled by adding/removing these components based on game logic.

### 3. **Collision Handling**

Your ProjectileDotsCollisionSystem is set up to handle collisions, but the actual collision handling logic (e.g., responding to hits, applying damage) needs to be implemented. Use the CollisionEvent system in DOTS to detect and respond to collisions between entities.

### 4. **Event Systems**

If your original system uses events (e.g., OnDeath, OnTriggerEnter), you'll need to adapt this to DOTS. This can involve creating specific systems for these events or using shared components to flag entities for processing by other systems.

### 5. **External Libraries and Features**

Your original script references several external libraries and Unity-specific features (e.g., DOTween, Koreographer). Integrating these with DOTS can be challenging, as DOTS operates on a different paradigm. You'll need to find equivalent DOTS-compatible solutions or create custom systems to achieve similar functionality.

### 6. **Linking Systems Together**

To ensure your systems work together:

- Use UpdateInGroup and UpdateBefore/UpdateAfter attributes to control the execution order of your systems.
- Make sure systems that depend on the output of other systems are reading the correct components and that those components are being updated as expected.

### 7. **Debugging and Validation**

- Use the Entity Debugger to inspect your entities and their components at runtime. This can help identify missing components or unexpected state configurations.
- Validate that your systems are being executed and that entities are transitioning through states as expected.

### 8. **Performance Considerations**

- Utilize the Burst compiler for your systems where possible to improve performance.
- Consider the impact of your systems on performance, especially those running every frame. Use ScheduleParallel for parallel execution where possible.

### Final Steps:

- 

**Integration Testing**

: Once all systems are in place, thorough testing is crucial to ensure that the behavior matches the original MonoBehaviour system and that performance meets your requirements.

- 

**Optimization**

: After confirming functionality, profile your game to identify and address any performance bottlenecks.

This conversion process is complex and requires a deep understanding of both the original system and DOTS. The transition to DOTS offers significant performance benefits but may require rethinking certain approaches to fit the data-oriented design.

---

## From: Feb 11th.md

# Feb 11th

Adjusting Spline Manager so that speed scale is not so arbitray a number

Currently many of them re 0.02 or 0.08, etc

Now features a multiplier

Adjusted Snakes teleports so they use a dissolve effect, may want to adjust shader for galaxy texture, combine with other shaders, etc

Deep dive on PSB again. locking things into states, trying to fix bugs.

Some things definitely clearer, but bullets arent shooting properly from player

regardless of enemy locked or not - need to trace this and fix it up

---

## From: Feb 13.md

# Feb. 13

Still working on PSB

Also updating several assets

- A* looks to have massive improvements so updated to *.95
- Curvy Spline has a newer version, hoping it fixes my issues with gizmos not showing up for the rotations

Player Movement, or other script, seem to be causing the player object to rotate gradually, spinning around on the y axis. still unsure why. updated scripts to fix this but doesnt seem like it did anything

Hyperpassive as a rubric for the music and interactivity in Beat Remake

[https://www.youtube.com/watch?v=S4pHJN8YclE](https://www.youtube.com/watch?v=S4pHJN8YclE)

[https://www.youtube.com/watch?v=woLgWVZPMk4](https://www.youtube.com/watch?v=woLgWVZPMk4)

[https://www.youtube.com/watch?v=osGJGqDDby8](https://www.youtube.com/watch?v=osGJGqDDby8)

[https://www.youtube.com/watch?v=ZytOQ4NSciU](https://www.youtube.com/watch?v=ZytOQ4NSciU)

[Spatial Communication in Level Design](https://www.youtube.com/watch?v=AKeUZVikPV8)

[Unity VFX Graph：Ribbons and Balls (Event control)](https://www.youtube.com/watch?v=h9ApA9tHiqk&list=WL&index=12)

[SIMPLE Tip For Better Unity Game Architecture](https://www.youtube.com/watch?v=mAfpfUYhpAs)

[Practical generators for blender](https://www.youtube.com/watch?v=wBT0GGui75I)

[3 Game Programming Patterns WE ACTUALLY NEED.](https://www.youtube.com/watch?v=BwA36em_DnA&list=WL)

In game settings

[https://github.com/xZenvin/UnitySettingsFramework](https://github.com/xZenvin/UnitySettingsFramework)?

---

## From: Feb 1st.md

# Feb 1st

Useful for Ophanim

[Ring World Using Random Flow](https://www.youtube.com/watch?v=YHkhEyh5G68)

Also good blender editing tool possibly 0 Quad Remesher - already installed

[Quad Remesher add-on | Blender Secrets](https://www.youtube.com/watch?v=gCj6dTNhO_c)

Look into Quadriflow Remesh as well?

[Unity VFX Graph：Use SDF to make model particle effects](https://www.youtube.com/watch?v=FBP9k6W48vM&t=905s)

Model for how I implemented SDF particle effects - May need as reference

[https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)

Game Design Resources full spreadsheet - lots of good links!

[SwiftRoll 🐦 – Medium](https://medium.com/@swiftroll3d)

Unity Optimization and Programming

[A multitude of Empty Slots (for Comissions) on Twitter / X](https://twitter.com/ZloNoNameSov/status/1716610855125799378)

Cool enemy character inspo - check out guys other stuff too!

[EXTREME PERFORMANCE with Unity DOTS! (ECS, Job System, Burst, Hybrid Game Objects)](https://www.youtube.com/watch?v=4ZYn9sR3btg)

Convert projectile system to DOTS?

How to spawn the DOTS projectiles

```jsx
using Unity.Entities;
using UnityEngine;

public class ProjectileSpawner : MonoBehaviour
{
    public GameObject projectilePrefab;

    void Start()
    {
        var settings = GameObjectConversionSettings.FromWorld(World.DefaultGameObjectInjectionWorld, null);
        var projectileEntityPrefab = GameObjectConversionUtility.ConvertGameObjectHierarchy(projectilePrefab, settings);

        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;

        Entity projectileEntity = entityManager.Instantiate(projectileEntityPrefab);
        entityManager.SetComponentData(projectileEntity, new ProjectileData { bulletSpeed = 10f, lifetime = 5f, homing = true, launched = true });
    }
}
```

Notes on current state of conversion of projectiles to DOTS

[Dots Notes](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Dots%20Notes.md)

Trying some other optimizations beofre DOTS

[Projectile Manager Optimizations](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Projectile%20Manager%20Optimizations.md)

Singleton Manager - definitely seems to help!

---

## From: Feb 22.md

# Feb 22

Implemented hacky LookAt solution so player game object stops rotating 

Need a better long term solution but works for now 

Figured out orientation anchor issue with Curvy Spline

Its a feature in its custom menu that disabled. Re enable it not under gizmos but under the curvy spline menu in the scene view

Can finally fix orientation issues!

Started implementation of color picked based on enemy type shooting the projectile. will set the color parameter. not working, needs finalization.

Because of how it would globally affect a shared material, have decided against this. 

Doing a material swap instead. 

Need Kanban board or proper issue tracker for game bugs

---

## From: Feb 23.md

# Feb 23

Updated Amplify Shader Editor

Got this message

- A new URP version 14.0.10 was detected and new templates are being imported.
Please hit the Update button on your ASE canvas to recompile your shader under the newest version.

Need to investigate Leak issues

Leak Detected : Persistent allocates 4 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.

Working on Materials swap, not properly working currently. Leaving for now

Created Kanban board to be able to log and address issues easier

Working on Ouroboros level, trying to make it playable throughout

Mobius Tube 2 is pretty good

Mobius Tube 2 Variation has a solid start

Mobius Tube 7 has a solid start

Mobius Tube 6 needs a lot of work - no enemies? Maybe a bad structure

---

## From: Feb 24.md

# Feb 24

Looking at moving snake dolly stuff with curvy now

Curvy - Gravity towards spline example - maybe useful for character controller 

11 - Rigidbody

Also 21-CG Extrusion - Volume example

Also has a Tubes example which could be very useful

29 - CGDeform mesh looks to be applicable to Snakes that are moving

Snake Dolly doesn’t really work 

Trying Infinite track applied to game

Issues with getting the tail / head attributes to work properly, but fixable i think!

Definitely could be a cool idea for section of Ouroboros

Also could use as part of travelling inside of one

---

## From: Feb 25.md

# Feb 25

Added Snake head to infinite track, so it’s like you’re being chased by ouroboros

Cool idea! Needs refinement, liekly doesnt need to be on its own spline

Need a better snake mouth with some aniamtion to pull it off though

Take time to assess multiple snakes, try one with animation

- this also needs to be done for static ouroboros, assess the best snake heads for that

**Ouroboros**

Level Ideas/Strucutre

Several ouroboros figures

Back of snake - chased

- shoot something…. not sure what!

Interior Snake? 

- enemies inside running along interio?
- is this infinite or a structure?

Two headed snake boss

**Ophanim**

running along moving rings

- how could infinite track be applied here??

---

## From: Feb 27.md

# Feb 27

Looking at creatures

Found a snake that seems good, try applying new animator controls on it, open jaw, see what we can do for chasing player

Made animation edits on it with umodeler or uanim or whatever its called

 working okay!

Play / watch some sin & punishment for idea of attach phases here

- enemies move up the snake’s back?
- snake’s eyes do something?
-

---

## From: Feb 28.md

# Feb 28

Model / Animation pipeline adjustments

**Umotion Notes**

Editing Animations 

[(2) Editing Existing Animations - UMotion In Practice](https://www.youtube.com/watch?v=TPzCp6Ezy4o)

Uses Animation Layers - make adjustments without getting rid of original animation

- can use override to adjust original

Animation layers seem to be a simple quick way to make broad adjustments across base animation 

FK vs IK - read up on this - seems IK preferred

Investigate root motion

Seeing curve view of the animations is very useful - can find immediate discrepancies

Copy to other side tool - good for adjustment of mirror things!

Setting up a shortcut for this and enabling Generate allows for quick copying of animation frames

- Shown well in video!

Editing Mesh 

[How to edit mesh without editing rig?](https://www.reddit.com/r/Unity3D/comments/12tovbq/how_to_edit_mesh_without_editing_rig/)

Trying to edit mesh in Blender, Skinned Mesh proving troublesome

[Export Synty Characters from Unity to Blender and Back](https://www.youtube.com/watch?v=QLiseo8s45s)

Looking for tips from this 

- Adjust scale to 1 before export from unity

Scale tip helpful, but bones / rig is off from imported model (snake in this case)

Animation isnt applying to my model because  of a naming problem! the bones are being renamed with ‘__’ instead of ‘_ ‘

Made a tool called Protofactor Bone Renamer to address this within Unity.

Snake infinite track - charging attack that you have to hit enough times before it's dismissed

---

## From: Feb 29.md

# Feb 29

Series of animations for Snake head decided

Need UMotion editing

Can I apply layer on multiple animations?

![Untitled](Feb%2029%200b8ae405e1f74759a29f7edc7c23f434/Untitled.png)

Did many, seems rotation is off on Idle Open Mouth compared to the others

fixing… 90 in y?

Redid idle position to fix this, but need more work on animations cause it totally throws off positioning - should be able to fix in umotion with root or something like that i think?

maybe just needs to be set?

Tried this, but i think the problem is larger. scale of the object changes massively when play mode vs not. unsure why exactly

Seems like its issues with ROOT_ or above, deleting some of those attributes in the animation seems to have made a difference

Delete top level stuff, seems to have fixed issue. Also deleted tag frames at end, seemed unessecarry due to many animations being shorter than the 2 second place these were placed

They seem to be an error or something i did wrong

Made some updates to infinite track, working better now. but need a solution to gameplay style that is compelling. 

Maybe tail animator can have snakes that follows but move with the physics?

Maybe use player plane as a base and do more traditional rail shooter waves etc?

Why look back at snake? to shoot?

What is that system?

Same people who make animancer have a tail / physics animation system that seems quite good

May be useful for snake models

---

## From: Feb 5th.md

# Feb 5th

Catching up, actually did a lot the past couple days for restructuring bullets and making the whole game run more efficiently. 

IMPORTANT

to make things brighter / more emissive / stand out, HDR intensity needs to be adjusted along with color. Makes a big difference!

---

## From: Feb 6th.md

# Feb 6th

Added HBAO and changed Bakery settings for rendering

Not a real noticeable improvement, something to revisit later in optimizing pipeline

Added HDR color to Joost shader, looking into character being more visible. A good start!

Updated some of projectile logic but not fully working.

Can lock on and shoot but enemy’s not dying - why?

- suspect the bullets need to go faster and maybe go through walls?
- Maybe dont shoot from above, gather bullets at reticle? Shoot from reticle?

Have adjusted bullets to gather above the player 

- not working currently - not entirely sure why

Also getting errors related the shooting and the bullets being kinematic

the enemies should not need to apply force to the bullets - just instantiate them

ADJUST FOR THIS - the bullets should move on their own (i think!)

Birth of character room… ‘tutorial’ section…

use this effect?

[Recreating the Water Caustics Effect from Blade Runner 2049](https://www.youtube.com/watch?v=Oh21hYx_Jbk)

Adding animancer pro for better animation control in unity

Disabling the render for the projecitle when locked, this is because its now going to the reticle position and it was too bright, but could not get emissions to change, so just not rendering the model now.  A workable temporary fix. Need to re-enable this when shot because i cant currently see where bullets are even going lol Unlocking when lauchatenemy, but come back to this!! cant use this long term, i can see it causing issues 

Doesn’t seem like shooting is working though. Need to work through the whole process and understand what’s happening.

---

## From: Feb 8th.md

# Feb 8th

Turnign the player on and off fixes issues with the reticle/shooter movement

Its one of the scripts on the player object - maybe?

Investigate this!

Implemented Temporary basic fix

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled.png)

Also projectile not shooting from reticle - its in front but not moving on button release

Investigate this!

important

locked = determines whether a bullet will damage enemy when it hits it

must be true when shot

cleaning up the console a bit so i can use debugs easier

Found a bullet stuck to reticle, noticed this is happening

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%201.png)

Should be disabled, need to reassess how this is disabled so nothing gets stuck 

The eyeballs seem to stop shooting, need to find out why…. its the same reason! many of the bullets are stuck at this 

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%202.png)

![Untitled](Feb%208th%20c540c7ad7fae4a89bc6cea6ae44b86a3/Untitled%203.png)

A bunch of projectiles are staying locked, therefore lifetime is not decreasing and they’re not being pooled. they are not parented to reticle. Need to follow through the whole cycle of them to understand whats going wrong. 

They are static and regular enemy projectiles, so i think that part of it doesnt make a difference. 

I think this issue also explain why when i lock on some projectiles seem to disappear completey

I think they are going to this spot

May be able to go back to DOMove in PlayerShotState - need to reassess - the async nature of it may be the problem, but maybe not, may be offset?

Set the target to null - now projectiles remaining frozen in environment. I think this is a a sign of what’s happening but im not sure exactly what yet

chjanged things so launchback launches projectiles now, but its not holding on to them, so that needs to be addressed

---

## From: Game Name Ideas.md

# Game Name Ideas

Word I like

Resonate
Vibrants
Reflect
Objekt Rezonance
Sensate
Killing Sound
Heatsync
Function
Violens
Rendering
Cerpin Taxt
Hollo
Version
Noto
Vectorment
Strobe
Poly Grafik
Fold
Aux Machina
Channel
Decibel
Doppler
Pattern Recog
Deverb
Fidelity
Format
Chain Delay
Haas
Hum
Phase
I / O
Interface
Meter
Off/set
Overtone
Parallel
Preamp
Pulse Code
Pulse Width
Q Error
Reflections
Reso
RMS
Signal Flow
softCLIPPING
Subtrakt
Warp
Zero Crossing

Some song / band names I like for reference for naming my game
Garden of Delete
Hyperpassive
Replica
Something like Returnal ?
Hyperdawn
Shifting
Unfold
Repeater
Phase

Floating Points

Video game names I like
Citizen Sleeper
Thrasher
Tetris Effect
Armored Core Fires of Rubicon
Disco Elysium
Genesis Noir
Metal Slug
HeXen Death in Abyss
System Shock
Radiant Silvergun
Mars Matrix
Raw Metal
Hi-Fi Rush

Vibratic Abberation

Formant Shift

Phantom Center

Waveshaping

Glitch Vector

Thrashold

Repeater

Boundary Object

Recoil

"Revelate" is a made-up term derived from "Revelation," suggesting both the biblical book and a sudden, powerful action.

I / O

Infinity Oblivion

Osciliation

Interpolate / Obliterate

Interference / Operate

Obstacle

UltraVibrant

Floating Point

---

## From: Google Thinking _Scripts Reccommendations.md

# Google Thinking _Scripts Reccommendations

Here's an analysis with actionable tasks for an AI programming agent, focusing on ease and performance impact:

## Structural Patterns Identified

- **Base Class Usage:** The BaseBehaviour class demonstrates a good practice of using inheritance to provide common functionality (accessing the Chronos timeline).
- **Conditional Logic with Scriptable Objects:** ConditionalDebug leveraging a DebugSettings ScriptableObject is a solid pattern for managing environment-specific behavior.
- **Extension Methods:** AsyncOperationExtensions showcases the effective use of extension methods to add functionality to existing Unity classes.
- **Manager Classes:** Scripts like ProjectileManager, LevelLoader, and SceneManagerBTR indicate a clear separation of concerns for managing specific game systems.
- **Pooling:** You're actively using object pooling for projectiles and particle systems, which is excellent for performance.
- **Interface-Driven Design:** The IAttackAgent and IDamageable interfaces promote decoupling and allow for different implementations of these core functionalities.
- **State Machines (Potentially):** The presence of ProjectileStateBased, EnemyShotState, PlayerLockedState, and PlayerShotState suggests you might be using or planning to use state machines for controlling behavior.

**Should you adapt these patterns elsewhere?**

- **Base Class:** Yes, if you find other common functionalities that several MonoBehaviour scripts need, consider creating more base classes. Be mindful of over-inheritance, though.
- **Conditional Logic with Scriptable Objects:** Absolutely. This pattern is great for managing settings, configurations, and even A/B testing within your game without code changes.
- **Extension Methods:** Yes, continue using extension methods for adding utility to existing Unity or third-party classes.
- **Manager Classes:** Yes, maintain this pattern for centralizing control and logic for different game systems.
- **Pooling:** Definitely expand pooling to other frequently instantiated and destroyed objects like visual effects, UI elements, or even enemies if performance becomes an issue.
- **Interface-Driven Design:** Highly recommended. This will make your codebase more modular, testable, and easier to extend. Identify core interactions and define interfaces for them.
- **State Machines:** If you're comfortable with state machines, continue using them for complex behaviors. They can improve code organization and readability for entities with multiple distinct states.

## Actionable Tasks for an AI Programming Agent (Sorted by Ease and Potential Impact)

Here's a breakdown of tasks, focusing on the easiest to implement with the highest potential performance impact:

**Tier 1: Easy Implementation, Medium-High Performance Impact**

1. **Task:** Implement Caching of Components.
    - **Scripts to Modify:** Many scripts, particularly those in the Camera, Player, and Other directories that frequently access components using GetComponent.
    - **Action:** In Awake() or Start(), cache references to commonly used components (e.g., GetComponent<Transform>(), GetComponent<Rigidbody>(), GetComponent<Renderer>()). Use these cached references instead of calling GetComponent repeatedly.
    - **Rationale:** GetComponent is a relatively expensive operation. Caching significantly reduces this overhead, especially in Update() or FixedUpdate() loops.
    - **Ease:** Easy. Primarily involves adding a private field and a GetComponent call in Awake/Start.
    - **Impact:** Medium-High. Can have a noticeable impact, especially on frequently updated objects.
2. **Task:** Replace String Literals with Constants.
    - **Scripts to Modify:** ConditionalDebug.cs, TimeTesting.cs, FmodOneshots.cs, CinemachineColliderExtension.cs, StartingVFX.cs, LevelLoader.cs, MainMenuSwitchScene.cs, OnSwitchSceneEvent.cs, PlayerShooting.cs, SpawnFromPool.cs, ProjectileDetection.cs, and potentially others.
    - **Action:** Create static readonly string constants in relevant classes (or a dedicated GameConstants class) for tags, event names, layer names, shader property names, etc. Replace all instances of the string literals with these constants.
    - **Rationale:** Improves maintainability (easier to change a value in one place), reduces the risk of typos, and can offer a very slight performance improvement due to string interning.
    - **Ease:** Easy. Mostly find-and-replace operations.
    - **Impact:** Medium. While the individual performance gain might be small, it contributes to overall code quality and reduces potential bugs.
3. **Task:** Replace Magic Numbers with Named Constants/Serialized Fields.
    - **Scripts to Modify:** Numerous scripts, especially those with movement, timing, or distance calculations (e.g., TimeTesting.cs, CinemachineColliderExtension.cs, OuroborosInfiniteTrack.cs, DestroyEffect.cs, GhostProjectile.cs, WarpSpeed.cs, PlayerGroundAdjuster.cs, PlayerMovement.cs, PlayerShooting.cs, ProjectileDetection.cs, ProjectileStateBased.cs, CustomParentConstraint.cs, DottedLine.cs, ObjectAvoidance.cs, ObjectTeleporter.cs, Orbital.cs, RotateRing.cs, SnakeTween.cs, TailSegment.cs, TempoSpin.cs).
    - **Action:** Identify "magic numbers" (literal numerical values with unclear meaning). Either create const static fields for values that shouldn't change or use [SerializeField] to expose them in the inspector.
    - **Rationale:** Dramatically improves code readability and maintainability. Makes it clear what the values represent and allows for easy tweaking in the editor.
    - **Ease:** Easy-Medium. Requires identifying the magic numbers and deciding whether they should be constants or serialized.
    - **Impact:** Medium. Primarily improves maintainability and reduces the chance of errors. Can indirectly improve performance by allowing for better tuning of parameters.

**Tier 2: Medium Implementation, Medium-High Performance Impact**

1. **Task:** Implement Basic Pooling for Visual Effects (Where Not Already Used).
    - **Scripts to Modify:** Scripts that instantiate visual effects using Instantiate and Destroy without using a pool (e.g., potentially in DestroyEffect.cs, GhostProjectile.cs, WarpSpeed.cs, and projectile-related scripts if they spawn effects).
    - **Action:** Create pooler scripts for common visual effects prefabs. Modify the scripts to get and release effects from the pool instead of instantiating and destroying them directly.
    - **Rationale:** Instantiating and destroying GameObjects frequently is expensive. Pooling reuses existing objects, reducing garbage collection and improving performance. You already have ParticleSystemPooler and PoolProjectiles, so extending this pattern is logical.
    - **Ease:** Medium. Requires creating new pooler scripts and modifying instantiation logic.
    - **Impact:** Medium-High. Can have a significant impact if visual effects are frequently created and destroyed.
2. **Task:** Optimize OuroborosInfiniteTrack.cs.
    - **Scripts to Modify:** OuroborosInfiniteTrack.cs.
    - **Action:** This is a complex task, but focusing on the following could yield significant gains:
        - **Reduce GetComponent calls:** Cache references to frequently used components within the script and on related GameObjects.
        - **Review FixedUpdate Logic:** Break down the large FixedUpdate method into smaller, more manageable functions. Profile this method to identify performance bottlenecks.
        - **Object Pooling for Prefabs:** Ensure the prefabs placed along the track are efficiently pooled.
        - **Mesh Generation Optimization:** Analyze the mesh generation process (CurvyGenerator). Are there any ways to optimize the geometry creation or updates? Consider using the Jobs system or Burst compiler if the generation is CPU-intensive.
        - **Batching:** The script mentions batching. Ensure this is implemented correctly and efficiently. Look for opportunities to further optimize batching.
    - **Rationale:** This script appears to be a core component with potentially high performance demands. Optimizing it can lead to significant frame rate improvements.
    - **Ease:** Medium-Hard. Requires a deeper understanding of the script's logic and potential performance bottlenecks.
    - **Impact:** High. Significant potential for performance gains.

**Tier 3: Medium-Hard Implementation, Medium-High Performance Impact**

1. **Task:** Refactor Potential Code Duplication.
    - **Scripts to Modify:** Look for similarities in functionality across different scripts. Examples might be found in movement logic (PlayerMovement.cs, ShooterMovement.cs), look-at behavior (LookAtCamera.cs, LookAtGameObject.cs, LookAtReversed.cs), or FMOD logging (FMODCustomLogger.cs, FMODLogger.cs).
    - **Action:** Identify duplicated code blocks or similar logic. Extract this logic into shared methods within a utility class or potentially create new base classes or interfaces.
    - **Rationale:** Reduces code redundancy, improves maintainability, and can sometimes lead to performance improvements by centralizing logic.
    - **Ease:** Medium-Hard. Requires careful analysis of different scripts and designing appropriate abstractions.
    - **Impact:** Medium. Primarily improves maintainability but can indirectly improve performance by simplifying code.
2. **Task:** Investigate and Potentially Refactor ProjectileStateBased.cs.
    - **Scripts to Modify:** ProjectileStateBased.cs.
    - **Action:** This script is flagged as complex. Consider the following:
        - **Break down large methods:** The analysis points out many large methods (UpdatePredictedPosition, OnTriggerEnter, Death, ResetForPool, UpdatePosition). Break these into smaller, more focused functions.
        - **Simplify Conditional Logic:** Reduce nested if statements and multiple checks within methods.
        - **Review State Machine Implementation:** Ensure the state machine is implemented efficiently and cleanly.
        - **Optimize Job System Integration:** Verify that the job system is being used optimally and that data is being passed efficiently.
        - **Consider Composition over Inheritance:** Instead of a monolithic class, explore using smaller, focused components that handle specific aspects of projectile behavior.
    - **Rationale:** Simplifying this complex script will improve readability, maintainability, and reduce the potential for bugs. It might also reveal opportunities for performance optimization.
    - **Ease:** Medium-Hard. Requires a deep understanding of the script's functionality and design.
    - **Impact:** Medium-High. Can significantly improve maintainability and potentially performance.

**Tier 4: Hard Implementation, Potentially High Performance Impact (Proceed with Caution)**

1. **Task:** Address Reflection in DisablePlayerFeatures.cs.
    - **Scripts to Modify:** DisablePlayerFeatures.cs.
    - **Action:** Instead of using reflection to access a private field, try to find a more direct and maintainable way to achieve the desired behavior. This might involve:
        - Modifying the target script to expose the necessary functionality (if you have control over it).
        - Using a different approach that doesn't rely on disabling the action directly.
    - **Rationale:** Reflection is generally less performant and more brittle than direct access. It can break if the internal structure of the target class changes.
    - **Ease:** Hard. Requires understanding the interaction between DisablePlayerFeatures.cs and the target script and potentially redesigning the approach.
    - **Impact:** Low-Medium (Performance), High (Maintainability). The performance impact of this specific reflection might be small, but it sets a bad precedent. The main benefit is improved maintainability.

## Specific Problem Solutions and Recommendations

Here's a breakdown of solutions to the problems you identified:

- **Magic Numbers:** Replace them with const static fields for values that don't change or [SerializeField] private fields for values that can be tweaked in the editor.
    - **Pros:** Improved readability, maintainability, easier to understand the purpose of values.
    - **Cons:** Might require a bit more upfront work to define the constants/fields.
- **String Literals:** Replace them with static readonly string constants.
    - **Pros:** Improved maintainability, reduces typos, slight potential performance gain due to string interning.
    - **Cons:** None significant.
- **Performance of Complex Scripts (OuroborosInfiniteTrack.cs, ProjectileStateBased.cs):** Focus on caching, reducing unnecessary computations, optimizing data structures, and potentially leveraging the Jobs system and Burst compiler for CPU-intensive tasks. Profile these scripts to pinpoint bottlenecks.
    - **Pros:** Significant performance improvements.
    - **Cons:** Can be time-consuming and require careful analysis.
- **Error Handling:** Implement more robust error checking with Debug.LogError for critical issues and consider using exceptions where appropriate.
    - **Pros:** More stable and reliable code, easier to debug.
    - **Cons:** Can add some overhead if not used judiciously.
- **Code Duplication:** Refactor common logic into shared methods, utility classes, or base classes.
    - **Pros:** Improved maintainability, reduced code size, easier to update logic in one place.
    - **Cons:** Requires careful design to create effective abstractions.
- **Comments and Documentation:** Add clear and concise comments to explain the purpose and functionality of scripts and methods. Consider using XML documentation for public APIs.
    - **Pros:** Improved code understanding and maintainability.
    - **Cons:** Requires discipline to keep documentation up-to-date.
- **Use of Reflection (DisablePlayerFeatures.cs):** Avoid reflection if possible. Look for alternative solutions that provide direct access or modify the target script's API.
    - **Pros:** More performant, less brittle code.
    - **Cons:** Might require more significant code changes.
- **Magic Strings (OnSwitchSceneEvent.cs):** Replace with const string constants.
    - **Pros:** Improved maintainability, reduces typos.
    - **Cons:** None.
- **Hardcoded Values (LoadingScreen.cs):** Make these [SerializeField] fields to allow for easy tweaking in the editor.
    - **Pros:** More flexible and configurable.
    - **Cons:** None.
- **Missing Null Checks:** Add null checks where necessary to prevent NullReferenceException errors.
    - **Pros:** More robust code, prevents crashes.
    - **Cons:** Can add some boilerplate code.
- **Inconsistent Naming:** Follow consistent naming conventions (e.g., PascalCase for classes and methods, camelCase for variables). Use a code analyzer/formatter to help enforce consistency.
    - **Pros:** Improved readability and maintainability.
    - **Cons:** Might require renaming existing elements.
- **Lack of Abstraction:** Use interfaces and abstract classes to decouple components and make the code more flexible and testable.
    - **Pros:** Improved modularity, testability, and extensibility.
    - **Cons:** Can increase complexity initially.
- **Missing Editor Tools:** Consider creating custom editor scripts to simplify the setup and configuration of complex components.
    - **Pros:** Improved workflow and ease of use.
    - **Cons:** Requires additional development effort.
- **Specific Issues in ProjectileStateBased.cs and OuroborosInfiniteTrack.cs:** Address the specific points raised in your analysis, such as breaking down large methods, simplifying logic, optimizing loops, and ensuring proper resource management (e.g., releasing pooled objects).

By prioritizing the tasks in Tier 1 and Tier 2, you can make significant improvements to your game's performance and maintainability with relatively little effort. Focus on the easiest wins first and then tackle the more complex optimizations. Remember to profile your game regularly to identify the areas where optimization will have the most impact.

---

## From: January 10th, 2024.md

# January 10th, 2024

Trying to do a build of a scene, upgrading things and moving to laptop seem to bring up old issues

Compiling shaders causes build to take over an hour or more, not letting it finish. For one scene, should be much quicker I think. Tried preloading shader variants under graphics settings, will see if this moves initial build quicker. Will have to do more research if that doesn't fix the problem

This error happening on transition - player mesh had disappeared - maybe dodge related?

![Untitled](January%2010th,%202024%206ab92944aeed4d9c85ad8aad0b842133/Untitled.png)

Use gravity rush enemies as inspiration for my enemies - look up the various types 

![Untitled](January%2010th,%202024%206ab92944aeed4d9c85ad8aad0b842133/Untitled%201.png)

Want to apply this to text and ui in Unity

[https://twitter.com/ashlee3dee/status/1745524506872176970](https://twitter.com/ashlee3dee/status/1745524506872176970)

Maybe look at Ace Combat, emulates this a bit - search for this

---

## From: January 15th - Many Links!.md

# January 15th - Many Links!

Game Dev Links

[https://github.com/QuentinKing/OuterWildsBlackHole](https://github.com/QuentinKing/OuterWildsBlackHole)

- [https://github.com/QuentinKing/OuterWildsBlackHole/issues/1](https://github.com/QuentinKing/OuterWildsBlackHole/issues/1)

[https://github.com/JonasDeM/QuickSave](https://github.com/JonasDeM/QuickSave)?

[https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides](https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides)

[https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance](https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance)

[https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/](https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/)

[https://www.youtube.com/watch?v=3CfpJlGI2wc](https://www.youtube.com/watch?v=3CfpJlGI2wc)

[https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released](https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released)

[https://twitter.com/OhPoorPup/status/1727814207398408439](https://twitter.com/OhPoorPup/status/1727814207398408439)

[One Wheel Studio](https://www.youtube.com/@OneWheelStudio/videos)

[https://twitter.com/folmerkelly/status/1729145782337716492](https://twitter.com/folmerkelly/status/1729145782337716492)

[https://www.youtube.com/watch?v=1N_4NzwprJI](https://www.youtube.com/watch?v=1N_4NzwprJI)

[https://vol.co/products/wipeout-futurism](https://vol.co/products/wipeout-futurism)

[https://www.youtube.com/watch?v=hJcFgZqT0ow](https://www.youtube.com/watch?v=hJcFgZqT0ow)

[Scott Game Sounds](https://www.youtube.com/@ScottGameSounds/videos)

[https://twitter.com/DominicTarason/status/1731662776840372406](https://twitter.com/DominicTarason/status/1731662776840372406)

[Portal effect in URP](https://www.reddit.com/r/Unity3D/comments/18aeidd/portal_effect_in_urp/)

[https://github.com/Unity-Technologies/ProjectAuditor](https://github.com/Unity-Technologies/ProjectAuditor)

[https://twitter.com/andytouch/status/1733190142548992437](https://twitter.com/andytouch/status/1733190142548992437)

[Easy and Powerful Extension Methods | Unity C#](https://www.youtube.com/watch?v=Nk49EUf7yyU)

[Shader Graph: Learn to use nodes with Node Reference Samples | Tutorial](https://www.youtube.com/watch?app=desktop&v=vrEi6M3GjC4)

[https://github.com/DeadlyRedCube/Cathode-Retro](https://github.com/DeadlyRedCube/Cathode-Retro)?

[https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events](https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events)

[https://github.com/pointcache/BasicEventBus](https://github.com/pointcache/BasicEventBus)

[https://softsaz.ir/blender-market-citybuilder-3d/](https://softsaz.ir/blender-market-citybuilder-3d/)

[https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/](https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/)

[RGD Workshop : Rational Game and Level Design](https://www.youtube.com/watch?v=xqHAjwrNp70)

[https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/](https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/)

[Mirza Beig on Twitter / X](https://twitter.com/TheMirzaBeig/status/1715936953407815983)

[https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)

[Harry Clarke’s Illustrations](https://kottke.org/23/12/harry-clarkes-illustrations)

[Shmup Visual Design, Colour Theory and Bullet Visibility (Subtítulos en Español)](https://www.youtube.com/watch?v=NxVG7caNmO0)

[Twisted Corridor Effect in Unity Shader Graph](https://www.youtube.com/watch?v=CGI0SyiElfY)

[The Cinematography of Evangelion](https://www.youtube.com/watch?v=fsmbpEZ_lI8)

[How Platinum Design a Combat System](https://www.youtube.com/watch?v=D689ZBdOAuw)

[What's new in Unity's Universal Render Pipeline?](https://www.youtube.com/watch?v=Z-XCk8mXJtI)

[[TUTORIAL] Stencil buffer in Unity URP (cutting holes, impossible geometry, magic card)](https://www.youtube.com/watch?v=y-SEiDTbszk)

[Rhythm game architecture](https://www.reddit.com/r/Unity3D/comments/196l47e/rhythm_game_architecture/)

[10 Unity Tips You (Probably) Didn't Know About](https://www.youtube.com/watch?v=JgAhIX8YLBw)

[https://twitter.com/ashlee3dee/status/1746606648330686730](https://twitter.com/ashlee3dee/status/1746606648330686730)

[Better Singletons in Unity C#](https://www.youtube.com/watch?v=LFOXge7Ak3E)

[hazylevels](https://www.youtube.com/@hazylevels/videos)

[Service Locator: Inversion of Control in Unity C#](https://www.youtube.com/watch?v=D4r5EyYQvwY)

[https://github.com/electronicarts/IRIS](https://github.com/electronicarts/IRIS)

[https://github.com/sudotman/BetterUnity](https://github.com/sudotman/BetterUnity)?

[Easy Optimization! Flyweight design pattern in unity](https://www.youtube.com/watch?v=zrUhpjwBpPI)

[I Asked 17 Game Developers For Their Biggest Tips](https://www.youtube.com/watch?v=ESrw7BerWgM)

[Write Clean Code to Reduce Cognitive Load](https://www.reddit.com/r/programming/comments/17to0h1/write_clean_code_to_reduce_cognitive_load/)

[Warped Imagination](https://www.youtube.com/@WarpedImagination/videos)

[Eric Wang_VFX Artist](https://www.youtube.com/@EricWang0110/videos)

[Shockwave Shader Graph - How to make a shock wave shader in Unity URP/HDRP](https://www.youtube.com/watch?v=dFDAwT5iozo)

[Don't Use Deep Profiler! - Profiler Marker Unity](https://www.youtube.com/watch?app=desktop&v=DsrFkPP0Qe8)

ALternate radar? 

[How to make RPG Radar Chart (Unity Tutorial)](https://www.youtube.com/watch?v=twjMW7CxIKk)

Make bullets glow - look into this

[How to make Unity GLOW! (Post Processing Bloom Unity Tutorial)](https://www.youtube.com/watch?v=bkPe1hxOmbI)

Apply bloom to specific objects?

[How to Add GLOW to Certain Objects | Unity 2022](https://www.youtube.com/watch?v=9v6yyRIoWnE)

THis work for adding selective bloom, just need to adjust intensity of objects emission map

Better glow on projectiles - good step forward! need to refine

Need to look into when light intensity is too much in the scene - why is it happening?

Need to make the game more fast paced, feels a bit slow?

---

## From: January 17th.md

# January 17th

Adjusting Reticle control to try and fix jerky issues

Have Interpolate off right now to make it more responsive, unsure if that’s actually helping

But in general, it’s working better than before by having adjusted the code to better use the new input system - existing outside the Update method I believe. 

Testing reticle movement in build to verify changes occur there.

Need to figure out how to get camera to rotate up/down more when reticle moves towards edges, necessary for looking around. Thought I solved this problem already????

Need to find a way to reduce build times - Assembly Definitions? Look into it

Need to fix various small problems with game play, audio, that i thought were already fixed???

---

## From: January 18th.md

# January 18th

Need to organize my own scripts so I can better use Cursor to provide feedback. Put them all in one folder, so they can be looked at. 

Need different radar icons for enemies vs bullets - this is perspective radar not minimap

Look up references!

THink on idea - graphics variation - maintain interest this way.

---

## From: January 22.md

# January 22

Remove indicators from bullets - too messy and cooler when they’re not there

Fixing lock on inidicators on shooter - currently cant see them, finding a solution

- Still not great, need to look at new material or different rendering methods
    - Make slightly emissive?

Changing lock on projectile behaviour so that if you attempt to lock on within a certain radius, it just shoots at the target enemy right away

Need to also address behaviour if there are no target enemies

- ideally it just fires off into the distance

Code is in there but needs to be verified, and lockableRadius needs to be tested

Busted it! Need to try this again 

- not too hard

Added press R to reload scene, so I can start over when using hot reload

Should expand this to jumping phases? or just kill enemies to quickly move to next phase?

---

## From: January 23.md

# January 23

Adjusted shooter movement so its seperated from frame rate - connected to TImeline of Chronos - Constant Clock

Looking into camera adjustments now

Havent figured it out. May have broken reticle partially.

---

## From: January 24th.md

# January 24th

Steam Devkit uplaod system setup - can easily upload to steamdeck now

Camera movement when reticle moves to edges also setup - this is working ok

Still not sure why controls janky in editor but okay on device / build

Something weird going on!

NOTE

When playing, too much down time. Things are too slows

Need more activity and movement! more effects! 

Avoid down time - need more to do?

---

## From: January 25th.md

# January 25th

Adjusted Shooting Movement so that rotations dont put camera directly throguh ground as much, center of screen adjusted for same reasons. player now lower in view.

Adjusting small things that have come up

- Filter to audio no longer applied when hitting start
    - Fixed, all exists on Pause Menu Manager now

Updated Reach to and trying to quickly fix things, will keep it going for an hour to see if its worth it

- Seems good so far!

---

## From: January 26th.md

# January 26th

Did more adjusting the UI, basically fixed it

Watching Under the Skin… why cant video games before unexplained? Visceral in this way? Why not?

Use simple things to set the tone and vibe, makes later choices more impactful

Consider how to apply this… horror games probably lean into these ideas

Beginning of Under the Skin as a ‘Birth” ?

Ideas for Beat Remake

- controls are wonky at start, slowly become more sensible, its being born, its learning, getting adjusted

Have sped up bullet ssignificantly

Need to give them obstacle avoidance, at least for ground? 

- apply this for both enemy shooting and player shooting?

Place static enemies along the snake to also take out? Gives more to do

---

## From: January 27th.md

# January 27th

Looked into Shapes, may be a worthwhile replacement on reticle

Not attempting that now

Changes fonts to something more interesting

- WipeOut Influenced

Have particles effects on Score? Replicate Rez tracking every action

Have each thing that effects score show the numbers of whats happening and particle effects

Like - 100, + 500 etc

Considering UI, what might work best

---

## From: January 28th.md

# January 28th

Added some code for projectiles to avoid ground, likely needs to be tweaked

SDF stuff is adding some interesting effects, not final but interesting experiement

Trying out build on steam deck

---

## From: January 29th.md

# January 29th

Trying to add some visual whizz bang to the game, flourishes or emphasizing things more

Changing enemy material, using Ultimate Lit Shader to get better lighting

- Went back to regular Lit shader cause of better light properties, need to look into why

Changed enemies to use Better Object Pooling for Birth/Death FX

Fixed dodging issues, was dotween being killed in the movement. removed the material swap when dodging cause it’s not currently working - will come back to this

Intersting setup for having multiple levels

[Split up YOUR Game in Unity](https://www.youtube.com/watch?v=YacTpE4ZP_w)

---

## From: January 30th.md

# January 30th

Need to fix Scaling issues of bullets, they’re wildly off depending on what object shoots them. Need to set them a certain size and not inheirit scale from parent objects

Adding a static shooting “enemy” - not killable currently but emits bullets from terrain 

Using FindObjectOfType in the class, this could be a problem. Need to refine this so the code is better 

The on wave stated issue didnt fix my problem seems likely it more of a pooling issue, with projectile pool being created and some particular reference being required. 

Issue is finding tag with player when player is currently disbaled - lol
”_projectile.currentTarget = GameObject.FindWithTag("Player").transform;”

Removing all the wavespawner stuff, not necessary?

Changed Cinemachien Camera Switching so that it disables player model and reticle, rather than the whole playe object, that way game SHOULD start without errors about finding the player game object

Snake Eyeball Prefab is made, should be good to place around multiple areas of the Snake.

---

## From: January 31.md

# January 31

Death in Abyss game - a little similar? Neat!

[Agelvik | Wishlist 'Death In Abyss' (@Agelvik) on X](https://twitter.com/Agelvik)

[christa lee on Twitter / X](https://twitter.com/OhPoorPup/status/1752440548852674984)

---

## From: January 5th, 2024.md

# January 5th, 2024

Need to make some rigid decisions on Bosses / Enemies / Levels

Strategy for creative look of bosses - several phases?

- use a base shape as point to construct boss
- use geometry nodes to make a deconstructed look for the boss
- third phase? Unique to each boss?

Having issues importing rigged characters into blender

Need to see if Importing with 3DS Max, then exporting to Blender, may work. 

Also will try with Maya

---

## From: July 14.md

# July 14

Trying to debug path fail issue on current iteration, version from a few weeks ago doesn’t encounter this

Eventually path fails critically on a mesh, or when enemy dies and they spawn again, they all get paith fail and are on the fritz. This behaviour doesn’t happen in older project version, so trying to debug this. 

Need to assess if fixing new version is better then building on top of old version until something breaks

In the mean time - level strucutre! What do I want to do?  Stil taking a break from Ouroboros?

Ophanim is coming along. 

Idea - use deformer on ophanim to  make things twistier? Will Curvy splines respond appropriately?

---

## From: July 15.md

# July 15

Reverted to an old version due to this issues, and picking through the improvements to carry forward. 

Don’t think i’ll touch the splitting the enemy class much at the moment, feel the issue is here but couldn’t figure it out. 

Things to bring over

- linger Ophanim developments
- Deform attributes
- Polyfew? Or did this cause problems….
- Optimizations - use profiler to diagnose
- Newish camera system - not bound to layer up/down/left/right as much
    - Important for Ophanim level
- Menu System
- Enemy Basic Shoot script + SHooting patterns

![Untitled](July%2015%20fc61f760a9724ed8b24c749f2e419c2e/Untitled.png)

Also - simplify render pipeline for big performance gain - normal / transparent rendering 

Only have one? Find a simpler way to do this?

---

## From: July 22.md

# July 22

Looking at a few optimiziation things

Need to optimize projectile particle effects

Pool or something? Offload to Projectile Manager?

---

## From: July 24 25.md

# July 24/25

Getting stuck in rewind time state occasionally, need to address this

need to list out thing / changes made

most recent is reverse direction you’re running

- can i have player reverse direction and camera not flip here???l

also a detection zone for projectile coming at you with sound effect

---

## From: July 29 30.md

# July 29/30

Koreographer track issue was breaking the infinite areas

go over how that works again - unsure why this broke things

- i udnerstand the track it wanted wasnt there, but i dont know why im use on collection of tracks vs another, need to evaluate how all of that works again

Setup GIt for this project again

Need to approach oncoming tasks as small pieces, time blocked. This will help me actually get things done. 

31st

https://www.youtube.com/watch?v=Dv7KImnPnm8

Section sounds into SUb / LOw / Mids / Highs and consider what type of sounds get what

Bus Structure

- Define busses for general soudn types, and properly sidechain them for what needs to be highlighted

Transient / Body / Tail structure for sound clips may help with setup in FMOD

Use Spatializer to give certain sounds bigger impact

Testing game

- Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it
- Locked state material defined on projectile - problem with this level is the scale of the projecitle makes it too big when locked, looks odd - Sclaed down from 10 to 3 currently
- Verify Infinite Snake View Target - is this necessary anymore?
- Adding deformables to the tail end of the infinit ouroboros generation
    - deformable’s are being added, need to verify they properly deform

GOt Deformables working in Infinite Snake section! Good stuff

---

## From: July 5th.md

# July 5th

Lost of changes and improvements over the past couple weeks

Need to look back on June notes to see what was fixed / whats still standing

Brainstorming session important now on progression of Ophanim levels

On one ring / enemies on multiple others. Take them out

Take out enough enemies - move to next section

QUESTION: Is this on the same ring? Or a new ring? Can i have two waves on rings - 3rd is interior eye? 

Interior Eye - Cube or other shape with eyes on all sides 

Maybe a bit of a rhythm game in that you’re floating in the middle and different eyes are shooting at you - eyelid opens and closes - you need to lock on and shoot an open eye. if it’s closed it doesnt damage them. cube is rotating and you only need to take out 4 of 6 eyes. 

Idea for main ophanim boss 

- similar mechanic to level, but every few you take out, you go deeper on the ring?
- you eventually get to the central eye

**July 9th**

Can I use these to make snakes movement look more interesting?
https://assetstore.unity.com/packages/tools/modeling/deform-148425

Use Deform to alter levels / mesh in interesting ways? Could this help add variety to levels?

Try deform of colliders and see how enemies respond to that. Check performance of altering these things.

Tried Recast for movement of enemies in an internal cube, unfortunately did not work. 

How can i have enemies freely move along walls and odd surfaces, but have those surfaces moving?

Looking at A* demos for level/enemy ideas

Everything very flat - putting enemies on wall or similar doesnt seem to be possible. 

Can use basic unity navmesh and achieve this though
https://www.youtube.com/watch?v=QCYBI6JaeIU

Could possible use the same system meant for odd shapes and have the enemies move around properly there. need a more rounded shape with flipped normals but it might work. I dont think i could have it move around though.

**WHy not just spin the player in place instead? The whole platform and rotate making things seem like they’re spinning - probably the best bet!**

Could try both 

ALso try doing the galaxy material but with different textures for similar effect but different look 

Void Behemoth - Space - move around in the wake of a comet / asteroid ? 

Mild amount of light from it, rest of level quite dark with sound causing light to occur

Seraphim Nebula - Tendrils

Enemies likely need to move along curvy splines matched to tendrils as well.

Defeat some - shift to next set of tendrils

**World Tree Yggdrasil -** 

Similar to tendrils but they’re not moving. We’re moving along branches

Get to the base of the tree then go to move along the reflection of the tree?

Data flows from the branches

the ‘pool’ that seperates the two sides of the tree is data as well ‘Datapool’

Water element - reflection

Metatron’s Cube

- Player moves to the curvy spline that they’re currently facing while also trying to take out all the enemies
- Player and enemies move along different parts / splines
    - Use colour to delineate this
- 

 

Use Deform on stuff - it seems really good!

---

## From: June 10.md

# June 10

Fixed some issues with haptics and Feedbacks not playing

Added more sounds and fix transisiton between levels

IMPORTAT to FIX

Need to never be locking on when moving into next level, and need to make sure it doesnt transition into a lock on section on next level start. need to investigate how to handle this.

---

## From: June 11.md

# June 11

Aggregating the last week of issues / concerns to tackle

- Need to never be locking on when moving into next level, and need to make sure it doesnt transition into a lock on section on next level start. need to investigate how to handle this.
- remove static shooter bullets from radar. only bullets with homing on player show up
- implement descending health and see how game feels - is it fair?
- Can I get snake tail enemies working well?
- Make more levels by alternating path player is on / flipping things upside down or sideways
- More FX needed, more feedbacks. Think about what player needs to know - maximum locks, projetiles are close by, etc
- Consider more sounds to tell whats happening.
- Build out dual snake boss area
- FX for time slow / rewind glitch things. very lacking right now.
    - what are the utitily of these? give more of a reason to reach for them during gameplay - think on scenarios
    - can slow time initiate doppler on projectiles?
    - Look into more interesting VFX like Inkblot, etc
- watch jason booth talk again about optimization - what can be applied
- Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Previous things to do a month ago - verify this list with what needs to be done

[May 15](../May%209ac535a1c587434abe88186ee2b96d25/May%2015%20b4778e7f23ba46e39d3f0f43659cc83d.md) 

Revisit Idea

- more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
    - Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm.
    

Parent Constraint script is neat / may be useful for other scenarios

- Infinite snake objects in background?
- Other scripts from this lot that could be useful as well?

---

## From: June 13.md

# June 13

---

## From: June 16.md

# June 16

Making sure no locked targets or projectiles when a scene change occurs 

Should help with scene transition issues

Game Manager is calling Crosshair to handle this 

Have moved AdjustSongParamters into Game Manager now. Combining these for clearer use

Partially working, not completely. Doesn’t work effectively on scene change, need to debug

---

## From: June 18.md

# June 18

Doing some dev build optimization to change things up from working on coding problem of level changes

Waking all these game objects at start cause a big frame spike 

![Untitled](June%2018%20129035f997e64f1db9d605749b914beb/Untitled.png)

Debug Logs from Death of PSB causing spikes

![Untitled](June%2018%20129035f997e64f1db9d605749b914beb/Untitled%201.png)

Seems like using Deep Profiler was causing some issues, not good to use on device connected live i think, probably better for a separate device 

Changing projectile state based to use update for lifetime isntead of a seperate coroutine. this is supposed to be more performant. debug!

Also causing calls to projectiel manager to be paced across several frames, that way a higher frame rate is maintaned and less of a spike. Seems to be working well!

Replacing dotween with primetween, may be better. taking a look!

I dont entirely see the animation happening, but the code looks right. need to flag this, may be some adjustment needed to get it working properly. 

Back to DOtween for the time being - for ProjecitleStateBased at least

Moved Projecitle lifetine to Projectile Manager now - for performance!

Projectile Radar prefabs are pooled as well, added to projectiles wehn shot by enemy

definitely closer to 60! not dropping to 15 frequently like before as well

Still need to fix transition between scenes, dont forget

---

## From: June 25.md

# June 25

Many updates! Just poor job of documenting them. Many strucutral

Important things → Frame debugging

Need to find best shader for projectiles

FIx bug of changing between splines

THe code needs cleaning up and assessing

---

## From: June 26.md

# June 26

Looking at the transition phase - the code is messy here, im not sure whats doing what exactly

its currently messing up with transition, not invoking the trans cam off event as expected

Need to Position Viper better in chase areas - not in proper line with player

Mostly sorted but needs to be more upright / refined

---

## From: June 27th.md

# June 27th

Some optimization and refinement of enemy bullets. Struggling with interesting movement here, so I’ve pulled it back to more direct hits. Need to refine this. Get dissolve or disappearing effect working better. 

THinknig about reticle, have it adjust size / shape with upgrades in number of lock ons? How can I pull back on things to allow for greater weight of what you lock on to? 

Difficult to choose the bullets fired at you / easy to choose the ones that are shot up from environment. Is there a solution to this?

Testing

- Running through scenes, making notes of whats currently wrong

Notes

- force player to face forward on each song section change
- radar should be adjusted per level / scale is off

Ouroboros Section 3

- Mobius Multi Var 1 Needs an A* scan - FIXED
- Mobius Tube 5 needs model flipped and fixed
    - then new A* scan
- Is there anything interesting to be done on an interior section? Can use this model if so?

How do i Feel about crazy glitch looking infinite snake?

Ourobors Section 5

- does not load in properly, stuck on start sound - FIXED

If i use n to change sections then there are some game objects that need to be reactivted, Scene Switch cleanup is supposed to do this, but its not working properly.

---

## From: June 5.md

# June 5
 2 | 
 3 | Figuring out all Ouroboros levels
 4 | 
 5 | Section 1
 6 | 
 7 | - Mobius Tube 2 Variation 2
 8 | - Mobius Tube 2
 9 | 
10 | Section 2
11 | 
12 | - Infinite Track 1
13 | 
14 | Section 3
15 | 
16 | - Mobius Tube Multi Var 1
17 | - Mobius Tube 5
18 | 
19 | Section 4
20 | 
21 | - Infinite Track 2
22 | 
23 | Section 5
24 | 
25 | - 
26 | 
27 | Need many more enemies per wave
28 | 
29 | But only a few die to advance
30 | 
31 | MUbe they're one hit?
32 | 
33 | The. Use multi tied enemies for more hits

---

## From: June 6.md

# June 6
 2 | 
 3 | Introduced minimum kill count into Ultimate Wave Spawner as a continue mode. Need to test!
 4 | 
 5 | Probably need to account for this in despawning or enemy deaths. 
 6 | 
 7 | Also set start health to 10 for instant kill
 8 | 
 9 | Thoughts on having many bullets flynig at player
10 | 
11 | Make a system where they once they reach a certain radius, they hit the player in time? 
12 | 
13 | more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
14 | 
15 | Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm. 
16 | 
17 | Important - made the previous edits to the wave events, now I made more
18 | 
19 | Instead of subscribing in the inspector on the WaveSpawnController, added a script that subscribes to all active instances of the event. This is better for changing scenes, should find the carried over fmod and such. 
20 | 
21 | These are the events 
22 | 
23 | ![Untitled](June%206%20e9f434ec8d424de5a514ca512ad45f55/Untitled.png)
24 | 
25 | Order matters!
26 | 
27 | Idea - time is running down / health is running down
28 | 
29 | No limit on lock on 
30 | 
31 | Just get as many as possible and go
32 | 
33 | Made changes to crosshair, verify these dont break things. Should just be null checks but revert if necessary.
34 | 
35 | The issue is definitely arrising due to increasing the locked on enemy count. I have more so the scenario is more possible. that they die when still locked on, i guess? 
 36 | 
 37 | Seems to be working okay now
 38 | 
 39 | SNake tail enemies dont totally work, need more adjustment - causing technical errors

---

## From: June 7.md

# June 7

Minor adjustments - not in the mood today

reticle more visible in Section 1

- Crosshair Static - Transparency 240

Need to assess - Player as prefab - can i just use override and apply setting changes across all scenes? What changes? is there anything specific? 

You can individually apply overrides so this does not matter - just apply the relevant ones

---

## From: June 8.md

# June 8

Looking at feedbacks, reconfiguring them - getting them to work agian

Can get haptics workning again!

---

## From: June 9.md

# June 9

Fixing up some MM Feedbacks 

Added a scaling lock on effect for the reticle, works for lock on to bullets

Doesnt seem to work when shooting - look into this!!

---

## From: March 10.md

# March 10

Fixing the prefab pooling of Snake Eyes on infinite track generation

Wasnt talking to pool properly

Editied Better Object Pooling Instance - ensure this isnt reset!

Added StopAllCoroutines to it. 

Still havent figured this out.

Coroutine issues still happening

 BUT

- prefabs are being properly despawned now
- doesn’t seem to effect gameplay?

Need to still figure out why i can extend the tail in the example scene, however in my scene it’s not working properly, im always at the end of it. it seems this becomes a problem over time - needs to be addressed, for visual reasons and others

Changed snake eyeballs to shoot from their forward. 

Enemies need to shoot a different colour bullet, rectify this

---

## From: March 11.md

# March 11

Compilation of past comments / issues

- Enemies need to shoot a different colour bullet,
    - Material swap for this?
- Need to still figure out why i can extend the tail
- Coroutine issues still happening (doesnt break gameplay?)
- Edit snake mesh
- Not using A* in Infinite Track example - adjust rotation / ground detection accordingly
- refine aspect: reticle aim influences locked projectiles - currently only rotation
- Remove or refine dotween shake on hit ColliderCallBacks
- Infinite Snake - figure out gameplay structure here
- Also could use Infinite Snake as part of travelling inside of one?
    - Would interior of regular ouroboros be good enough here? Or preferred?
- Update all Mobius Tube
    - All need adjustment, but 6 needs a lot of work - no enemies? Maybe a bad structure

Inspo

- Hyperpassive as a rubric for the music and interactivity in Beat Remake
- Possible Settings guidelines for Unity
    - [https://github.com/xZenvin/UnitySettingsFramework](https://github.com/xZenvin/UnitySettingsFramework)

**Ophanim**

running along moving rings

- how could infinite track be applied here??

IMPORTANT

to make things brighter / more emissive / stand out, HDR intensity needs to be adjusted along with color. Makes a big difference!

Today

Working on improving the Ouroboros 

Cleaning up scene to be more playable

Need alternate ideas on what the eyes littering the snakes could be,,,,

![Untitled](March%2011%20a3e4e0318a5e46d089e6e1b0e895b48d/Untitled.png)

Thinking they are shot off and caught by hanging items. Lat think shooting them off… not sure what yet. But having them caught means you can see where they’re going. Need to reposition them to have it work a bit better

Thiking shape somewhat like an enemy,, but differnet enough to not confuse them. half way submerged in the snake. Use hexagon / ostagon like things here. like a crystal!

Added Sensor Toolkit for enabling / disabling these static enemy shooters

Need to add shooters to other ourobors sections

Prefab painter should be able to do this?

Mobius Tube 7 - Feels too big maybe? Scale down? 

Mobius Tube 6 - also too big? Also getting stuck?

IMPORTANT

Even with all SnakeEYes/Static Shooters inactive - INTENTIONALLY - getting the cannot start coroutine thing and they appear to still be shooting. HOW IS THIS POSSIBLE?

Projectile manager maybe? Investigate

---

## From: March 12.md

# March 12

Adjusted Rotations on static shooters of mobius 2 (first one you play on)

Maybe a longer trail / any trail on the static shot ones would be good? 

Something to showcase their directionality

---

## From: March 15.md

# March 15

Using Claude to improve Crosshair.cs

Didnt work! broke stuff i think! or maybe it was good, hard to tell because other stuff broke.

Likely will give this a second try once I better understand issue with locking proejctiles

Projectile locking working once again, but not sure if it’s fully fixed.

Can define boxcast in crosshair inspector now - makes it easier to see range etc

Figured out A Star Debug messages being A LOT - change logging level in A* object

---

## From: March 16.md

# March 16

Tired to use claude on crosshair again and ran into issues AGAIN

maybe not worth it. 

Will try a more targetted approach

---

## From: March 18.md

# March 18

slowTime of Crosshair has not been assigned 

- visual effect that’s not happening

Got the correct effect in, tweaked a bit, doing ok!

---

## From: March 21.md

# March 21

adjusting infinite prefab static shooters placement to affect rotation of them better

also adjusting generation frequency and placement to generally improve them

Minor improvements, needs more work

Building scene changing into Game Manager singleton class

Learning about Scriptable Objects in doing this 

Figured out tail issues, it had to do with where the player spline controller was being placed. tail adjusted correctly but controller was always being placed at the end + 2. Now this number is adjustable in the inspector

Basic EnemySnakeMidBoss health figured out, with animations when getting hit and scene transition upon death 

Have a method figured out where I can call ‘Switch Scene’ in custom event when final wave occurs from enemy wave spawner, and it will initiate GameManager’s scene switching. 

Should I adjust midboss death so it doesnt call scene switching so directly? Unsure

Likely need to move the appearance of wave # text to the custom event call for a cleaner approach. 

SnakeMidBoss

Started adding attack - setup basics of animation

THinknig about attack patterns for bullet shots…

Use Object Particle Spawner or adapt Uni Bullet Hell into 3D?

Tempted by second solution

Experimenting with that a bit… but it seems like more of a pain. Not well designed. 

OPS may be the better bet. 

Just need the right particle patterns.

---

## From: March 22.md

# March 22

Moving json song structure for fmod into a scriptable obect

should be better for editing - will need to address this per scene

Checking how audio transitions from one scene to another 

It sort of works? But seems to be playing two sections are the same time 

Maybe related to how enemy wave spawner is defining things on new scene load? 

Need to adjus this / make its own musical section / ensure it transitions properly

Privating event calls in Adjust Song Paramters because if the object is persistent across scenes, this is easier way to handle it. 

Lots of work done and now scene changes work! FMOD persists across them

Is there a more elegant way to handle the static koreos in one scene where they’re laid out, versus another where they are static and disabled? I think so, base around how it’s been handled in Ourboros Infinite Track, will likely think of a way 

Not totally working i think… need to address this. Issues in build too

Need to remove enemy basics class, just keep enemy basics setup?

Debugging the lack of movement of A* enemies when loading into a scene

Movement in these fields when when enemy movement working

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled.png)

And this is how the behaviour designer looks during movement

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled%201.png)

But the one that isnt moving looks like this

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled%202.png)

THinks its complete? Likely the issue

Interesting thing is that moving into the scene, the enemies dont move

but if i use R to reload that scene, the enemies move just fine. 

There’s a difference in these operations 

The behaviour operates like it should, not showing complete. 

[BD Tactical Shoot and Scoot - succeeds and stops running?](https://www.opsive.com/forum/index.php?threads/bd-tactical-shoot-and-scoot-succeeds-and-stops-running.9739/)

This was the solution! I solved this once before!!!

Removing Default Behavior game object, as its not really doing anything. It was just finding the player but the enemies can do that themselves, its not a big deal

Important note on behavior designer performance spikes

Pooling the trees may be a way to avoid the spikes

[External Behavior Trees - Opsive](https://opsive.com/support/documentation/behavior-designer/external-behavior-trees/)

Fixed score issues when changing between scenes, maintains score properly now. 

Death of midboss snake now has trasition ot next scene (talks to game manager) in its death

Not sure if i should be using beautify 3, is it helpful? need to assess. may be a burden

SIDENOTE

I need to consolidate these input chekcs

```jsx
private void HandleRewindTime()
{
    if (Time.timeScale != 0f && CheckRewind())
    {
        StartCoroutine(RewindToBeat());
    }
    if (Time.timeScale != 0f && SlowTime())
    {
        StartCoroutine(SlowToBeat());
    }
    if (CheckRewind() && rewindTriggedStillPressed)
    {
        rewindTriggedStillPressed = false;
    }
}
```

---

## From: March 26.md

# March 26

**Journal**

Thinking about how I avoid making music for the game… 

Its more than just procrastination

Generating sounds is easier now than ever. Anybody can make music IMO

But what is the MEANING

This is what drives this game, and why i so rarely work on the music for it.

Because all of these systems, the second to second gameplay, is what drives the sound

and when that becomes clear, the sound follows. 

The sound informs too! But I’m so acquainted with making sounds, it doesn’t need to be actively worked on constantly. I can imagine my way through the part of the audio process. 

This is why I avoid writing music in general. What am I writing about? Its not always words, but a feeling, and general vibe and notion that drives the creation. and through video games and interactivity im looking for new sources of meaning to drive creation, and ultimately the experience. 

This idea also fuels the point of the game. I see rhythm games, and the promise of Rez, and I think - the sounds could be informed with more meaning. Different meanings. More focus. 

More than just easily fading between stems and battle tracks. Though these are useful and effective.

---

## From: March 28.md

# March 28

Need to compare BT current to BT fix to see why visual effects are behaving weirdly

Fixed this! It was the order Buto was renderer. Was kind of cool, worth considering for a level

Still bad in a build and unsure why.

---

## From: March 30.md

# March 30

May need to rebuild volume to get Post Process going

Also removing these settings to see if they make a difference, not currently active but need to revisit

![Untitled](March%2030%205fe3c4b0eb744cb7bb2dcfc6f6b910b0/Untitled.png)

Test Scene Volume in editor

![Untitled](March%2030%205fe3c4b0eb744cb7bb2dcfc6f6b910b0/Untitled%201.png)

In build

![Untitled](March%2030%205fe3c4b0eb744cb7bb2dcfc6f6b910b0/Untitled%202.png)

Removing various fx and checking editor vs build

![Untitled](March%2030%205fe3c4b0eb744cb7bb2dcfc6f6b910b0/Untitled%203.png)

![Untitled](March%2030%205fe3c4b0eb744cb7bb2dcfc6f6b910b0/Untitled%204.png)

---

---

---

## From: March 4.md

# March 4

Working on Infinite track / snake chasing

snake eveballs shooting exist so far

Create basic class for chasing snake - considered MidBoss

Have a callback class on colliders that can be hit. 

They call back to MidBoss with their names

Collider Hit Back’s send the gaemobejct name and the damage amount to the boss script

Some basic implementation of enemy lock on for the call back collider class

Also written into Crosshair so it sees it properly

Not working for lock on, need to take a look at why

---

## From: March 6.md

# March 6

Tim Cain videos

**Design Docs**

- Define Settings, Story, Systems - in that order

Setting

- Succinct - sentence or two
    - Fallout Example - post-apoc but 50s!
        - Simple with a twist

How to make a good setting?

Questions

1. Is it easy to explain to someone in a way the can grasp and understand it?
2. Can you tell an interesting story in that setting?
3. Can you write interesting quests?
4. Is the setting evocative? How does it draw people in? 

[Unity Profiler Walkthrough & Tutorial | Unity](https://www.youtube.com/watch?v=xjsqv8nj0cw)

Curvy Generator causing spikes - may not be great to use, try in isolation

---

## From: March 7.md

# March 7

Code optimization in Crosshair - be wary of breaking things!

Doing this in part to fix Infinite track / Collider Hit Callbacks

Not using A* in Infinite Track example - important to error messahes

Lock on working in Infinite Track if the object is on the Enemy Layer and has the Enemy tag. 

But Enemy layer seems invisible or obscured? Need to look into this

Enabled Enemy layer on camera - see what that breaks!

LockedStatus set to false on colliderhitcallback and enemybasicsetup when they get hit

This may break something, need to verify

Also I want to lose the lock on after an enemy gets hit. I think this occurs at two levels

1 - Crosshair loses it’s lock on a moment after shooting at it

2 - once the enemy is hit with a lock on bullet, the lock on anim goes away

These should happen in conjunction with each other - i think?

if crosshair loses it’s lock on, it’s because the enemy got hit by a locked on bullet

Adding this into cycle of ProjectileStateBased - ProjecitleManager - Crosshair

— Upload to Github here

Now considering - i want lock on success to be dependent on how closs your crosshair is to the target when you fire. if you’re relatively close then it will be a success - i think air combat games have a version of this at times

Bullet speed and Rotation speed appear to be the limiting factors in hitting the target

Currently have predictive rotation effect by the crosshair accuracy, not sure how effective this actually is but its implemented!

Havce basic animations for getting hit happening in the Infinite Snake, butthe inherent problem with the scale and positioing seems messed up, and the animation behaves weird. Need to fix at the root of it! 

See if original snake has this problem 

Added dotween shake to elements getting hit 

Tbh not noticeable - emphasize or rethink this

---

## From: March 9.md

# March 9

Using Claude to reorganize some classes

 Player Movement, 

Removed instance from Crosshair

Due to animation issues and Umodeler issues, Creature Testing 2 is a thing

DO NOT upgrade the unity version for this creature testing project if you dont wanna break everything!!

Snake with better anims is in main project now. Still need to do mesh edits to make it more unique

Need to bring over eyes and other aspects for infinite track section

---

## From: May 1.md

# May 1

Really need to go minimal viable product with my game - make a place - do it!

---

## From: May 10.md

# May 10
 2 | 
 3 | Added Toon kit manager to scene to control toonkit ambient light and shadows effectiveness
 4 | 
 5 | Likely need to prefab this
 6 | 
 7 | Trying to debug Y-axis rotation issues
 8 | 
 9 | Player Movement is playing some part? May need to disable what it does 
10 | 
11 | But it doesnt seem to be affected things… Seems any kind of ground fitting script, wether A* or actually Ground Fitter, always causes the issue.

---

## From: May 11.md

# May 11
 2 | 
 3 | Added Keijiro Duotone to the project - definitely possible uses in places for this
 4 | 
 5 | Changed reticle spin to something not koreographer based - just hard coded tempo based
 6 | 
 7 | Easier, simpler, good enough for what it is - for now and probably the future
 8 | 
 9 | Wave delay before switch scene, may want to do it, but some technical things to solve if so. Need to re enable appropriate things, its a special transition. for example, player character. 
10 | 
11 | WaveCustomEventSorter is what handles a change to the next scene
12 | 
13 | Need to ensure on scene load that certain elements are active. maybe a scriptable object that defines this - may be more than one, since some scenes may have different requirements

---

## From: May 13.md

# May 13
 2 | 
 3 | Things to try / Implement!
 4 | 
 5 | [May 1](May%201%20038edfee9455402aa54b8dce20ccf465.md) 
 6 | 
 7 | Is this an issue? Investigate
 8 | 
 9 | ```jsx
10 | warning: You are currently using Visual Studio 2019 to compile and link C++ code. It is recommended that you upgrade to Visual Studio 2022 for better performance.
11 | UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)
12 | ```
13 | 
14 | Tracking chart
15 | 
16 | | Task | Details | Status |
17 | | --- | --- | --- |
18 | | New UI for Projectile and Enemy Locks | Current one that follows the reticle is not very useful and not working properly |  |
19 | | Refine Reticle Control and Enemy / Projectile Lock On | Doesn’t feel as good as it should, hard to lock on to enemies |  |
20 | | Play with Parry Feature | Doesn’t feel responsive, logic seemed off when trying to use it, may need some rethinking |  |
21 | | Motion Extraction Effect | Figure this out! |  |  |
22 | | May 1st Ideas Implementation | Go through May 1st Ideas and write up implementations |  |  |
23 | | Make Enemy Death Effect More Visible | Looks like its happening in scene view but barely see it in Play mode |  |
24 | | Motion Extraction Effect | Figure this out! |  |
25 | | A* or Ground Fitter Fix | Y rotation issues - disabled for now |  |
26 | | Skip Between Scenes | Previous method didn’t work and is removed |  |
27 | | Toonkit Manager Adjustments | Adjust shadows and light as appropriate |  |
28 | | Keijiro Duotone | Look at how to use this - test as scene transition effect? |  |
29 | | Reticle Spin | Not tied to koreographer - look at if this is ok implementation |  |
30 | | Game Manager Controls Music Transition | Need to build this out more - tied to scene list? |  |
31 | | Game Objects Inactive on Scene Load | Need to find proper solution for this |  |
32 | | Visual Studio 2019 Warning | Upgrade to Visual Studio 2022 for better performance |  |
33 | 
34 | Idea 
35 | 
 36 | Time / music / health
 37 | 
 38 | All the same
 39 | 
 40 | Music dies when time stops!
 41 | 
 42 | Build curiosity to make level repayable
 43 | 
 44 | X # of secrets found 
 45 | 
 46 | What are the rewards for replaying a level?
 47 | 
 48 | Make it so game plays has an Obvious approach + many non obvious options
 49 | 
 50 | How can there be endless mastery? Think devil daggers and others 
 51 | 
 52 | Speed running is a popular approach but how does that align with my game at all?
 53 | 
 54 | Need to think through how to better intermingle all my game mechanics
 55 | 
 56 | Instead of 5 different options, have them exist as multipliers for each other to create whole new actions 
 57 | 
 58 | Could I do a pacifist run?
 59 | 
 60 | Watch people play a game to see how they make their own fun

---

## From: May 14.md

# May 14
 2 | 
 3 | [Developing 'Hi-Fi RUSH' Backwards and Finding Our Positive Gameplay Loop](https://www.youtube.com/watch?v=pG4UxqRMNX0)
 4 | 
 5 | Insights
 6 | 
 7 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled.png)
 8 | 
 9 | Keyword Rhythm Action → What does that look like as definite gameplay?
10 | 
11 | Prototype video shows action and effects - consider how this and tutorial introduce mechanics and gameplay. 
12 | 
 13 | **Design Fundamentals**
 14 | 
 15 | - think - you’re the star of a music video!
 16 | 
 17 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%201.png)
 18 | 
 19 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%202.png)
 20 | 
 21 | Assume players will not hit the beat - being close enough is good enough for the sync effects
 22 | 
 23 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%203.png)
 24 | 
 25 | Animation speeds and hits are adjusted based on input time - slower or faster
 26 | 
 27 | **Positive Game Loop**
 28 | 
 29 | Interpolation creates a positive feedback loop naturally
 30 | 
 31 | Feeling like you hit the beat feels good
 32 | 
 33 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%204.png)
 34 | 
 35 | Playing to the music feels better, but if you miss rhythm you’re not really punished
 36 | 
 37 | Makes this more accessible
 38 | 
 39 | Feel like you are good at music even if you aren’t
 40 | 
 41 | **Make success easy to understand**
 42 | 
 43 | Make actions feel like they’re part of the song - HiFi Rush has a big HEY sound if you nail an attack
 44 | 
 45 | This is also done with dashing and a hi hat sound effect when its done on the beat
 46 | 
 47 | Showing the player that there is room for improvements 
 48 | 
 49 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%205.png)
 50 | 
 51 | Developing the game in reverse from the impact!
 52 | 
 53 | Thinking backwards
 54 | 
 55 | on a Micro and Macro level
 56 | 
 57 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%206.png)
 58 | 
 59 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%207.png)
 60 | 
 61 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%208.png)
 62 | 
 63 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%209.png)
 64 | 
 65 | Did a bunch of edits to fix scene transition , music choice for scene switching, and other things 
 66 | 
 67 | Playtesting
 68 | 
 69 | - feels a bit slow in general?
 70 | - Mechanics need to interact better with each other
 71 |     - time glitch
 72 |         - could highlight where the enemies are located
 73 |         - could allow you to move faster or slower while enemies move differently?
 74 |             - or maybe just enemy shot bullets are slowed drastically
 75 |     - slow time
 76 |         - allows you to move backwards?
 77 | - Enemy types on wave scene 3 need adjusting, need more projectiles flying around
 78 | 
 79 | Making adjustments to rewind and slow to beat speeds and how they affect the spline.
 80 | 
 81 | Definitely something good within this, tweak a few values to find better balance.
 82 | 
 83 | Likely need to speed up player character and some other things, more intensity
 84 | 
 85 | Cant make player model rewindable or seems to fall out of camera view. probably just need custom animation triggers for the movement changes
 86 | 
 87 | It appears bullets dont need their own local clocks so i’ve removed it, need to keep watching to make sure this doesnt break anything though, seems like it already has, so i put it back on but made it additive
 88 | 
 89 | Added this to update method of projectiles in attempt to get them to respond to time changes more effectively, but doesnt appear to be working - look more into the clock part
 90 | 
 91 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%2010.png)

---

## From: May 15.md

# May 15

Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Ouroboros as a pass on this

- remove many top level elements, keep  kick / snare pattern?

Creating a plan using chat gpt and past couple logs / aggregations 

### **High Impact / Easy**

1. **Upgrade Visual Studio 2019 to 2022**: Resolves performance warnings.
2. **Add Quit to Desktop**: Already done, but high impact for user experience.
3. **Adjust Prediction Values on Projectiles**: Improvements noted, continue progress.
4. **Improve Enemy Death Effect Visibility**: Minor tweak with a significant impact on gameplay feedback.
5. **Increase Player Speed**: Quick tweak to make gameplay feel faster and more intense.

### **High Impact / Difficult**

1. **Refine Reticle Control and Enemy/Projectile Lock On**: Key to core gameplay; needs significant rework.
2. **New UI for Projectile and Enemy Locks**: Essential for player feedback, requires redesign.
3. **Snake-like Enemy Implementation**: Complex, but adds unique gameplay mechanics.
4. **Play with Parry Feature**: Needs rethinking to improve responsiveness.
5. **Integrate Mechanics for Combined Actions**: Ensures mechanics interplay well together.
6. **Rework Bullet Clocks for Time Changes**: Critical for time mechanics to function properly.
7. **Time Glitch Enhancements**: Highlight enemy locations, adjust player/enemy speeds.
8. **Slow Time Enhancements**: Allow backward movement, adjust time effects.

### **Low Impact / Easy**

1. **Make Enemy Death Effect More Visible**: Simple visual adjustment.
2. **Toonkit Manager Adjustments**: Minor visual tweaks.
3. **Keijiro Duotone for Scene Transitions**: Test and potentially implement visual effect.
4. **Game Objects Inactive on Scene Load**: Find a proper solution, minor impact but necessary for consistency.
5. *A or Ground Fitter Fix*: Fix Y rotation issues, minor but required for functionality.
6. **Particle Effects Visibility**: Ensure particle effects are more exaggerated.
7. **Skip Between Scenes**: Fix method for scene transitions.

### **Low Impact / Difficult**

1. **Motion Extraction Effect**: Needs significant effort to figure out and implement.
2. **Ricochet Dodge Feature Testing**: Requires testing and refinement, but lower overall impact.
3. **Enemy Waves Adjustment**: Fine-tune enemy types and projectiles, requires playtesting.
4. **Scene Transition and Music Choice Edits**: Ongoing adjustments, minor but important for polish.

---

### **Implementation Plan**

**Phase 1: Immediate Improvements (High Impact / Easy)**

1. Upgrade Visual Studio 2019 to 2022.
2. Improve enemy death effect visibility.
3. Adjust prediction values on projectiles.
4. Increase player speed.
5. Add quit to desktop (verify implementation).

**Phase 2: Core Gameplay Enhancements (High Impact / Difficult)**

1. Refine reticle control and enemy/projectile lock on.
2. Design and implement a new UI for projectile and enemy locks.
3. Play with parry feature to improve responsiveness.
4. Integrate mechanics for combined actions to enhance gameplay depth.
5. Rework bullet clocks to ensure proper time mechanics.
6. Implement snake-like enemy idea with tail animator.
7. Enhance time glitch and slow time mechanics for better player interaction.

**Phase 3: Visual and Functional Tweaks (Low Impact / Easy)**

1. Make enemy death effect more visible.
2. Adjust Toonkit Manager for shadows and light.
3. Test and implement Keijiro Duotone for scene transitions.
4. Ensure game objects are active on scene load.
5. Fix A* or ground fitter Y rotation issues.
6. Exaggerate particle effects for better visibility.
7. Fix method for skipping between scenes.

**Phase 4: Detailed Testing and Refinements (Low Impact / Difficult)**

1. Figure out and implement motion extraction effect.
2. Continue testing and refining the ricochet dodge feature.
3. Fine-tune enemy waves and projectiles for better gameplay balance.
4. Continue scene transition and music choice edits for a polished experience.

WORK DONE

OUROBOROS

Didnt totally touch the above list, but did dig in on sounds and approach

pulled back to minimalist sound a bit more, can see a better direction here

then synced that with vfx explosion event i can place around the scene

makes more sense i think - sound and visual seem more in sync

A better direction forward

THings that occurs to me that cross over with above list

make character a bit faster

balance enemy speed and their bullet speeds

continue on path of adjusting time based effects so they are better to interact with / more desirable

IMP need to figure out how to reverse the music sounds as well as the drums

---

## From: May 16.md

# May 16

Using Cursor on this Notion - looking for insights

Seems a bit basic - not sure its working all that well

---

## From: May 17th.md

# May 17th

Look at some of the Jason Booth optimizations to apply to my project. 

Adjusting update internval of behavior manager (I think? may need to verify)

Have it running half speed now, is this more performant?

Also applying some performance optimizations to Projectile State Based, this is based on seeing spikes due to the On Enable method in the Profiler. 

Careful with materialInitialized flag that’s been added in ProjectileStateBased, may cause an unexpected error 

May 18th

inkblot not working, should be in game now, just bring it in agian and koreo it. 

may need to import new vfx librayr once again from occa

---

## From: May 2.md

# May 2

Small things to address before digging into May 1st Notes

Added Ricochet Dodge this replaces dodge

Adding basic particle effect for this - something i will likely replace later

Need to pick a day to sort through particle effects!

Parry still exists in project, likely just not very functional 

These are the details of a projectiel that refuses to be ricochet dodged

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled.png)

This one does not have that issue

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%201.png)

Appears to be nothing differetn!

Here is a bullet i just ricocheted

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%202.png)

Nothing obvious in any of that

trying different settings

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%203.png)

Seems better with new values!

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%204.png)

Keep playing with this

---

## From: May 21.md

# May 21

Minor VFX and logic updates

Inkblot working now

---

## From: May 22.md

# May 22

Added rate-limiting to OnLock and OnLockEnemy methods to help improve performance

Attempt at collective project notes

[2022](May%2022%20d0383e67d7af4be28bed0b866cd41c5c/2022%20b590717d1f9a4660b051b69346cb8224.md)

[2023](May%2022%20d0383e67d7af4be28bed0b866cd41c5c/2023%20ba07c726a5a74ff1933b5909b87c321e.md)

[Data Analysis](May%2022%20d0383e67d7af4be28bed0b866cd41c5c/Data%20Analysis%200b870f293c1847e7af486adbe700017a.md)

[Code Documentation](May%2022%20d0383e67d7af4be28bed0b866cd41c5c/Code%20Documentation%20f78fce8267f54171b0e976a6c09165d2.md)

---

## From: May 23.md

# May 23

Fixing the volume control in the system menu. Works with FMOD now. 

Need to define categories better though - do i like the groups as they currently exist? 

Master, UI, Player, Enemies

Looking into how Reach works with controls and rebinding.

Still not quite sure how to get that working properly 

Got the proejctile radius feature working, its LifetimeExtended

adds to lifetime of projecitles within certain radius of player (currently 25)

seems to have worked to stop dissolve 

Need better player hit effect from projectile, feels too quick and non-impactful

Added on - MIXED opion on it, but its an improvement

Prefab effect thats on the player health, so plays from pool when player is hit

Make sure death effect from projectile manager is being returned to pool 

Infinite Spline

Snake shoots in a rhythm

Pow pow pow 

pow pow

QUICK - requires a dodge

MAYBE first snake like this, just steady and fast like it currently is

second like this - more of a pattern

maybe a third like this? Or double snake rotate section

have different patterns occur with double snake

Mechanics

Think about how these interact with each other

- can slow time initiate doppler on projectiles?

---

## From: May 25.md

# May 25

New script - Scene Load Activation Controller

This is for any element that needs to be deactivated when a scene is transitioned into, but want it active if im in editor working on the scene. Useful!

Parent constraint - useful tool! Have things loosely follow a parent! 

Adding sound for when snake shoots

Adding fx for this too. also added an FX that projecitle manager attaches to the projectiel if its enemy shot, 

didnt like what i had though, so skipping that, will slot in a better vfx on the projectile later

integrate stylized anime FX

grab some enemy assets from uac

---

## From: May 27.md

# May 27

Tried new graphics in infinite snake

Looking interesting, may be a thing

broken scene transitions? Was this recent or just unnoticed?

Downloading old version to see

---

## From: May 28.md

# May 28

Cannot seem to fix issue with scene transitions. need to figure out this before next steps!

---

## From: May 3 4 5.md

# May 3/4/5
 2 | 
 3 | Sorting and aggregating issues from last couple days
 4 | 
 5 | | Task | Details | Status |
 6 | | --- | --- | --- |
 7 | | New UI for projectile and enemy locks | current one that follows the reticle is not very useful and not working properly |  |
 8 | | Refine reticle control and enemy / projectile lock on | Doesn’t feel as good as it should, hard to lock on to enemies |  |
 9 | | Play with parry feature and refine or remove | Doesnt feel responsive, logic seemed off when trying to use it, may need some rethinking |  |
10 | | Motion Extraction Effect | Figure this out! |  |  |
11 | | May 1st Ideas Implementation | Go through May 1st Ideas and write up implementations |  |  |
12 | | Make enemy death effect more visible | Looks like its happening in scene view but barely see it in Play mode |  |
13 | | Motion Extraction Effect | Figure this out! |  |
14 | | A* or Ground Fitter Fix | Y rotation issues - disabled for now |  |
15 | | Skip Between Scenes | Previous method didn’t work and is removed |  |
16 | | Toonkit Manager Adjustments | Adjust shadows and light as appropriate |  |
17 | | Keijiro Duotone | Look at how to use this - test as scene transition effect? |  |
18 | | Reticle Spin | Not tied to koreographer - look at if this is ok implementation |  |
19 | | Game Manager Controls Music Transition | Need to build this out more - tied to scene list? |  |
20 | | Game Objects Inactive on Scene Load | Need to find proper solution for this |  |
21 | | Visual Studio 2019 Warning | Upgrade to Visual Studio 2022 for better performance |  |
22 | 
23 | According to f impossible dev - spine animator is a better tool for snake like behaviour - we’ll see!
24 | 
25 | [【Ground Fitter (free)】- Fit your dynamic objects to ground](https://forum.unity.com/threads/ground-fitter-free-fit-your-dynamic-objects-to-ground.541434/page-2#post-5549257)
26 | 
27 | NOTE: Ground fitter not totally working for player character? TEST

---

## From: May 7 8 9.md

# May 7 / 8 / 9
 2 | 
 3 | Didn’t get to work on this for more than a few minutes, but fixed a Reach UI error 
 4 | 
 5 | Need to assess UI - How should it exist within each scene? Prefab? Singleton?
 6 | 
 7 | Load a scene additively as pause menu? Look into this - interesting 
 8 | 
 9 | [Unity - Scripting API: SceneManager](https://docs.unity3d.com/ScriptReference/SceneManagement.SceneManager.html)
10 | 
11 | also
12 | 
13 | “My usual workflow is that I'll have a scene that loads in the beginning with a single GameManager object, with an attached GameManager script that is responsible for handling things like scene transitions and scene logic, as well as any other things that are useful to have in a singleton. Then I set that object to not destroy on load. Any UI elements that I want to exist in all of my other scenes are simply parented to that Gamemanager object (in a canvas of course), so that they are always accessible. If you want, you can then add some logic for not having your UI available in certain instances (say you don't want your player to pause the game on the title screen)”
14 | 
15 | Try parents Timekeeper, UI, and such to GameManager, and as it moves along scenes, see if things still work? 
16 | 
17 | May 8th
18 | 
19 | Trying the above idea for UI and other things to transition between scenes
20 | 
21 | Not working, may be a better time for it
22 | 
23 | Still encountering Text Mesh Pro Issues, investigating. Seems like it’s not properply installed, strangely’
24 | 
25 | Fixed! It seems
26 | 
27 | Trying UI stuff again now and debugging
28 | 
 29 | Using Persistent Object script to bring forward to other scenes
30 | 
31 | Not working it seems. ALso parenting things to the game manager breaks the game in unexpected ways. Need to analyze or just be careful about this
32 | 
33 | Had issues with UI being a prefab, unsure why… need to check this out again too. 
34 | 
35 | Doing it and it mostly seems to work? This may be the best method for unified UI among multiple scenes - will continue testing but moving forward with this! :)
36 | 
37 | Also trying to rewrite my reload scene script - want thing working better - fails currently, may not be easily solvable. May not be worth it!
38 | 
39 | Rough implemention integrated
40 | 
41 | Added next scene to game manager, rough implementation, seems to be skipping ahead too much 
42 | 
43 | Need to get out of the bug trench and into gameplay trench a bit more!
44 | 
45 | May 9th
46 | 
47 | Also fixed Player Movement issue with double rotations occuring, debounce in the methods has fixed this. Adjust timing of this if necessary

---

## From: Nov 11 12th Bug Testing.md

# Nov. 11/12th Bug Testing

**Restart - does it work?**

- music not properly restarting, editing and testing

Now working within first scene, follow up scenes seem to work but need further testing

**Code to address this may have highlighted / introduced wave transition error**

- Start4 not found by FMOD - trying to fix this
- Need better way to handle start sound of waves / levels - investigate this

**locking must be reset between waves and scenes**

- made code adjustments to account for this

**Fix broken effects** 

- Scene start effects
- Ricochet effect
- Glitch times efectsd

**Mild Improvements - probably need to replace many of them**

Need to make incoming projectiles that will hit player, more obvious or something, need a better chance at shielding these

Scene 1

- Need to increase ability for camera to look up and down
    - try to do this without clipping things
- Clamping in ShooterMovement script influecnes this - need to adjsut and potentially adjust Cienmachien camera confiner to be adjusted depening on reticle position as well.
- What enabled clamping on start? this is a a bit vague, somethng does i on scene star

Increase clamped limits but also adjust confiner? Need to investigate

Somewhat working - test it

- need to adjust when facing backwards - player is out of screen
- needs refinement on sphere position vertically.

look at waht camera is doing in scene 

- is it rotating properly? could it rotate more? is this the issue?

TEST TEST AND OBSERVE

(can revert shooter movement if needed)

Nov. 14th

Dont think adjust Camera Restriction is doing anything good

Problems were in Shooter Movement, rotation restrictions are tehre

MAJORLY increased now

Also added dead zone that will haveto be refined

Scene 2 

- Snake not gfetting hurt byu projectiles - why?
    - Are they shooting properly?

Scene 3 

Enemy Shoot Manager check music look state is taking 85 !!! ms !!!

- must fix this

Look of static projectile shots may also be shooting too much

- look into shooting rate

Need to edit my VFX transparency effect present on meshes to use the actual in scene mesh and not a reference to the model - maybe this will address problem with positioning

---

## From: Nov 11th.md

# Nov. 11th

Here's a structured approach to evaluate your rail shooter:

### **1. Gamefeel Analysis**

- How responsive are the controls?
- Is there satisfying feedback on hits/misses?
- Does the camera movement feel natural?
- Are hitboxes and aim assist tuned properly?

### **2. Tension Management**

- Is there a good rhythm between high and low intensity moments?
- Do players have enough time to react?
- Are there meaningful consequences for failure?
- Does difficulty scale appropriately?

### **3. Player Investment Loop**

- What rewards keep players engaged?
- Are there meaningful progression systems?
- Do players feel a sense of mastery over time?
- Are there short and long-term goals?

### **4. Engagement Vectors**

Score your game 1-5 in each area:

- Mechanical satisfaction (shooting feel)
- Challenge balance
- Reward systems
- Progression clarity
- Visual/audio feedback
- Player agency within constraints

### **5. Core Loop Analysis**

Evaluate each phase:1. Player Input

- Is aiming intuitive?
- Do controls feel responsive?
- System Response
- Is feedback clear and satisfying?
- Are hit reactions appropriate?
- Player Reward
- Are achievements meaningful?
- Does success feel earned?

---

**GameFlow Analysis**

Score each element 1-5:

- Concentration
- Is attention properly directed?
- Are distractions minimized?
- Is workload appropriate?
- Challenge
- Does difficulty scale smoothly?
- Are skills properly tested?
- Is failure informative?
- Skills
- Are core mechanics clear?
- Is mastery achievable?
- Do players feel improvement?
- Control
- Are inputs responsive?
- Is feedback immediate?
- Do players feel agency?

### **2. First Hour Experience**

Evaluate:

- Holdouts
- What keeps players engaged early?
- Which mechanics show promise?
- What creates intrigue?

2. Information Flow

- How are mechanics introduced?
- Is progression clear?
- Are goals well communicated?
- Early Engagement
- When do players first feel mastery?
- What creates early wins?
- Where do players struggle?

### **3. Flow State Analysis**

Check for:1. Clear Goals

- Are objectives obvious?
- Is progress measurable?
- Do players know what's next?
- Immediate Feedback
- Are hits/misses clear?
- Do actions have clear results?
- Is improvement trackable?
- Challenge/Skill Balance
- Does difficulty match player skill?
- Are there appropriate peaks/valleys?
- Is frustration minimized?

[Nov. 11/12th Bug Testing](Nov%2011th%2013bffc7f811f80528383c1aac2186bb7/Nov%2011%2012th%20Bug%20Testing%2013cffc7f811f805a88f8d78308c37761.md)

Made Reticle slightly bigger to see if that feels better

- Was comparing with Rez

---

**Game to check out**

- Omega Boost
- Danger Forever
- RayStorm
- RayCrisis
- Thunder Force V
- G-Darius
- Soukyugurentai / Terra Diver
- Zero Gunner 2
- Shienryu Explosion
- Zaxxon Motherbase
- Under Defeat
- Xevious 3D
- Brave Blade
- Nebulas Ray
- Soulstar
- TOP NEP
- Missile Dancer 2
- Night Striker S
- Galaxy Force  2
- Child of Eden
- Xevious Resurrection
- **ALLTYNEX Second**
- XTOM 3D  [https://www.myabandonware.com/game/xtom-3d-g0a](https://www.myabandonware.com/game/xtom-3d-g0a)
- Silpheed remake

[https://www.youtube.com/watch?v=SxNQSXtaljg](https://www.youtube.com/watch?v=SxNQSXtaljg)

---

## From: Nov 18th.md

# Nov. 18th

```csharp
    public override void OnTriggerEnter(Collider other)
    {
        GameObject hitObject = other.gameObject;

        if (hitObject.CompareTag("Enemy"))
        {
            // Handle enemy collision
            HandleCollision(hitObject);
        }
        else
        {
            Debug.Log($"Projectile hit non-enemy object: {hitObject.name}");
            _projectile.gameObject.SetActive(false); // Deactivate the projectile
            ProjectilePool.Instance.ReturnProjectileToPool(_projectile); // Return it to the pool
        }
    }

```

PlayerShotState is deactivating projectiles too frequently, likely waht we need to do is dsiable certain collision layers on the projectile - this may have been messed up in the switch to Unity 6.

For now, i’ve commented out the deactivation and return to pool when colliding

Trying to optimzie the infinite track generation, implement a loadignm screen that infintie ouroboros uses during intiial optimizaiton

Trying to figure out the looping functioanlity for locking mode - trying Destiantion Marker named ‘LoopPoint’ for Scene 3 - where the performance is heavily impacted by previous implementation of couroutines

NOT WORKING

Need to rethink this - idea is pretty good, have the shooting change in lock state, or something like that, just need to implement it smarter

---

## From: Nov 19th - Organize.md

# Nov 19th - Organize

- Fix alternate loop firing (CheckMusicState)
    - Just know the lock state - no need to talk to FMOD - its not central!
        - but how do we know the repeat time? integrate fmod callback system.
        - markers that delineate loop point + knowing if we are in lock state
- Projectile Collision layers
    - Need to define these and their behaviour
    - Code currently commented out for deactivate/return to pool on collision

Fix broken effects

- visual mainly - better replacements?
    - Scene start effects
    - Ricochet effect
    - Glitch times efectsd

Starting Sound broken

- Likely need better implementation of this occuring

Better visibility on Incoming Projectiles that are about to hit player

- Not really sure what to do here??

Shooting rate of static enemy shooting projectiles may be glitchy

- two at once, etc. May need to refine how this works

VFX SDF not working properly for all meshes

- need to rebuild this so it works or take another approach

VSYNC forces a set framerate (60fps?) this needs to be fixed or investigated

General FMOD implementation optimizations

- Need to keep track, possibly funnel these into an FMOD Manager to track instances
- Really don’t need a lot of instances of many things, we’re trying to reduce this

Need to fix lighting in several stages

Need to finish the explode enemy

Need to debug all audio cutting out but then returning

- Whats causing that to happen?

Streamline renderers, such as renderontop etc

May need to investigate performance impact of Enemy AI

Should projectile system be made into ECS? Unsure. Recommendations are:

- Move physics calculations to jobs using Unity.Mathematics
- Implement spatial partitioning for collision detection (as mentioned in "Insanely FAST Spatial Hashing" document)
- Use object pooling (which you've already started with ProjectilePoolType)
- Batch similar operations together

Specific implementation

1. Create a ProjectileUpdateJob for movement calculations
2. Use NativeArrays for storing position/rotation data
3. Keep MonoBehaviour for component references and Chronos integration
4. Use IJobParallelFor for processing multiple projectiles simultaneously

**Recommendation:** Start with a hybrid approach:

1. Keep your current MonoBehaviour structure for Chronos integration
2. Move performance-critical calculations (movement, targeting) to Jobs
3. Use Burst compilation for these jobs
4. Implement spatial partitioning for collision detection
5. Consider batching similar operations

Reduce number of shaders in use to optimize build in general

Fun Ideas

- FMOD plugin development
- GPU Instancer Pro for asteroids or other objects
- Implement bit crusher better to exclude elements
- Item Pickups
    - Dodge to grab?
- Final Boss!!
- Do I want a new highlight / outline solution?
- Idea
- Reticle speed slows down for easier aiming
    - This comes from Don pachi slow down with big laser vs regular shots and you move faster
- Thinks of the reticle as a shmup ship? In this context, what can be done differently?
- Use a lock on like layer section with wild missile curvy lasers as fx? Have some projectile fx packs that would work well for this I think
    - See also zero ranger / crimson clover
- Maybe implement the power up after shots mechanic that kill knight uses? Think about it or similar things

---

## From: Nov 20th.md

# Nov. 20th

```jsx
- Fix alternate loop firing (CheckMusicState)
    - Just know the lock state - no need to talk to FMOD - its not central!
        - but how do we know the repeat time? integrate fmod callback system.
        - markers that delineate loop point + knowing if we are in lock state
```

Addressed this with a triplet koreography track

need to think if this is how i still want to go, but it works 

- alternate system was fmod callbacks, but that breaks koreographer

also split shooting projectiles over several frames, majorly reduces performance spike

Also reworked audio system in Projectile Audio Manager for performance benefits

Tried implementing spatial hashing quickly, no success. may not be necessary - consider other bottleneck first

---

## From: Nov 27th.md

# Nov. 27th

Rename Warp effect to appropriate Datamosh effect

- think about how it will be used
    - targetting? make enemies more apparent through UI / highlights?
        - anything else?

Scene 5 - fix orientation of snakes - very annoying being 45 degrees off

What kind of boss? Look to Rez, Panzer Dragoon, and more

---

## From: Nov 30th.md

# Nov 30th

**Organizing Links**

[Digital Divinity](https://restofworld.org/series/digital-divinity/)

[Game UI Database - Mirror's Edge](https://gameuidatabase.com/gameData.php?id=815)

Events

[Indie Superboost: The Do's and Dont's of Self Publishing w Lee Vermeulen](https://www.eventbrite.ca/e/indie-superboost-the-dos-and-donts-of-self-publishing-w-lee-vermeulen-tickets-1079697216439?aff=ebemoffollowpublishemail&ref=eemail&utm_campaign=following_published_event&utm_content=follow_notification&utm_medium=email&utm_source=eventbrite)

[Awkward First Date Tours | Art Gallery of Ontario](https://ago.ca/events/awkward-first-date-tours)

[AV Live: Future Underground | Art Gallery of Ontario](https://ago.ca/events/av-live-future-underground)

Unity Tech Tutorials

[Build Your Own Dependency Injection in less than 15 Minutes |  Unity C#](https://www.youtube.com/watch?v=PJcBJ60C970)

[PracticAPI](https://www.youtube.com/@practicapiglobal/videos)

[Code Like a Pro: Refactoring to Patterns!](https://www.youtube.com/watch?v=OnlR4TczIPY)

**VFX**

[VFX Graph Learning Templates | Tutorial](https://www.youtube.com/watch?v=DKVdg8DsIVY)

[EricWang(Unity VFX Artist) - BOOTH](https://ericwang.booth.pm/)

[Eric Wang_VFX Artist](https://www.youtube.com/@EricWang0110)

[Mirza Beig on Twitter / X](https://x.com/TheMirzaBeig/status/1836885186560118940)

[I made this waterfall effect without any textures. It inherits colours from animated lights.](https://www.reddit.com/r/Unity3D/comments/1g3dk31/i_made_this_waterfall_effect_without_any_textures/)

[Create advanced visual effects in VFX Graph: Decals | Unity](https://www.youtube.com/watch?v=nqhkB8CG8pc)

Design Knowledge

[Featured Blog | The art of game balance: evolution](https://www.gamedeveloper.com/design/the-art-of-game-balance-evolution)

[Naming Things](https://www.namingthings.co/)

VFX Inspo

[Ishmael - The Merciful One - Beta Movesets Showcase - Punishing Gray Raven CN](https://www.youtube.com/watch?v=BBRKQM-JCiA&t=116s)

Assets

[https://github.com/EduardMalkhasyan/Serializable-Dictionary-Unity/releases](https://github.com/EduardMalkhasyan/Serializable-Dictionary-Unity/releases)

[https://www.flexalon.com/templates?utm_source=fxmenu](https://www.flexalon.com/templates?utm_source=fxmenu)

[https://github.com/johanhelsing/UniSdf](https://github.com/johanhelsing/UniSdf)?

[https://github.com/DumoeDss/AquaSmoothNormals](https://github.com/DumoeDss/AquaSmoothNormals)

Black Friday Deals

[Galaxy Materials (Skybox Update)](https://assetstore.unity.com/packages/vfx/shaders/galaxy-materials-skybox-update-191773)

[Stylized Shoot & Hit Vol.1](https://assetstore.unity.com/packages/vfx/particles/stylized-shoot-hit-vol-1-216558)

[Creative Lights](https://assetstore.unity.com/packages/vfx/particles/fire-explosions/creative-lights-282858#reviews)

[GIBLION 2024: Anime-NPR-Toon Framework](https://assetstore.unity.com/packages/tools/level-design/giblion-2024-anime-npr-toon-framework-299579)

[Sci Fi Hologram Shader](https://assetstore.unity.com/packages/vfx/shaders/sci-fi-hologram-shader-170106)

[Stylized Shoot & Hit Vol.2](https://assetstore.unity.com/packages/vfx/particles/stylized-shoot-hit-vol-2-222939)

https://assetstore.unity.com/publishers/60810

Tech Knowledge

[Unity Custom SRP](https://catlikecoding.com/unity/custom-srp/)

Render Graph

[Video Conferencing, Web Conferencing, Webinars, Screen Sharing](https://unity3d.zoom.us/rec/play/7RuwCLk-1B8xnUjpUzpPRqfzkM_745tUQaikPJHDui3CBizLG-yBPJ_r1_uRfVaEScNRBHipqx-eFxgk.NsemXUQjLJVwDZvr?canPlayFromShare=true&from=share_recording_detail&continueMode=true&componentName=rec-play&originRequestUrl=https%3A%2F%2Funity3d.zoom.us%2Frec%2Fshare%2FiOwgx3jdHqpgG62-GN4Mhp0ZRTyoqMyP_O4MvuyePD2YH_sxAor7dAoGlqTm9ZTm.iGnJ6Xwi1ftRkcqb)

Procedural 

[Procedurally Generating plant stems](https://www.fraculation.com/blog/trunk-generation/)

Work - Osler

https://www.reddit.com/r/PowerApps/comments/1gtw7ul/admin_access_best_practice/

https://www.youtube.com/playlist?list=PLcwrIWK7WBcRHasuKkgh0BPySO9vuD_wX

https://www.reddit.com/r/PowerPlatform/comments/1guineg/road_to_power_platform_developer_questions/

https://pragmaticworks.com/resources/cheat-sheets

https://pcf.gallery/sendgrid-emailer/

https://www.reddit.com/r/PowerApps/comments/1gmcqq8/how_to_learn_advance_concepts_in_powerapps/

https://www.youtube.com/@powerdynamite365/videos

https://www.youtube.com/watch?v=vTraPvXMvRo

https://www.reddit.com/r/PowerApps/comments/1gu2z7w/courses/

[Codebase Restructure](Nov%2030th%2014effc7f811f80c0b45fd3b1a40c574d/Codebase%20Restructure%2014effc7f811f801688fbfa0faeb9dc2e.md)

---

## From: Nov 4th.md

# Nov. 4th

Was trying to build an ECS effects system to replace particles, unsuccessful. Need to look into this further. 

Can i build exactly this?

https://antoinefougea.com/posts/unity-ecs-exploration-making-a-particle-system-part-1/

May be unnessecary - will a efficient use of VFX Graph solve this problem? 

IDEA -  one instance triggered by an event - walk me through how to set this up - and what's the most performant approach. it only needs one trigger, it just needs that to happen at the 300 enemies locations. 300 is not a set number as well, just an example

enemy locations are static, so can be registered in editor / before scene actually starts. 

EVEN BETTER - try the sparks manager script with sparks effect shader - most efficient?

```csharp

public void OnShoot()
{
  SparkManager.Instance.TriggerSpark(muzzlePoint.position);
}
```

Also look at Digital Layer Effect to replace the VFX Graph on each mesh - could be more efficient!

Also look at FMOD plugin development - compiling and putting them in the software

Projectile system could be moved to ECS, but the trick part appears to be Chronos interaction. Would need to make an ECS based system that tracks similar things, and then a translation layer between that and chronos, that properly keeps projectiles in line with Chronos actions. 

- what is the performance gain here? if significant, may be worth doing

Particle Systems and Physics conversions recommended - is this significant? performance gains through pursuing ECS versions of these?

try Mirage Impostors system on objects - seems better than others.

---

## From: Oct 14.md

# Oct 14

Implemented Jobs system for enemy raycasts for line of sight

Implemented Jobs system for projectile collisions

- make sure proper layer masks are set on enemies

Using Particle system Manager to better track and optimize any large amounts of particle systems 

Tried fioguring out waht is necessary for optimzing mesh for A*, unsuccessful. Most of my mesh seem to be ok but the WaitforJob - Idle stuff from before, still present on some meshes. Awaiting Aron’s repsonse and need to investigate further or just try only meshses without large overhead

Tracking - which structures per scene? 

Scene 1

Mobius Tube 2 - works

Mobius tube 2 Variation - works

Scene 3

Mobius Tube 6 - works

Mobius Tube 5 - External - works

Scene 5

Mobius tube 7 - works

Mobius Tube 5 - Internal - works

Could not fix

Mobius Tube 2 Flipped - optimization needed

Mobius tube 2 Varaition 2  - optmization needed

In a place where i can playtest and finish up / fix / refine what is here

Delete unecessary meshes wehn ready

---

## From: Oct 16th.md

# Oct. 16th

Need to see what’s up with LSPP not working with Buto

need to decide if i’m keeping it in the stack or not

Occa Outline appears to not work for me

trying Inab’s Ultimate Outlines - works - lots of features

Not dialed in yet, need to work on this 

Pulling back on Buto and Light intensity

Add spot lights to areas i think

give more variation

Problems with with RenderOnTop setup, look at enemies and see whats going wrong here

Switched to just being on Default layers for now 

First stage feels more like an exploding stage then a regular shooting one, swap with something else from later 

Figure out why outline seems more present behind or agaisnt the ground then when its not

by outline i mean highlighter

---

## From: Oct 2.md

# Oct. 2

Rough overview of things (top of head)

- Projectiles working for shooting enemy targets and shooting straight
- FMOD mostly reorganized, cleaner approach to how this is setup - less confusing!
- Projectiles shooting in Interior scene (ouroboros)

ToDo

- Get Enemy Explodes damaging the Player
    - After that
        - Refine spawn points
        - Refine flashing / sfx distances
        - Refine timeline for how long it takes to reach the player
        - Refine placement of static shooters

- Get the snake like dodeca enemies working better or ditch them
    - QTE for these?

- Infinite Snake
    - Refine shooting pattern - use multiple?
    - 

Idea

- Reticle speed slows down for easier aiming
- This comes from Don pachi slow down with big laser vs regular shots and you move faster
- Thinks of the reticle as a shmup ship? In this context, what can be done differently?

Use a lock on like layer section with wild missile curvy lasers as fx? Have some projectile fx packs that would work well for this I think 

See also zero ranger / crimson clover

Maybe implement the power up after shots mechanic that kill knight uses? Think about it or similar things

---

## From: Oct 21.md

# Oct 21

Was quite sick for the past week, just looking at things again now

Updating Cinemachine 3 camera to be a bit more refined

Updating Feel so it works with Cinemachine 3

- Implemented basic ones for hit and ricochet

Update several projectiles system / time manager so that projectile spawning is responsive to the changes in the time scale

Need to setup proper SDF’s and level bits for Scene 3

Remeber vectooField is the property originally used

May need to find something better than the SDF’s

Or research SDF more to see how to make them fit

Important I investigate this

https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005
Seems some of my jobs are getting in the way of each other, may need to figure out they scheduling so they don’t interrupt one another

Need to figure out the see through issue where enemies are more visible when behind / underneath the mobius/ground/snake

OCT 22

Adjusting shaders on main character so i can exclude them from the bitcrushing effect 

Ultimate Lit Shader is done - use this more or try others

This didnt work, taking another approach 

going to see where in the stack i want it to render

- adding the option to the render feature to choose where it’s rendered

Maybe I can set certian objects on layers where they are rendered on top of this?

Vaguely have this working

Effect intensity of JPG is being reset on start to 0 - need to fix this

OCT 23rd

Effect intensity of JPG is being reset on start to 0 - FIXED

trying to resolve issue with projectiles not shooting at times

i think it has to do with player locking on to projectile causing some interruption in how the enemies shoot

realizing this has more to do with what part is repeating in FMOD

Addressed this with an alternate ‘shoot every loop’ pattern when i nthe lock state,

located in the Enemy Shooting Manager

Also set a minmum loop time, so that if a loop is too short it’ll be every two loops 

IDEA

Item pickup using Dodge button

ITEM - Increases number of possible locks for player

Look up what yo uwant to use stamina for again??>????

Making things a bit more efficient

- Audio Manager for projectiles, no longer many fmod instances of sounds, just one used for all
    - may expand on this, small pool etc
- Edited a Highlighter script to activate more gradually to minimzie frame spike with enemy loading

vsync forces 60 fps, need to fix this

Build Test

- CPU limited is the issue for frame rate

Need to fix lighting in several stages

Need to finish the explode enemy

Need to debug all audio cutting out but then returning - whats causing that to happen?

THink

Minimum Viable Product - so you can tweak things once implemented

Implement Final snake boss!

Implement item pickups!

---

## From: Oct 24.md

# Oct. 24

Looking at optimization for fun 

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image.png)

Highlighter causing spike on enemy spawning

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%201.png)

Fmod Projectiles issue - thought I resolved this?

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%202.png)

Unsure what this is

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%203.png)

Some mesh levels have this issue, some don’t, investigate what it is

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%204.png)

Why AI waiting take so much time?

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%205.png)

Infinite Snake - optimize this if possible

![image.png](Oct%2024%20129ffc7f811f8006bd44c486add14ecc/image%206.png)

Not sure what’s happening when particle systems have this issue

Issues largely CPU bound - for higher frame rates GPU bound things to look into, but not the main concern

Can I use Adaptive Probe Volumes? Seems I can turn it on

Need to set layers that cast shadows for optimization

How to reduce setpass calls when lots of geometry? Optimize Batching?

Frame Debugger will show why things cannot be batched - analyze

More materials = more setpass calls. Reduce materials if possible

Simple Lit best performance Shader?

Disable MipMaps? What are MipMaps? Don’t know settings related to this

Need to learn how timeline view works

Attempting to batch timeline inits due to performance spike on static shooters of projectiles 

**Testing**

Projectiles having issues wehn colliding with Non enemy objects. This mostly occurs when player is straight shooting them. Some changes made to address this, but analyze further and test - this can cause projectile pool to come to a halt, and can be the reason why shooting stops worknig for everything. 

IMPORTANT NOTE - AIMovementSystem has been modified to address the job issues 

[https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005](https://forum.arongranberg.com/t/repairpathsystem-performance-profiling-improvement/17005)

If updating A*, need to do this again 

Frame Debugger help, fidnign ways to batch things that previously werren’t batching. Snake Static Shooters are different now, if look is bad adjust and test, but has reduced bathcing. find more things like this 

Increased fmod channel count from 32 to 64 to see if it effects build sound dropout

increased fmod channerl count to 128

Also made an Audio Manager to help deal with releasing instances

Reference this for playing sounds

Should clean up instances so channels are not filled out

**Oct. 25th**

Lots of optimizations in materials

Need to repalce particle effects and shjaders with more efficient things in general

Look at Enemy Shooting FX

Particle Explosion

Particle Hit Explosion

Anything that there’s a lot of

---

## From: Oct 3.md

# Oct. 3

Tried updating to newest A* Pathfinding 

Some possibly BIG improvements I cant use!

But errors in shooting and possibly performance issues 

Need to test in smaller cases and give thorough write up to figure this out with Aron Granberg

Implementing performance improvements in Projectile Management to reduce spikes

---

## From: Oct 9th.md

# Oct. 9th

Fixing small issues, like major performance issue in Scene 3. Problems with Death effect playing for all projectile deaths - should not happen when lifetime runs out

This did not fix the problem - but it was a problem regardless.

Verify these work in the right scenarios - and work properly VISUALLY

removing Enemy Death Particles from Projectile Manager to help debug particle system issues in regards to performance 

Scene 3 just not working - look at later I guess

Maybe work on Scene 5?

Oct. 10th 

Looking at Scene 3 again

Trying to find what particle system activity is causing these performance issues

Seems to be an issue with the level ouroboros model? when not being used I dont have these issues

**Optimziations to try**

Projectile Manager

- review job scheduling and completion to ensure not causing unecessary sstalls or idle time.

a. The Update method is doing a lot of work every frame, including completing jobs and processing projectile requests. Consider spreading this work across multiple frames or using coroutines.

b. The RegisterProjectile and UnregisterProjectile methods are resizing native arrays frequently, which can be expensive. Consider pre-allocating larger arrays or using a more efficient data structure.

c. The UpdateProjectilesAfterJob method is iterating through all projectiles every frame. Consider optimizing this to only update active projectiles.

d) The ProcessProjectileRequests method is creating and registering new projectiles every frame. This could lead to frequent transform changes and particle system updates.

ProjectileEffectManager

a. The PlayDeathEffect method is creating new particle systems if the pool is empty. This could lead to frequent instantiation during gameplay. Consider increasing the initial pool size or adding a warning when the pool is close to empty.

b. The ReturnEffectToPoolAfterFinished coroutine is checking if the effect is alive every frame. Consider using a less frequent check or a callback system.

ProjectileSpawning

This script manages projectile spawning. The ProcessShootProjectile method is setting up projectiles every time they're spawned. Consider pre-configuring projectiles in the pool to reduce setup time during gameplay.

a) The ProcessShootProjectile method is creating new particle effects for each projectile. Consider pooling these effects to reduce instantiation and destruction overhead.

b) The ShootStaticEnemyProjectile method is using a coroutine to spawn projectiles over time. This could be optimized to reduce the frequency of spawns or to batch spawn operations.

ProjectileStateBased

a) The SetupProjectile method is setting up new projectiles frequently. Consider batching these operations or using object pooling more effectively.

b) The Death method is creating death effects for each projectile. This could be optimized by pooling these effects.

PROGRESS

Need to revisitng the spiral mobius strip cayuse it got too many problems. 

Using other strucutre instead, working fine

Fixed debug go to next scene issue

Addressing weird load times going into infinite snake. it’s a deformable issue, need to optimzie how that’s handled with scene start / generation if i want to use it. may just find an alternative approach

Fix Mobius Strip 7 

Error 

[Degenerate Geometry A* Error](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/Degenerate%20Geometry%20A%20Error%2011cffc7f811f80e7839cd5d12172382f.md)

If i play any level long enough, this becomes a problem

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image.png)

Figure out whats up! Something to do with enemies

Also an issue in places, second half of  Scene 3

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image%201.png)

Possible optimization for explode enemy ai as well

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image%202.png)

---

## From: Ouroboros Boss Fight Concept 1.md

# Ouroboros Boss Fight Concept 1

Stage 1

- **Mechanics:**
    - **Forward Time Snake:** Moves in a straightforward, predictable manner. Its attacks are direct and easy to anticipate, such as straight-line firing patterns.
    - **Reverse Time Snake:** Moves unpredictably, appearing to rewind its movements occasionally. Its attacks might involve shooting in patterns that then reverse, challenging the player to remember the path of bullets as they come in and then go out.

Stage 2

- **Mechanics:**
    - **Combined Attack Sequences:** The snakes start to coordinate their attacks, creating complex patterns. For example, as the Forward Time Snake shoots a straight line of bullets, the Reverse Time Snake could add a spiraling pattern that the player needs to navigate through.
    - **Temporal Swap:** Introduce a mechanic where the snakes swap their time orientation briefly, confusing their movement and attack patterns and forcing the player to adjust quickly.

Stage 3

- **Mechanics:**
    - **Time Convergence:** Occasionally, both snakes' movements and attacks synchronize, leading to a moment of high intensity where the player must dodge a flurry of attacks from both directions.
    - **Final Burst:** When one snake is defeated, the remaining snake gains new attacks or an increased attack rate for a short period, pushing the player to the limits of their abilities.

---

## From: Ouroboros Boss Fight Concept 2.md

# Ouroboros Boss Fight Concept 2

### **Overview of the Boss Fight**

The fight is set on an ouroboros-themed track—a loop that visually represents the snake eating its own tail, emphasizing cyclical and infinite patterns. This setting can be used to subtly introduce time themes without complex mechanics. The player can still look in four directions, but the focus will be more on horizontal (left and right) and forward viewing due to the looped nature of the track.

### **Stage 1: Introduction and Basic Time Interaction**

- **Objective:** Introduce the player to the two snakes and their distinct temporal behaviors, which are simplified to forward-moving and occasionally reversing movements.
- **Mechanics:**
    - **Forward Time Snake:** This snake consistently moves forward, with predictable, straightforward attacks.
    - **Reverse Time Snake:** This snake occasionally reverses its movement, mimicking the rewind mechanic. It retreats along its path briefly before resuming its forward assault.
- **Implementation:** Use visual and audio cues to highlight the reversal moments of the Reverse Time Snake, making it clear and manageable for the player to anticipate changes.

### **Stage 2: Intermediate Complexity with Ouroboros Theme**

- **Objective:** Increase the challenge by utilizing the ouroboros loop track, requiring players to anticipate where and when they will encounter each snake again due to the looping path.
- **Mechanics:**
    - **Loop Encounters:** As the player progresses, each snake appears multiple times along the loop, requiring the player to remember the position and timing of each encounter.
    - **Synchronized Attacks:** Occasionally, the snakes’ paths will align, and they will attack simultaneously from different directions (front and back), making use of the player’s ability to look in multiple directions.
- **Implementation:** Keep the track design simple but visually align with the ouroboros theme, perhaps visually representing the track edges merging or splitting as the snakes' paths cross.

### **Stage 3: Climactic Convergence with Simplified Time Mechanics**

- **Objective:** Bring the boss fight to a climax using a simple but effective time manipulation feature that tests all of the player's accumulated skills.
- **Mechanics:**
    - **Temporal Echo:** When a snake is defeated, it leaves behind a 'temporal echo' for a brief period, where its last few attacks are replayed (ghost bullets), challenging the player to dodge past patterns.
    - **Final Burst:** The remaining snake intensifies its attack pattern, incorporating elements from the defeated snake’s behavior.
- **Implementation:** This stage uses a basic form of time manipulation (the echo) that doesn’t require complex programming but effectively enhances the challenge and thematic depth.

---

## From: Projectile Manager Optimizations.md

# Projectile Manager Optimizations

To further optimize the performance of your game when handling a large number of projectiles, consider moving any functionality that can be processed in bulk or doesn't need to be handled individually by each projectile to the ProjectileManager. Here are some suggestions:

### 1. Bulk Physics Checks

If multiple projectiles are performing similar physics checks (e.g., raycasts for obstacle detection), consider moving these checks to the ProjectileManager and performing them in a more optimized manner. For example, you could group projectiles by their general location and perform a single raycast for that group if they are close enough to each other or share a common target.

### 2. Global State Updates

If there are state updates that can be applied globally to all projectiles or a subset of projectiles (e.g., global wind effects, area-based effects like explosions or environmental hazards), handle these in the ProjectileManager to avoid duplicating calculations across individual projectiles.

### 3. Efficient Target Tracking

For homing projectiles, instead of each projectile independently searching for or tracking a target, consider having the ProjectileManager maintain a list of potential targets and assign or update targets for each projectile as needed. This can reduce the overhead of each projectile having to independently find targets.

### 4. Projectile Pooling

Implement a projectile pooling system within the ProjectileManager if you haven't already. Reusing projectiles instead of constantly instantiating and destroying them can significantly reduce garbage collection overhead and improve performance.

### 5. Collision Handling

Consider centralizing collision detection logic if possible. While Unity's physics system handles collision detection efficiently, there might be cases where you can optimize how and when projectiles check for collisions, especially if you can predict collision points or if certain projectiles don't need to check for collisions every frame.

### 6. Bulk Movement Calculations

For simple movement patterns (e.g., straight-line movement), calculate the movement in the ProjectileManager and apply the results to each projectile. Complex movements that depend on individual projectile states might not be suitable for this optimization.

---

## From: Projectile System Changes.md

# Projectile System Changes

### **Major Changes Made**

1. **Projectile System Architecture**:
    - Centralized movement logic in **`ProjectileMovement.cs`**
    - Removed duplicate homing logic from **`ProjectileStateBased.cs`**
    - Introduced proper state management and registration for homing projectiles
2. **Chronos Integration**:
    - Fixed timeline/clock integration
    - Ensured projectiles use the correct "Test" global clock
    - Properly handle time scaling in movement calculations
3. **Homing Behavior Improvements**:
    - Added virtual target system for accuracy-based targeting
    - Implemented adaptive rotation speeds
    - More frequent target position updates
    - Better accuracy implementation using offset calculations

### **Performance Analysis**

**More Performant**:

- ✅ Reduced duplicate calculations by centralizing movement logic
- ✅ Better memory management with object pooling
- ✅ Optimized grid updates with position thresholds
- ✅ Batch processing for projectile updates

**Less Performant**:

- ❌ More frequent target updates (now every frame)
- ❌ Additional GameObject creation for virtual targets
- ❌ More complex rotation calculations

### **Areas for Further Improvement**

1. **Virtual Target Optimization**:
    
    ```
    
    - Replace GameObject-based virtual targets with a simple Vector3 position
    - Use a struct-based target system to avoid garbage collection
    
    ```
    
2. **Spatial Partitioning**:
    
    ```
    
    - Implement distance-based update frequencies more aggressively
    - Only update targets for projectiles within relevant range
    
    ```
    
3. **Job System Integration**:
    
    ```
    
    - Move target position calculations to the job system
    - Batch process rotation calculations
    - Parallelize accuracy offset calculations
    
    ```
    
4. **Memory Management**:
    
    ```
    
    - Pool transform components for virtual targets
    - Implement object pooling for all projectile effects
    - Use non-allocating physics queries
    
    ```
    
5. **Update Optimization**:
    
    ```
    
    - Add dynamic update frequency based on projectile density
    - Implement priority queue for critical projectiles
    - Add LOD system for distant projectiles
    
    ```
    

### **Current State Assessment**

**Working Well**:

- ✅ Homing behavior is more accurate and responsive
- ✅ Better integration with time system
- ✅ More predictable projectile behavior
- ✅ Proper accuracy implementation

**Could Be Better**:

- ⚠️ Memory allocation from virtual targets
- ⚠️ Update frequency could be more optimized
- ⚠️ Job system could be better utilized
- ⚠️ Some calculations could be batched

Would you like me to focus on implementing any of these suggested improvements? The most impactful would probably be replacing the GameObject-based virtual targets with a simpler system and implementing better spatial partitioning for updates.

---

## From: Ricochet Refs.md

# Ricochet Refs

Yes, there are several games that handle fast-moving projectiles or incoming threats really well. Let me break down some notable examples:

1. **Furi**
- Similar to your game, features intense bullet-hell segments
- Uses bright neon trails behind projectiles
- Implements a subtle slow-motion effect when dodging
- The ground shows ripple patterns indicating where bullets will land
- Key takeaway: The combination of trails and ground indicators makes patterns readable even at high speeds
1. **Returnal**
- Handles 3D bullet-hell masterfully
- Uses bright orange/purple colors for projectiles that contrast with the darker environment
- Implements a ring around the player that highlights incoming projectiles
- Projectiles have distinct audio cues based on type
- Key takeaway: The contrast between projectiles and environment, plus the player-centered ring system
1. **Devil May Cry 5**
- Has a "Royal Guard" system for deflecting projectiles
- Shows a brief flash when projectiles spawn
- Uses distinct sound design for different projectile types
- Screen edges glow red when off-screen projectiles are approaching
- Key takeaway: The multi-layered warning system (visual + audio + UI)
1. **Ghostrunner**
- Features one-hit-kill projectiles that require precise timing
- Time slows slightly when you're about to deflect
- Projectiles leave bright energy trails
- Uses screen-edge indicators for threats
- Key takeaway: The brief time dilation gives players just enough time to react
1. **Metal Gear Rising: Revengeance**
- Has a parry system for projectiles
- Shows a distinctive flash before projectiles are fired
- Uses a "zoom-in" effect when perfect parries are possible
- Implements distinct audio cues for different types of incoming attacks
- Key takeaway: The clear telegraphing of attacks through multiple senses
1. **Sekiro: Shadows Die Twice**
- Features a crucial deflection mechanic
- Uses a distinct "glint" effect before projectiles are fired
- Sound design changes when deflection is possible
- Projectiles have clear trajectories
- Key takeaway: The importance of clear "windup" animations and effects

Common successful patterns across these games:

1. Multi-sensory feedback (visual + audio + haptic)
2. Clear contrast between projectiles and background
3. Brief time manipulation to aid reaction
4. Distinct "telegraph" phases before critical moments
5. Layer multiple warning systems (particle effects + UI indicators + sound)

1. **Rocket League**
- Not typically thought of as similar, but deals with fast-moving objects from all angles
- Uses boost trails to show ball trajectory
- Implements a transparent "ball cam" that helps track the ball's position
- Small circular indicator on the ground shows where the ball will land
- Key takeaway: The ground indicators and camera systems make 3D spatial awareness intuitive
1. **Super Monkey Ball**
- While it's about controlling a ball, it teaches excellent spatial awareness
- Uses camera angle shifts to warn of upcoming challenges
- Implements subtle visual cues in level design to guide player attention
- Sound pitch changes with speed/danger
- Key takeaway: Environmental design can naturally guide player attention
1. **F-Zero GX**
- Racing game with extremely high speeds
- Uses "zip zones" that are telegraphed with leading lines
- Screen warping effects indicate direction and speed
- Implements a mini-map that shows upcoming turns with clear advance warning
- Key takeaway: Visual distortion can actually help clarify direction at high speeds
1. **Catherine**
- Puzzle game, but deals with quick directional decision-making
- Uses block highlighting to show possible paths
- Implements a "danger meter" that fills as time runs out
- Edge glow effects show where threats will come from
- Key takeaway: Clear highlighting of safe zones vs danger zones
1. **Thumper**
- Rhythm violence game with abstract threats
- Uses "boom" effects that ripple through the track
- Screen shake and visual distortion telegraph upcoming obstacles
- Sound design perfectly matches visual threats
- Key takeaway: Synchronizing audio and visual feedback enhances reaction time
1. **Mirror's Edge**
- Not about projectiles, but masters the art of directing player attention at speed
- Uses "Runner Vision" to highlight important elements in red
- Implements subtle camera tilts to indicate direction
- Environmental design naturally draws the eye to the correct path
- Key takeaway: Color theory and environmental design can guide player attention
1. **WipEout**
- Racing game that handles weapon dodging at extreme speeds
- Uses minimal UI elements but clear weapon indicators
- Screen edge warnings for incoming attacks
- Implements a brief slow-mo when picking up weapons
- Key takeaway: Clean, minimal UI can be more effective than complex overlays
1. **Lethal League**
- Ball-based fighting game with extremely fast-moving projectile (the ball)
- Uses screen freeze frames at moment of impact
- Implements hit-stop effects to emphasize important moments
- Clear visual trails show exact trajectory
- Key takeaway: Brief pauses in action can enhance readability
1. **Audiosurf**
- Music game that handles incoming note blocks at varying speeds
- Uses lane coloring to indicate safe paths
- Implements "approach rate" based on music tempo
- Visual effects intensify with speed
- Key takeaway: Difficulty can scale naturally with speed through visual intensity

Common insights from these non-obvious examples:

1. Environmental design can do a lot of heavy lifting for player guidance
2. Brief pauses or hitches in action can actually improve readability
3. Ground/surface indicators are often clearer than floating UI elements
4. Speed lines and distortion effects can clarify rather than obscure
5. Audio cues can work subconsciously while visual cues work consciously
6. Color theory and contrast are crucial at high speeds
7. Camera manipulation can naturally direct attention

---

## From: Sept 1 - 3.md

# Sept 1 - 3

Plasytesting checklist

- each wave should have minimum number of enemies killed to progress to next
    - Need effect that shows wave end - explosion, burst at center of level?
- fixing camera problems in infinite snake sections
- fixing prefab static shooter placements and projectile lfietime issues / shooting directions, most evident in infinite snake section

Integrating proper death → restart to loop

Major Game Manager restructure

- need to fix scene transitions
- Msotly working!

Split Game Manager, Crosshair, Projectile Manager into many classes. 

Also Projectile State Based! Much easier to deal with these things now

May do more tomorrow with enemies etc. 

Building out QTE

Ideas about a tutorial 

IMP: Fix projectile accuracy - nothitting form palyer shot state and not so accurate when shot from enemies

- theres an ensure hit methow now, im not sure if thats going to be game breaking and jsut patch work on top of a system thats not fucntioning. keep an eye on this

Killing enemies gives a score value of 2*the start health - this is a value to play with 

Setup projectiles so enemy shot state is affected by time scale but other states are not affected byt the time scale - done!

Weird Fmod issues with automation maybe? cant get rewind sounds to play - automation is just not triggered i think? Odd issues that need more investigation. 

Lock seems to work? Make a new paramter - play with this?

---

## From: Sept 12.md

# Sept 12

Some things to address from last session

- ~~Make enemies brighter cant see them~~
- Add trail to snake enemies.
- ~~Need a reverse of mobius tube 5 for Secrtion 3, casue its normals are flipped and i dont need that for this part~~
- ~~In build, doesnt transition to next scene. Need to rectify this
Need a system of transition that works in dev and build~~
    - Working now, but some errors on build
- ~~Quit To Desktop is not working - only on steam deck~~
    - ~~dev build issue? not sure. made adjustments need to test~~
- There are still bullets getting stuck in spinning circles
- Internal to snake, enemies approach you and explode - you want to try and take them out before they do this
    - created behaviour for this section, need to buikld out the rest and test
- Projectile errors occuring in build, thigns glitching out and no projectiles shot at times by enemies , moving or static. Not sure what's going wrong! Test
    - Testing debugging on this

Look into this - Projectile Manager

![image.png](Sept%2012%206eda1778c22745ffae09306f546ba153/image.png)

What might fit other start logic?

ChronosKoreographyHandler created to make sure koreographer gets adjusted by time scale as i dont think it currently does

Making adjustments to projectiles so they handle the state where if beign rewound before their creation time, they will disappear

- Testing, seems to be working

Enemy Adjustments

- Adding robust line of sight to enemies, so that they are not hidden as much from player
- Trying to make maitnaned distance from player/each other better

Creating ChargeAndExplode Enemy for when inside of the Snake

- ChargeAndExplode Behaviour being created for this

In build, there are camera issue in both Snake Infinite sections. Not sure why camera is so fucked

There are times wehn enemies just dont shooty prtojectiles and i dont know why that is

Need better balance of bullet hitting player and missing player 

For game design sake, need to simplify how some of this stuff works. Here’s an approach

- if locked on to target, jsut do a laser effect and automatically hit the enemy. return projectile to pool
- From here, maybe develop a lock on system where you dont need to hold the button. depending on how good aim is at time of release, you will either shoot the projectile in a straight line OR the lock on is automatic - indicated by color change of reticle or soemthing design thing / movement to signify you’re locked in

Trying this new shooting system - current iteration does — The total damage from all projectiles will be summed up and applied to each locked enemy.

Sept 13/14

Updated shootuign method is working now, im killing enemies. Needs to be refined

Fixing up accuracy of the enemy shot projectiles as well, not sure ite being appleid properly so addressing this

---

## From: Sept 16.md

# Sept 16

Collection of things that may be help by o1

- Projectile movement
    - There are still bullets getting stuck in spinning circles
    - jagged movement
    - accuracy attribute
- Enemy behaviours implementation
    - line of sight
    - distance from one another
- optimize render pipeline
    - look at files, make edits where useful
- optimize any intense shaders
- 

General errors

- any lock ons must be disabled before moving to next section or there are errors.
- In build, there are camera issue in both Snake Infinite sections.
    - Seemingly camera issues in other spots as well, verify this

Creative Idea

- Charge and explode enemy type
- Improve feel of new shooting system
- Need to finish building out QTE shots

Organization

- Koreographer - Chart out my approach with appropriate naming, will make things easier moving forward
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl

![image.png](Sept%2016%20103ffc7f811f807d8035f38335d83548/image.png)

Look into turbulence particle affector - big resource hog

Remvoed it - this is resovled!

---

## From: Sept 17.md

# Sept 17

QTE System

- Always available or just for certain enemies? Need to think on this

Improvements to CustomAStar made - seems to work better!

Thinking about Charge and Explode enemy - going to try a basic version

Implemented! basic version. Need to extend further but basic behaviour is there, need to design the level around this 

Attempted to fix some camera issues but not working, See where else we can go wit hthe Collider Extension, might be useful.

---

## From: Sept 18.md

# Sept 18

Updates on [Sept 4](Sept%204%20897b9f685c254d90a4890ed785a55635.md) List

SceneManagement

- ~~Current state is better than before - any more improvements worth doing?~~
- ~~Long standing issue that pops up every few weeks - important to solidify this system and make it error proof~~

Fmod 

- ~~rebuild automation on Slow / Rewind to see if that fixes things~~
- ~~try global vs local setting for both, see if that works~~
- ~~try new automation field Time, see if that works~~
    - ~~Ultimately better to move to this regardless~~
- Should slow time cause doppler effects? How would this work?

Koreographer

- Chart out my approach with appropriate naming, will make things easier moving forward

Projectile Spawner

- ~~Verify being effected by time is not breaking the functionality, doesnt seem to behave entirely correct~~
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl
- Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it

Camera

- Game Manager / Cinemachine Camera Switching relationship needs to be cleaned up, its messy and could be much nicer!
- higher lookahead time seems to help with camera in reverse position , but causes issues elsewhere. Need to refine its movement
- Camera needs to be smoother / not move when reversing directions

Random

- ~~Look into DestroyEffect.cs - what is it really doing, is this good?~~
- Need effect that shows wave end - explosion, burst at center of level?
    - STARTED - Event Manager system started for this too
    - Is it effective? does it make sense?l
- Death → Restart cycle needs to be built out more
- Add trails to enemy snakes - will look nicer
- Refine Debug Settings scriptable object
- Can I have lock on sound progressively pitch up? Or is Fmod not capable of this logic?
- resetting projecitle lifetimes, whats the best values? need to refine this so you cant just hold bullets forever - i think, could be a mechanic - like bomberman sort of
- Odd issue where Gme Widnow has a differnet camera angel in editor depending on where and how it’s placed. Really strange!
- Finish DoTween → PrimeTween conversion
- Better Material for player - want to see shape contours - test toon shaders
- Implement various enemy shooting patterns again? Assess where this will be used first
- Where are interesting places to use Deformer?
- radar should be adjusted per level / scale is off
- Transition camera not currently in use, need to integrate at some point
    - Related, do i want to turn off player anymore or just use transition cam pointed elsewhere?
- AdjustSongParamters is free to delete - remember! dont need it
- Make more levels by alternating path player is on / flipping things upside down or sideways. Currently most relevant to Ouroboros structures
- More FX needed, more feedbacks, more sounds. Think about what player needs to know - maximum locks, projetiles are close by, etc
- Current Feedbacks not entirely working? Verify all of them.

QTE

- System needs refinement
    - also enemy lock list needs fixing due to being called enemy qte locklist, fix naming

Resources

https://www.tiktok.com/@pennywhistlestudios/video/7404455391200857390?embed_source=121374463,121451205,121439635,121433650,121404359,121351166,121331973,120811592,120810756;null;embed_masking&refer=embed&referer_url=cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.tiktok.com%2Fembed%2Fv2%2F7404455391200857390&display_name=tiktok&url=https%3A%2F%2Fwww.tiktok.com%2F%40pennywhistlestudios%2Fvideo%2F7404455391200857390&image=https%3A%2F%2Fp16-sign.tiktokcdn-us.com%2Fobj%2Ftos-useast5-p-0068-tx%2F9e89bc7ca8d442c4bfe8692cfff7e9af_1723984137%3Flk3s%3Db59d6b55%26x-expires%3D1724158800%26x-signature%3D7vJ%252BmktoLMpUG42yvD3SlYW9B%252Bc%253D&key=2aa3c4d5f3de4f5b9120b660ad850dc9&type=text%2Fhtml&schema=tiktok&referer_video_id=7404455391200857390.

https://www.youtube.com/watch?v=oxeD8kuCT_g

Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Build Issues - 2024-09-4

- Material / Shader for first structure is bugged - need to change
    - Removing wireframe to see if that is the issue

[Enemy Types](../../Ultimate%20BTR%20Dev%20Guide%20f9bf438fed974cc8b5f9b81a42a01296/Enemy%20Types%20877a54217fc845778f00204ffddc01d5.md) 

Level Assessment - Ophanim and others

[July 5th](../July%208fb5fd56583f43249ff8b3be6bfe7658/July%205th%205c9292d93b634fcb91521b76bbda468b.md) 

Previous task list - anything relevant?

[May 15](../May%209ac535a1c587434abe88186ee2b96d25/May%2015%20b4778e7f23ba46e39d3f0f43659cc83d.md) 

Attempted Collective project notes

[May 22](../May%209ac535a1c587434abe88186ee2b96d25/May%2022%20d0383e67d7af4be28bed0b866cd41c5c.md) 

Projectiles idea

- Make a system where they once they reach a certain radius, they hit the player in time?

more rhythmic?

- Have them surround in semi circle? Lots of different patterns possible here
- Would Flexalon help with this?
- Should each locked projectile add to time as well?

**Major Game Design Issues**

- Need an end boss to Ouroboros
- Need proper interesting progression along the way
- Look at how systems combine
    - How many different approaches are available to the player at a time? Can the construct a playstyle or is there one way to play?

---

## From: Sept 19.md

# Sept 19

Fixing vfx Graph heat distortion issues by importing from older project

- In Shader Graphs / Datamosh folder
- Good for void area

QTE idea - snake enemies have as many commands as they have length

Looking into performance hit that occurs when player shoots. Noticed on steam deck, something like a 15 fps drop. 

- ProjectileManager optimizations recommended, testing these

Adjsuting aim assist, not there yet but getting closer

Changing Default controls for new asiming system

Broke ability to damage mid boss snakes, need to fix. not sure if projectiles are not shooting properly or its a snake not taking damage issue

Adjustments to try and make snake not upside down for infinite generation area - not working. need to rethink approach here

Slowing projectiles a little before they hit player, for better player experience reacting to them 

Need a different sound for when yo uuse the shield and a projectiel doesnt boucne fof

make this more obviouis! bigger visual cue too

---

## From: Sept 23.md

# Sept 23

Adjusted Player Shooting script to for shooting projectiles straight without a locked on enemy. 

NEED TO TEST

Fix the QTE idea - use for different length enemies as test? what other ideas make sense?

Need to also make Infinite Snake chasing snake NOT be lockable - need to add paramters to allow for this. 

- Does Player need a check on enemies to see whether they are lockable or not?

Interested in Perfect Culling asset but may be the case that GPU Imstancer can achieve better results for me. Need to try that out again

Introduced Stamina for ricochet

Changed out some sounds, developing a new approach

Changed Player Locked State of Projectile to old version, still trying to figure out shooting these straight. 

!!!

maybe just recycle locked projectiles and then call the shoot method for however many were left? Shoot from the reticle - similar to static shooting calls. 

!!!!

**Ideation**

QTE

use for different length enemies

use for traps & obstacles

It’s not about just ideas that would add to complexity, but are also easily implementable and are multipliers of the current ideas

---

## From: Sept 24.md

# Sept 24

Rethinking the ‘shoot projectile in straight line’ problem

Try

- No alternate handling of projectiles when locked on
- All return to pool when locked on
- We just do the shooting operation from the reticle for the number of projectiles we want to shoot

---

## From: Sept 30.md

# Sept 30

Static shooters not shooting in Section 5 area, not sure why

Feel like its a Koreographer issue, things may not be fully setup 

Music issue is, i have a confusing setup for Game Manager - FMOD  - Koreographer

Need to rewrite the organizaiton of all of this 

Need to sort out my song sections vs level sections

Doing this now

Renaming parts of a level to just Scenes

Ouroboros - Base

Ouroboros - Scene 1

Etc

Sections are for FMOD

Tracks per Koreographer 

- Need to chart how each is used

**1 Bar**

**8th**

Enemies shoot on 16ths?

**16th**

Player is on 16ths?

---

## From: Sept 4.md

# Sept 4

Compiling list of current issues / things to investigate

SceneManagement

- Current state is better than before - any more improvements worth doing?
- Long standing issue that pops up every few weeks - important to solidify this system and make it error proof

Fmod 

- ~~rebuild automation on Slow / Rewind to see if that fixes things~~
- ~~try global vs local setting for both, see if that works~~
- ~~try new automation field Time, see if that works~~
    - ~~Ultimately better to move to this regardless~~
- Should slow time cause doppler effects? How would this work?

Koreographer

- Chart out my approach with appropriate naming, will make things easier moving forward

Projectile Spawner

- Verify being effected by time is not breaking the functionality, doesnt seem to behave entirely correct
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl
- Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it

Camera

- Game Manager / Cinemachine Camera Switching relationship needs to be cleaned up, its messy and could be much nicer!
- higher lookahead time seems to help with camera in reverse position , but causes issues elsewhere. Need to refine its movement
- Camera needs to be smoother / not move when reversing directions

Random

- Look into DestroyEffect.cs - what is it really doing, is this good?
- Need effect that shows wave end - explosion, burst at center of level?
    - STARTED - Event Manager system started for this too
    - Is it effective? does it make sense?l
- Death → Restart cycle needs to be built out more
- Add trails to enemy snakes - will look nicer
- Refine Debug Settings scriptable object
- Can I have lock on sound progressively pitch up? Or is Fmod not capable of this logic?
- resetting projecitle lifetimes, whats the best values? need to refine this so you cant just hold bullets forever - i think, could be a mechanic - like bomberman sort of
- Odd issue where Gme Widnow has a differnet camera angel in editor depending on where and how it’s placed. Really strange!
- Finish DoTween → PrimeTween conversion
- Better Material for player - want to see shape contours - test toon shaders
- Implement various enemy shooting patterns again? Assess where this will be used first
- Where are interesting places to use Deformer?
- radar should be adjusted per level / scale is off
- Transition camera not currently in use, need to integrate at some point
    - Related, do i want to turn off player anymore or just use transition cam pointed elsewhere?
- AdjustSongParamters is free to delete - remember! dont need it
- Make more levels by alternating path player is on / flipping things upside down or sideways. Currently most relevant to Ouroboros structures
- More FX needed, more feedbacks, more sounds. Think about what player needs to know - maximum locks, projetiles are close by, etc
- Current Feedbacks not entirely working? Verify all of them.

QTE

- System needs refinement
    - also enemy lock list needs fixing due to being called enemy qte locklist, fix naming

Resources

https://www.tiktok.com/@pennywhistlestudios/video/7404455391200857390?embed_source=121374463,121451205,121439635,121433650,121404359,121351166,121331973,120811592,120810756;null;embed_masking&refer=embed&referer_url=cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.tiktok.com%2Fembed%2Fv2%2F7404455391200857390&display_name=tiktok&url=https%3A%2F%2Fwww.tiktok.com%2F%40pennywhistlestudios%2Fvideo%2F7404455391200857390&image=https%3A%2F%2Fp16-sign.tiktokcdn-us.com%2Fobj%2Ftos-useast5-p-0068-tx%2F9e89bc7ca8d442c4bfe8692cfff7e9af_1723984137%3Flk3s%3Db59d6b55%26x-expires%3D1724158800%26x-signature%3D7vJ%252BmktoLMpUG42yvD3SlYW9B%252Bc%253D&key=2aa3c4d5f3de4f5b9120b660ad850dc9&type=text%2Fhtml&schema=tiktok&referer_video_id=7404455391200857390.

https://www.youtube.com/watch?v=oxeD8kuCT_g

Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Build Issues - 2024-09-4

- Material / Shader for first structure is bugged - need to change
    - Removing wireframe to see if that is the issue

[Enemy Types](../../Ultimate%20BTR%20Dev%20Guide%20f9bf438fed974cc8b5f9b81a42a01296/Enemy%20Types%20877a54217fc845778f00204ffddc01d5.md) 

Level Assessment - Ophanim and others

[July 5th](../July%208fb5fd56583f43249ff8b3be6bfe7658/July%205th%205c9292d93b634fcb91521b76bbda468b.md) 

Previous task list - anything relevant?

[May 15](../May%209ac535a1c587434abe88186ee2b96d25/May%2015%20b4778e7f23ba46e39d3f0f43659cc83d.md) 

Attempted Collective project notes

[May 22](../May%209ac535a1c587434abe88186ee2b96d25/May%2022%20d0383e67d7af4be28bed0b866cd41c5c.md) 

Projectiles idea

- Make a system where they once they reach a certain radius, they hit the player in time?

more rhythmic?

- Have them surround in semi circle? Lots of different patterns possible here
- Would Flexalon help with this?
- Should each locked projectile add to time as well?

**Major Game Design Issues**

- Need an end boss to Ouroboros
- Need proper interesting progression along the way
- Look at how systems combine
    - How many different approaches are available to the player at a time? Can the construct a playstyle or is there one way to play?

---

## From: September 7 8 9.md

# September 7/8/9

Light day, was too tired. more to do tomorrow

Many snakes in Model and Animating project for me to look at. 

Need to fix snake chase scene, but also go over all my notes

Successfully have Section 2 working again

Need some interesting differences between section 2 and 4, maybe similar for now

Section 5 as ‘the chase’ level? where interior and exterion version of being chased? 

Only a few enemies to kill this time, less than before 

Need to go over notes, and also build Section5 appropriately

- Scene Transition Error
    - I think transition needs its own call associated with it, need to architect how this works and the flow between these stages. likely a better way to handle it then what’s currently active

[Game Name Ideas](September%207%208%209%20fb8457ead97e413f9459f1cc8109f64e/Game%20Name%20Ideas%20ddff5910d0534bfa893528b399025049.md)

Disable this if issue in build

![image.png](September%207%208%209%20fb8457ead97e413f9459f1cc8109f64e/image.png)

Was a possible issue before, flag this for future reference

---
