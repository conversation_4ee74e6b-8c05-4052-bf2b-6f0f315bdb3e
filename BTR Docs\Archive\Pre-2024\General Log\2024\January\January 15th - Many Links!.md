# January 15th - Many Links!

Game Dev Links

[https://github.com/QuentinKing/OuterWildsBlackHole](https://github.com/QuentinKing/OuterWildsBlackHole)

- [https://github.com/QuentinKing/OuterWildsBlackHole/issues/1](https://github.com/QuentinKing/OuterWildsBlackHole/issues/1)

[https://github.com/JonasDeM/QuickSave](https://github.com/JonasDeM/QuickSave)?

[https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides](https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides)

[https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance](https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance)

[https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/](https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/)

[https://www.youtube.com/watch?v=3CfpJlGI2wc](https://www.youtube.com/watch?v=3CfpJlGI2wc)

[https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released](https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released)

[https://twitter.com/OhPoorPup/status/1727814207398408439](https://twitter.com/OhPoorPup/status/1727814207398408439)

[One Wheel Studio](https://www.youtube.com/@OneWheelStudio/videos)

[https://twitter.com/folmerkelly/status/1729145782337716492](https://twitter.com/folmerkelly/status/1729145782337716492)

[https://www.youtube.com/watch?v=1N_4NzwprJI](https://www.youtube.com/watch?v=1N_4NzwprJI)

[https://vol.co/products/wipeout-futurism](https://vol.co/products/wipeout-futurism)

[https://www.youtube.com/watch?v=hJcFgZqT0ow](https://www.youtube.com/watch?v=hJcFgZqT0ow)

[Scott Game Sounds](https://www.youtube.com/@ScottGameSounds/videos)

[https://twitter.com/DominicTarason/status/1731662776840372406](https://twitter.com/DominicTarason/status/1731662776840372406)

[Portal effect in URP](https://www.reddit.com/r/Unity3D/comments/18aeidd/portal_effect_in_urp/)

[https://github.com/Unity-Technologies/ProjectAuditor](https://github.com/Unity-Technologies/ProjectAuditor)

[https://twitter.com/andytouch/status/1733190142548992437](https://twitter.com/andytouch/status/1733190142548992437)

[Easy and Powerful Extension Methods | Unity C#](https://www.youtube.com/watch?v=Nk49EUf7yyU)

[Shader Graph: Learn to use nodes with Node Reference Samples | Tutorial](https://www.youtube.com/watch?app=desktop&v=vrEi6M3GjC4)

[https://github.com/DeadlyRedCube/Cathode-Retro](https://github.com/DeadlyRedCube/Cathode-Retro)?

[https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events](https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events)

[https://github.com/pointcache/BasicEventBus](https://github.com/pointcache/BasicEventBus)

[https://softsaz.ir/blender-market-citybuilder-3d/](https://softsaz.ir/blender-market-citybuilder-3d/)

[https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/](https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/)

[RGD Workshop : Rational Game and Level Design](https://www.youtube.com/watch?v=xqHAjwrNp70)

[https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/](https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/)

[Mirza Beig on Twitter / X](https://twitter.com/TheMirzaBeig/status/1715936953407815983)

[https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)

[Harry Clarke’s Illustrations](https://kottke.org/23/12/harry-clarkes-illustrations)

[Shmup Visual Design, Colour Theory and Bullet Visibility (Subtítulos en Español)](https://www.youtube.com/watch?v=NxVG7caNmO0)

[Twisted Corridor Effect in Unity Shader Graph](https://www.youtube.com/watch?v=CGI0SyiElfY)

[The Cinematography of Evangelion](https://www.youtube.com/watch?v=fsmbpEZ_lI8)

[How Platinum Design a Combat System](https://www.youtube.com/watch?v=D689ZBdOAuw)

[What's new in Unity's Universal Render Pipeline?](https://www.youtube.com/watch?v=Z-XCk8mXJtI)

[[TUTORIAL] Stencil buffer in Unity URP (cutting holes, impossible geometry, magic card)](https://www.youtube.com/watch?v=y-SEiDTbszk)

[Rhythm game architecture](https://www.reddit.com/r/Unity3D/comments/196l47e/rhythm_game_architecture/)

[10 Unity Tips You (Probably) Didn't Know About](https://www.youtube.com/watch?v=JgAhIX8YLBw)

[https://twitter.com/ashlee3dee/status/1746606648330686730](https://twitter.com/ashlee3dee/status/1746606648330686730)

[Better Singletons in Unity C#](https://www.youtube.com/watch?v=LFOXge7Ak3E)

[hazylevels](https://www.youtube.com/@hazylevels/videos)

[Service Locator: Inversion of Control in Unity C#](https://www.youtube.com/watch?v=D4r5EyYQvwY)

[https://github.com/electronicarts/IRIS](https://github.com/electronicarts/IRIS)

[https://github.com/sudotman/BetterUnity](https://github.com/sudotman/BetterUnity)?

[Easy Optimization! Flyweight design pattern in unity](https://www.youtube.com/watch?v=zrUhpjwBpPI)

[I Asked 17 Game Developers For Their Biggest Tips](https://www.youtube.com/watch?v=ESrw7BerWgM)

[Write Clean Code to Reduce Cognitive Load](https://www.reddit.com/r/programming/comments/17to0h1/write_clean_code_to_reduce_cognitive_load/)

[Warped Imagination](https://www.youtube.com/@WarpedImagination/videos)

[Eric Wang_VFX Artist](https://www.youtube.com/@EricWang0110/videos)

[Shockwave Shader Graph - How to make a shock wave shader in Unity URP/HDRP](https://www.youtube.com/watch?v=dFDAwT5iozo)

[Don't Use Deep Profiler! - Profiler Marker Unity](https://www.youtube.com/watch?app=desktop&v=DsrFkPP0Qe8)

ALternate radar? 

[How to make RPG Radar Chart (Unity Tutorial)](https://www.youtube.com/watch?v=twjMW7CxIKk)

Make bullets glow - look into this

[How to make Unity GLOW! (Post Processing Bloom Unity Tutorial)](https://www.youtube.com/watch?v=bkPe1hxOmbI)

Apply bloom to specific objects?

[How to Add GLOW to Certain Objects | Unity 2022](https://www.youtube.com/watch?v=9v6yyRIoWnE)

THis work for adding selective bloom, just need to adjust intensity of objects emission map

Better glow on projectiles - good step forward! need to refine

Need to look into when light intensity is too much in the scene - why is it happening?

Need to make the game more fast paced, feels a bit slow?