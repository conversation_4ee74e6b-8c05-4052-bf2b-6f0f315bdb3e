# June 6
 2 | 
 3 | Introduced minimum kill count into Ultimate Wave Spawner as a continue mode. Need to test!
 4 | 
 5 | Probably need to account for this in despawning or enemy deaths. 
 6 | 
 7 | Also set start health to 10 for instant kill
 8 | 
 9 | Thoughts on having many bullets flynig at player
10 | 
11 | Make a system where they once they reach a certain radius, they hit the player in time? 
12 | 
13 | more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
14 | 
15 | Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm. 
16 | 
17 | Important - made the previous edits to the wave events, now I made more
18 | 
19 | Instead of subscribing in the inspector on the WaveSpawnController, added a script that subscribes to all active instances of the event. This is better for changing scenes, should find the carried over fmod and such. 
20 | 
21 | These are the events 
22 | 
23 | ![Untitled](June%206%20e9f434ec8d424de5a514ca512ad45f55/Untitled.png)
24 | 
25 | Order matters!
26 | 
27 | Idea - time is running down / health is running down
28 | 
29 | No limit on lock on 
30 | 
31 | Just get as many as possible and go
32 | 
33 | Made changes to crosshair, verify these dont break things. Should just be null checks but revert if necessary.
34 | 
35 | The issue is definitely arrising due to increasing the locked on enemy count. I have more so the scenario is more possible. that they die when still locked on, i guess? 
 36 | 
 37 | Seems to be working okay now
 38 | 
 39 | SNake tail enemies dont totally work, need more adjustment - causing technical errors