# Feb 6th

Added HBAO and changed Bakery settings for rendering

Not a real noticeable improvement, something to revisit later in optimizing pipeline

Added HDR color to Joost shader, looking into character being more visible. A good start!

Updated some of projectile logic but not fully working.

Can lock on and shoot but enemy’s not dying - why?

- suspect the bullets need to go faster and maybe go through walls?
- Maybe dont shoot from above, gather bullets at reticle? Shoot from reticle?

Have adjusted bullets to gather above the player 

- not working currently - not entirely sure why

Also getting errors related the shooting and the bullets being kinematic

the enemies should not need to apply force to the bullets - just instantiate them

ADJUST FOR THIS - the bullets should move on their own (i think!)

Birth of character room… ‘tutorial’ section…

use this effect?

[Recreating the Water Caustics Effect from Blade Runner 2049](https://www.youtube.com/watch?v=Oh21hYx_Jbk)

Adding animancer pro for better animation control in unity

Disabling the render for the projecitle when locked, this is because its now going to the reticle position and it was too bright, but could not get emissions to change, so just not rendering the model now.  A workable temporary fix. Need to re-enable this when shot because i cant currently see where bullets are even going lol Unlocking when lauchatenemy, but come back to this!! cant use this long term, i can see it causing issues 

Doesn’t seem like shooting is working though. Need to work through the whole process and understand what’s happening.