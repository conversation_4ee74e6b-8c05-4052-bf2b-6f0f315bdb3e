---
systems: [gameplay]
components: [qte, time-control, waves]
tags: [core-system]
priority: p0
last_touched: 2025-04-09
links:
  - ![[Systems/Player/Architecture#^qte-integration]]
  - ![[Systems/Enemies/Architecture#^wave-system]]
---
# BTR Gameplay Systems Architecture
[[QuickTime Events]] → [[Wave System]]

```mermaid
flowchart TB
    %% Core Gameplay Systems
    QTEManager[QuickTimeEventManager]
    TimeControl[Time Control System]
    WaveSystem[Wave System]
    OptimizationSystem[Platform Optimization]
    
    %% QTE Components
    QTEPanel[QTE UI Panel]
    QTEInput[Input Handler]
    QTESequence[Sequence Generator]
    QTEProgress[Progress Tracker]
    
    %% Time Control Components
    TimeManager[Time Manager]
    GlobalClock[Global Clock]
    TimeEvents[Time Events]
    
    %% Wave Components
    WaveManager[Wave Manager]
    WaveHUD[Wave HUD]
    WaveEvents[Wave Events]
    
    %% Platform Optimization
    SteamDeck[Steam Deck Optimizer]
    PerfSettings[Performance Settings]
    
    %% External Systems
    InputSystem[Input System]
    UISystem[UI System]
    AudioSystem[Audio System]
    EventSystem[Event System]
    
    %% QTE Relationships
    QTEManager --> QTEPanel
    QTEManager --> QTEInput
    QTEManager --> QTESequence
    QTEManager --> QTEProgress
    QTEInput --> InputSystem
    QTEPanel --> UISystem
    QTEManager --> AudioSystem
    
    %% Time Control Relationships
    TimeControl --> TimeManager
    TimeManager --> GlobalClock
    TimeManager --> TimeEvents
    TimeEvents --> EventSystem
    
    %% Wave System Relationships
    WaveSystem --> WaveManager
    WaveManager --> WaveHUD
    WaveManager --> WaveEvents
    WaveEvents --> EventSystem
    
    %% Optimization Relationships
    OptimizationSystem --> SteamDeck
    SteamDeck --> PerfSettings
    PerfSettings --> TimeManager
    PerfSettings --> UISystem
    
    %% System Interconnections
    TimeManager --> QTEManager
    WaveManager --> TimeManager
    QTEManager --> WaveManager

    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef component fill:#bbf,stroke:#333,stroke-width:2px
    classDef support fill:#bfb,stroke:#333,stroke-width:2px
    classDef external fill:#fbb,stroke:#333,stroke-width:2px
    
    class QTEManager,TimeControl,WaveSystem,OptimizationSystem core
    class QTEPanel,QTEInput,QTESequence,QTEProgress,TimeManager,WaveManager,SteamDeck component
    class GlobalClock,TimeEvents,WaveEvents,PerfSettings support
    class InputSystem,UISystem,AudioSystem,EventSystem external
```

## Color Legend
- 🟪 Core (Purple): Primary gameplay systems
- 🟦 Components (Blue): System-specific components
- 🟩 Support (Green): Supporting features
- 🟥 External (Red): System integrations

## System Description
This diagram details the Gameplay Systems' architecture, showing:

1. **Quick Time Events (QTE)**
   - Input handling and validation
   - Sequence generation
   - Progress tracking
   - UI integration
   - Audio feedback
   - Time-sensitive mechanics

2. **Time Control System**
   - Global time management
   - System-wide time scaling
   - Event-based synchronization
   - Clock management

3. **Wave System**
   - Wave progression
   - HUD integration
   - Event propagation
   - Score tracking

4. **Platform Optimization**
   - Steam Deck specific settings
   - Performance management
   - Graphics optimization
   - System adaptation

5. **System Integration**
   - Input system coordination
   - UI system feedback
   - Audio system integration
   - Event system communication
   - Time synchronization

[[Architecture/architecture_overview|Back to Overview]]