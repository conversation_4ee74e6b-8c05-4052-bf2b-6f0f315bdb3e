# October 13

High Level - what does the game need?

List our requirements - how can others help?

Try my build on steam deck - curious on performance!

Mechanics

- Foundation exists for this

Graphics

- destroyed / destructured asset idea for most enviroment
- maybe for enemies as well

Sound

- Can do this, not worried, but a workload nonetheless

Narrative

- workshopping many ideas
- who are the characters?

Levels

- need key narrative decisions made for this
-