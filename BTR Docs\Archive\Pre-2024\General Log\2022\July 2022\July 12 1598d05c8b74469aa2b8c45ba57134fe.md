# July 12

Aim of Enemy Lock on is off

Realized this is due to being child of reticle, when it should probably just follow reticle by rotating

Is RaySpawnEnemyLock being used for anything? verify this / delete if not needed

Many problems with Cone enemy detection and Look At Constraint

Looking to switch back to raycast implementation of Enemy Locking

Changed to use current default Rayspawn, not a new one. 

Does it work?

EXTREMELY hard to lock onto enemies this way!

Trying with seperate RaySpawn for Enemies

Used seperate Ray

Works better! Working great!

<aside>
💡 Three bugs to address

</aside>

<aside>
💡 1) Turn off locking in transitions - need to jump into next wave without locking music

</aside>

<aside>
💡 2) Bullets locked mid air - whats going wrong here?

</aside>

<aside>
💡 3) Assets value errors - Pooling / Object Particle Spawner problems - debug!

</aside>

[Optimistic Nihilism - YouTube](https://www.youtube.com/watch?v=MBRqu0YOH14)

Purpose as rhythm? Is there a rhythmic aspect i should try locking in to this game? a wind up start? repeatedly pressing some button? 

[VIDEOBALL final gameplay trailer!](https://www.youtube.com/watch?v=dUxJspLDWdw&t=45s)

manipulate time more, as happens in the video ball trailer?