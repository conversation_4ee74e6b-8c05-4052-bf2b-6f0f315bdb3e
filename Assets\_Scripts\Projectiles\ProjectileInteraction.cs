using UnityEngine;

namespace BTR.Projectiles
{
    /// <summary>
    /// SIMPLIFIED ProjectileInteraction - handles collision and effects
    /// Direct migration from ProjectileStateBased collision logic
    /// NO complex abstractions - just move code between classes
    /// </summary>
    public class ProjectileInteraction : MonoBehaviour
    {
        [Header("Combat Settings")]
        public float damageAmount = 10f;
        public LayerMask collisionLayers = -1;

        // Effects integration (migrated from ProjectileStateBased and ProjectileVisualEffects)
        private ParticleSystem[] particleSystems;
        private TrailRenderer trailRenderer;

        // Cached components
        private Transform cachedTransform;
        private bool isInitialized = false;

        // Collision damage control (migrated from ProjectileStateBased)
        private bool collisionDamageEnabled = true;

        // Track if this is a player shot (for collision logic)
        private bool isPlayerShot = false;

        private void Awake()
        {
            // Cache components
            cachedTransform = transform;

            // Initialize visual effects (migrated from ProjectileStateBased)
            InitializeVisualEffects();
        }

        // MIGRATED from ProjectileStateBased visual effects initialization
        private void InitializeVisualEffects()
        {
            // Cache particle systems (migrated from ProjectileVisualEffects)
            particleSystems = GetComponentsInChildren<ParticleSystem>();

            // Cache trail renderer
            trailRenderer = GetComponent<TrailRenderer>();

            Debug.Log($"[ProjectileInteraction] Visual effects initialized - Particles: {particleSystems?.Length ?? 0}, Trail: {trailRenderer != null}");
        }

        private void Start()
        {
            if (!isInitialized)
            {
                InitializeInternal();
            }
        }

        // MIGRATED from ProjectileStateBased.OnTriggerEnter
        private void OnTriggerEnter(Collider other)
        {
            Debug.Log($"[ProjectileInteraction] OnTriggerEnter called - Other: {other.name}, Tag: {other.tag}, Active: {gameObject.activeInHierarchy}, Initialized: {isInitialized}, IsPlayerShot: {isPlayerShot}, CollisionDamageEnabled: {collisionDamageEnabled}");

            if (!gameObject.activeInHierarchy || !isInitialized)
            {
                Debug.LogWarning($"[ProjectileInteraction] Collision ignored - Active: {gameObject.activeInHierarchy}, Initialized: {isInitialized}");
                return;
            }

            // Migrated collision logic from ProjectileStateBased
            if (other.CompareTag("Player") && !isPlayerShot)
            {
                Debug.Log($"[ProjectileInteraction] Player collision detected - checking for IDamageable component");
                var damageable = other.GetComponent<IDamageable>();
                if (damageable != null && collisionDamageEnabled)
                {
                    Debug.Log($"[ProjectileInteraction] Applying damage to player: {damageAmount}");
                    ApplyDamage(damageable, cachedTransform.position);
                    TriggerHitEffects(other.transform.position, Vector3.zero);
                    Death(true); // Hit target
                }
                else
                {
                    Debug.LogWarning($"[ProjectileInteraction] Player collision failed - IDamageable: {(damageable != null ? "Found" : "NULL")}, CollisionDamageEnabled: {collisionDamageEnabled}");
                }
            }
            else if (other.CompareTag("Enemy") && isPlayerShot)
            {
                var damageable = other.GetComponent<IDamageable>();
                if (damageable != null && collisionDamageEnabled)
                {
                    ApplyDamage(damageable, cachedTransform.position);
                    TriggerHitEffects(other.transform.position, Vector3.zero);
                    Death(true); // Hit target
                }
            }

            // Additional layer-based collision check (FIXED: now respects isPlayerShot flag)
            if (ShouldCollideWith(other))
            {
                var damageable = other.GetComponent<IDamageable>();
                if (damageable != null && collisionDamageEnabled)
                {
                    // FIXED: Only damage if this is a valid target for this projectile type
                    bool shouldDamage = false;
                    
                    if (isPlayerShot && other.CompareTag("Enemy"))
                    {
                        shouldDamage = true; // Player projectiles can damage enemies
                    }
                    else if (!isPlayerShot && other.CompareTag("Player"))
                    {
                        shouldDamage = true; // Enemy projectiles can damage player
                    }
                    // Note: Enemy projectiles should NOT damage other enemies
                    // Note: Player projectiles should NOT damage player
                    
                    if (shouldDamage)
                    {
                        Debug.Log($"[ProjectileInteraction] Layer-based collision - IsPlayerShot: {isPlayerShot}, Target: {other.name}, Tag: {other.tag}, Damage: {damageAmount}");
                        ApplyDamage(damageable, cachedTransform.position);
                        TriggerHitEffects(other.transform.position, Vector3.zero);
                        Death(true); // Hit target
                    }
                    else
                    {
                        Debug.Log($"[ProjectileInteraction] Layer-based collision IGNORED - IsPlayerShot: {isPlayerShot}, Target: {other.name}, Tag: {other.tag} (would be friendly fire)");
                    }
                }
            }
        }

        private void InitializeInternal()
        {
            isInitialized = true;
            Debug.Log($"[ProjectileInteraction] Initialized - GameObject: {gameObject.name}");
        }

        // Public Initialize method for compatibility with ProjectileEntity
        public void Initialize()
        {
            if (!isInitialized)
            {
                InitializeInternal();
            }
        }

        private bool ShouldCollideWith(Collider other)
        {
            // Check layer mask
            int otherLayer = 1 << other.gameObject.layer;
            return (collisionLayers.value & otherLayer) != 0;
        }

        // MIGRATED from ProjectileStateBased.ApplyDamage
        private void ApplyDamage(IDamageable target, Vector3 hitPoint)
        {
            if (target == null) return;

            // Calculate final damage (migrated from ProjectileStateBased.CalculateDamage)
            float finalDamage = damageAmount;

            // Apply damage
            target.TakeDamage(finalDamage);

            Debug.Log($"[ProjectileInteraction] Applied {finalDamage} damage to {target}");
        }

        // MIGRATED from ProjectileStateBased and ProjectileVisualEffects
        private void TriggerHitEffects(Vector3 hitPoint, Vector3 normal)
        {
            // Play death effects (migrated from ProjectileVisualEffects.PlayDeathEffect)
            if (particleSystems != null && particleSystems.Length > 0)
            {
                foreach (ParticleSystem ps in particleSystems)
                {
                    if (ps != null)
                    {
                        ps.Stop(true);
                        ps.Clear(true);
                        ps.Play(true); // Play hit effect
                    }
                }
            }

            // Trigger ProjectileEffectManager effects if available
            if (ProjectileEffectManager.Instance != null)
            {
                // Use ProjectileDeath effect type (the correct enum value)
                // Safely handle zero normal vector to prevent LookRotation errors
                Quaternion rotation = normal != Vector3.zero ? Quaternion.LookRotation(normal) : Quaternion.identity;
                ProjectileEffectManager.Instance.PlayEffect(ProjectileEffectManager.EffectType.ProjectileDeath, hitPoint, rotation);
            }

            Debug.Log($"[ProjectileInteraction] Hit effects triggered at {hitPoint}");
        }

        // MIGRATED from ProjectileStateBased.Death (simplified)
        private void Death(bool hitTarget = false)
        {
            Debug.Log($"[ProjectileInteraction] *** PROJECTILE DEATH *** - Hit target: {hitTarget}, GameObject: {gameObject.name}");

            // Simple cleanup - delegate to ProjectileEntity
            var projectileEntity = GetComponent<ProjectileEntity>();
            if (projectileEntity != null)
            {
                Debug.Log($"[ProjectileInteraction] Calling ProjectileEntity.Death()");
                projectileEntity.Death(hitTarget);
            }
            else
            {
                Debug.LogWarning($"[ProjectileInteraction] No ProjectileEntity found - using fallback deactivation");
                // Fallback - just deactivate
                gameObject.SetActive(false);
            }
        }

        // Public API methods for ProjectileEntity
        public void Setup(float damage)
        {
            damageAmount = damage;

            if (!isInitialized)
            {
                InitializeInternal();
            }

            Debug.Log($"[ProjectileInteraction] Setup complete - Damage: {damageAmount}, Initialized: {isInitialized}, CollisionDamageEnabled: {collisionDamageEnabled}");
        }

        public void SetDamage(float damage)
        {
            damageAmount = damage;
        }

        public void SetCollisionLayers(LayerMask layers)
        {
            collisionLayers = layers;
        }

        public void SetIsPlayerShot(bool playerShot)
        {
            isPlayerShot = playerShot;
        }

        // MIGRATED from ProjectileStateBased collision damage control
        public void DisableCollisionDamage()
        {
            collisionDamageEnabled = false;
        }

        public void EnableCollisionDamage()
        {
            collisionDamageEnabled = true;
        }

        public void ResetForPool()
        {
            isInitialized = false;
            collisionDamageEnabled = true;
            isPlayerShot = false;
        }

        // MIGRATED from ProjectileStateBased.PlaySound with ProjectileAudioManager integration
        public void PlaySound()
        {
            // Use ProjectileAudioManager for sound playback (migrated from ProjectileStateBased)
            if (ProjectileAudioManager.Instance != null)
            {
                // Determine projectile type based on context (using correct enum values)
                ProjectileType projectileType = isPlayerShot ? ProjectileType.PlayerShot : ProjectileType.EnemyShot;

                // Play sound at projectile position
                ProjectileAudioManager.PlayProjectileSound(cachedTransform.position, projectileType);

                Debug.Log($"[ProjectileInteraction] Played {projectileType} projectile sound at {cachedTransform.position}");
            }
            else
            {
                Debug.LogWarning($"[ProjectileInteraction] ProjectileAudioManager not available for sound playback");
            }
        }

        // MIGRATED visual effects integration methods from ProjectileStateBased
        public void SetScale(float scale)
        {
            // Apply scale to transform
            cachedTransform.localScale = Vector3.one * scale;

            // Apply scale to particle systems
            if (particleSystems != null)
            {
                foreach (ParticleSystem ps in particleSystems)
                {
                    if (ps != null)
                    {
                        var main = ps.main;
                        main.startSize = main.startSize.constant * scale;
                    }
                }
            }

            // Apply scale to trail renderer
            if (trailRenderer != null)
            {
                trailRenderer.startWidth *= scale;
                trailRenderer.endWidth *= scale;
            }

            Debug.Log($"[ProjectileInteraction] Scale applied: {scale}");
        }

        public void EnableTrail(bool enable)
        {
            if (trailRenderer != null)
            {
                trailRenderer.enabled = enable;
                Debug.Log($"[ProjectileInteraction] Trail {(enable ? "enabled" : "disabled")}");
            }
        }

        // MIGRATED from ProjectileVisualEffects methods
        public void PlayAllEffects()
        {
            if (particleSystems != null)
            {
                foreach (ParticleSystem ps in particleSystems)
                {
                    if (ps != null)
                    {
                        ps.Play(true);
                    }
                }
            }
        }

        public void StopAllEffects()
        {
            if (particleSystems != null)
            {
                foreach (ParticleSystem ps in particleSystems)
                {
                    if (ps != null)
                    {
                        ps.Stop(true);
                        ps.Clear(true);
                    }
                }
            }
        }
    }
}
