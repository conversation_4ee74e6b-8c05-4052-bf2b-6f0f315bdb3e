# May 14
 2 | 
 3 | [Developing 'Hi-Fi RUSH' Backwards and Finding Our Positive Gameplay Loop](https://www.youtube.com/watch?v=pG4UxqRMNX0)
 4 | 
 5 | Insights
 6 | 
 7 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled.png)
 8 | 
 9 | Keyword Rhythm Action → What does that look like as definite gameplay?
10 | 
11 | Prototype video shows action and effects - consider how this and tutorial introduce mechanics and gameplay. 
12 | 
 13 | **Design Fundamentals**
 14 | 
 15 | - think - you’re the star of a music video!
 16 | 
 17 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%201.png)
 18 | 
 19 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%202.png)
 20 | 
 21 | Assume players will not hit the beat - being close enough is good enough for the sync effects
 22 | 
 23 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%203.png)
 24 | 
 25 | Animation speeds and hits are adjusted based on input time - slower or faster
 26 | 
 27 | **Positive Game Loop**
 28 | 
 29 | Interpolation creates a positive feedback loop naturally
 30 | 
 31 | Feeling like you hit the beat feels good
 32 | 
 33 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%204.png)
 34 | 
 35 | Playing to the music feels better, but if you miss rhythm you’re not really punished
 36 | 
 37 | Makes this more accessible
 38 | 
 39 | Feel like you are good at music even if you aren’t
 40 | 
 41 | **Make success easy to understand**
 42 | 
 43 | Make actions feel like they’re part of the song - HiFi Rush has a big HEY sound if you nail an attack
 44 | 
 45 | This is also done with dashing and a hi hat sound effect when its done on the beat
 46 | 
 47 | Showing the player that there is room for improvements 
 48 | 
 49 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%205.png)
 50 | 
 51 | Developing the game in reverse from the impact!
 52 | 
 53 | Thinking backwards
 54 | 
 55 | on a Micro and Macro level
 56 | 
 57 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%206.png)
 58 | 
 59 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%207.png)
 60 | 
 61 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%208.png)
 62 | 
 63 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%209.png)
 64 | 
 65 | Did a bunch of edits to fix scene transition , music choice for scene switching, and other things 
 66 | 
 67 | Playtesting
 68 | 
 69 | - feels a bit slow in general?
 70 | - Mechanics need to interact better with each other
 71 |     - time glitch
 72 |         - could highlight where the enemies are located
 73 |         - could allow you to move faster or slower while enemies move differently?
 74 |             - or maybe just enemy shot bullets are slowed drastically
 75 |     - slow time
 76 |         - allows you to move backwards?
 77 | - Enemy types on wave scene 3 need adjusting, need more projectiles flying around
 78 | 
 79 | Making adjustments to rewind and slow to beat speeds and how they affect the spline.
 80 | 
 81 | Definitely something good within this, tweak a few values to find better balance.
 82 | 
 83 | Likely need to speed up player character and some other things, more intensity
 84 | 
 85 | Cant make player model rewindable or seems to fall out of camera view. probably just need custom animation triggers for the movement changes
 86 | 
 87 | It appears bullets dont need their own local clocks so i’ve removed it, need to keep watching to make sure this doesnt break anything though, seems like it already has, so i put it back on but made it additive
 88 | 
 89 | Added this to update method of projectiles in attempt to get them to respond to time changes more effectively, but doesnt appear to be working - look more into the clock part
 90 | 
 91 | ![Untitled](May%2014%20d2596ca3160844599ddfb887d6d9f669/Untitled%2010.png)