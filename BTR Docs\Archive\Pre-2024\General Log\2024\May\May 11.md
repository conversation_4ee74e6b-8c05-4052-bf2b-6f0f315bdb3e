# May 11
 2 | 
 3 | Added <PERSON><PERSON><PERSON> Duotone to the project - definitely possible uses in places for this
 4 | 
 5 | Changed reticle spin to something not koreographer based - just hard coded tempo based
 6 | 
 7 | Easier, simpler, good enough for what it is - for now and probably the future
 8 | 
 9 | Wave delay before switch scene, may want to do it, but some technical things to solve if so. Need to re enable appropriate things, its a special transition. for example, player character. 
10 | 
11 | WaveCustomEventSorter is what handles a change to the next scene
12 | 
13 | Need to ensure on scene load that certain elements are active. maybe a scriptable object that defines this - may be more than one, since some scenes may have different requirements