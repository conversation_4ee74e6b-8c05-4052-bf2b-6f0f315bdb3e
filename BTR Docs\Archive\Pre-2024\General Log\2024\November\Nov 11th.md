# Nov. 11th

Here's a structured approach to evaluate your rail shooter:

### **1. Gamefeel Analysis**

- How responsive are the controls?
- Is there satisfying feedback on hits/misses?
- Does the camera movement feel natural?
- Are hitboxes and aim assist tuned properly?

### **2. Tension Management**

- Is there a good rhythm between high and low intensity moments?
- Do players have enough time to react?
- Are there meaningful consequences for failure?
- Does difficulty scale appropriately?

### **3. Player Investment Loop**

- What rewards keep players engaged?
- Are there meaningful progression systems?
- Do players feel a sense of mastery over time?
- Are there short and long-term goals?

### **4. Engagement Vectors**

Score your game 1-5 in each area:

- Mechanical satisfaction (shooting feel)
- Challenge balance
- Reward systems
- Progression clarity
- Visual/audio feedback
- Player agency within constraints

### **5. Core Loop Analysis**

Evaluate each phase:1. Player Input

- Is aiming intuitive?
- Do controls feel responsive?
- System Response
- Is feedback clear and satisfying?
- Are hit reactions appropriate?
- Player Reward
- Are achievements meaningful?
- Does success feel earned?

---

**GameFlow Analysis**

Score each element 1-5:

- Concentration
- Is attention properly directed?
- Are distractions minimized?
- Is workload appropriate?
- Challenge
- Does difficulty scale smoothly?
- Are skills properly tested?
- Is failure informative?
- Skills
- Are core mechanics clear?
- Is mastery achievable?
- Do players feel improvement?
- Control
- Are inputs responsive?
- Is feedback immediate?
- Do players feel agency?

### **2. First Hour Experience**

Evaluate:

- Holdouts
- What keeps players engaged early?
- Which mechanics show promise?
- What creates intrigue?

2. Information Flow

- How are mechanics introduced?
- Is progression clear?
- Are goals well communicated?
- Early Engagement
- When do players first feel mastery?
- What creates early wins?
- Where do players struggle?

### **3. Flow State Analysis**

Check for:1. Clear Goals

- Are objectives obvious?
- Is progress measurable?
- Do players know what's next?
- Immediate Feedback
- Are hits/misses clear?
- Do actions have clear results?
- Is improvement trackable?
- Challenge/Skill Balance
- Does difficulty match player skill?
- Are there appropriate peaks/valleys?
- Is frustration minimized?

[Nov. 11/12th Bug Testing](Nov%2011th%2013bffc7f811f80528383c1aac2186bb7/Nov%2011%2012th%20Bug%20Testing%2013cffc7f811f805a88f8d78308c37761.md)

Made Reticle slightly bigger to see if that feels better

- Was comparing with Rez

---

**Game to check out**

- Omega Boost
- Danger Forever
- RayStorm
- RayCrisis
- Thunder Force V
- G-Darius
- Soukyugurentai / Terra Diver
- Zero Gunner 2
- Shienryu Explosion
- Zaxxon Motherbase
- Under Defeat
- Xevious 3D
- Brave Blade
- Nebulas Ray
- Soulstar
- TOP NEP
- Missile Dancer 2
- Night Striker S
- Galaxy Force  2
- Child of Eden
- Xevious Resurrection
- **ALLTYNEX Second**
- XTOM 3D  [https://www.myabandonware.com/game/xtom-3d-g0a](https://www.myabandonware.com/game/xtom-3d-g0a)
- Silpheed remake

[https://www.youtube.com/watch?v=SxNQSXtaljg](https://www.youtube.com/watch?v=SxNQSXtaljg)