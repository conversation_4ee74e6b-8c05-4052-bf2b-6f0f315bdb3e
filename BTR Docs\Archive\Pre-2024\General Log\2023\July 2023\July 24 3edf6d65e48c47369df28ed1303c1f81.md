# July 24

Update on mesh disappearing at times in game

“Most likely your skinned positions are going outside of the calculated skinned mesh renderer's bounds and therefore getting frustum culled unexpectedly.

Annoyingly, Unity's importer only calculates the extents of a skinned mesh renderer bounds with animations on the same source FBX. So, if that's you're issue, you'll need to update the renderer's bound yourself in script.

[https://docs.unity3d.com/ScriptReference/Renderer-localBounds.html](https://docs.unity3d.com/ScriptReference/Renderer-localBounds.html)

You can also turn on "Update When Offscreen" on your skinned mesh renderers. It's inefficient, but simple fix. Also, if it fixes your problem, then it confirms that your bounds is the issue.

[https://docs.unity3d.com/ScriptReference/SkinnedMeshRenderer-updateWhenOffscreen.html](https://docs.unity3d.com/ScriptReference/SkinnedMeshRenderer-updateWhenOffscreen.html)”

Using the quick fix seems to work!

Broke something in Shooter movement with refactor - need to look at old code to fix