# June 11

Aggregating the last week of issues / concerns to tackle

- Need to never be locking on when moving into next level, and need to make sure it doesnt transition into a lock on section on next level start. need to investigate how to handle this.
- remove static shooter bullets from radar. only bullets with homing on player show up
- implement descending health and see how game feels - is it fair?
- Can I get snake tail enemies working well?
- Make more levels by alternating path player is on / flipping things upside down or sideways
- More FX needed, more feedbacks. Think about what player needs to know - maximum locks, projetiles are close by, etc
- Consider more sounds to tell whats happening.
- Build out dual snake boss area
- FX for time slow / rewind glitch things. very lacking right now.
    - what are the utitily of these? give more of a reason to reach for them during gameplay - think on scenarios
    - can slow time initiate doppler on projectiles?
    - Look into more interesting VFX like Inkblot, etc
- watch jason booth talk again about optimization - what can be applied
- Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Previous things to do a month ago - verify this list with what needs to be done

[May 15](../May%209ac535a1c587434abe88186ee2b96d25/May%2015%20b4778e7f23ba46e39d3f0f43659cc83d.md) 

Revisit Idea

- more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
    - Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm.
    

Parent Constraint script is neat / may be useful for other scenarios

- Infinite snake objects in background?
- Other scripts from this lot that could be useful as well?