# BTR Scoring System - Quick Setup Guide

## 🚀 5-Minute Setup

### Step 1: Add GameObjects to Scene

1. **Create Scoring System GameObject**:
   - Right-click in Hierarchy → Create Empty
   - Name: "ScoringSystem"
   - Add Component → KillStreakSystem
   - Add Component → ScoreManagerIntegration

2. **ScoreManager** (should already exist as singleton)

### Step 2: Configure Components

**KillStreakSystem** (leave defaults):
```
✅ Streak Time Window: 3
✅ Base Multiplier: 1.5
✅ Multiplier Increment: 0.5
✅ Max Multiplier: 5
✅ Enable Debug Logs: false
```

**ScoreManagerIntegration**:
```
✅ Enable Debug Logs: false
✅ Enable Score Validation: true
✅ Max Events Per Frame: 5

✅ Enemy Kill Base Score: 10
✅ Damage Score Multiplier: 1
✅ Wave Completion Base Score: 50

🔗 Kill Streak System: (auto-found on same GameObject)
```

### Step 3: Enable Integration

**ScoreManager GameObject**:
```
[Integration Settings]
✅ Use Integrated Scoring: true
```

### Step 4: Test

**Play the game and check console for**:
```
[ScoreManager] Integrated scoring system connected
[KillStreakSystem] Initialized - Time Window: 3s, Base Multiplier: 1.5x, Max: 5x
[ScoreManagerIntegration] System initialized and ready
```

## 🎯 How It Works

### Before (Legacy)
```
Enemy Dies → ScoreManager.OnEnemyKilled() → +10 points
```

### After (Integrated)
```
Enemy Dies → ScoreManagerIntegration → Apply Kill Streak → ScoreManager → +15 points (1.5x multiplier)
```

## 🔧 Quick Test

**In Play Mode**:
1. Kill 1 enemy: +10 points (no bonus)
2. Kill 2nd enemy (within 3s): +15 points (1.5x multiplier) + "Double Kill!"
3. Kill 3rd enemy (within 3s): +20 points (2.0x multiplier) + "Triple Kill!"

## 🐛 Troubleshooting

**❌ Integration not working?**
- Check "Use Integrated Scoring" is enabled in ScoreManager
- Verify ScoringSystem GameObject exists in scene with both components
- Check console for error messages

**❌ Kill streaks not working?**
- Ensure KillStreakSystem is on the ScoringSystem GameObject
- Check time window (default 3 seconds)
- Enable debug logs to see streak changes

**❌ No score events?**
- Verify GameEventsManager exists in scene
- Check GameEvents asset is assigned
- Enable debug logs in ScoreManagerIntegration

## 📊 Expected Behavior

### Kill Streak Progression
```
Kill 1: 10 points (1.0x) - No streak
Kill 2: 15 points (1.5x) - "Double Kill!"
Kill 3: 20 points (2.0x) - "Triple Kill!"
Kill 4: 25 points (2.5x) - "Multi Kill!"
Kill 5: 30 points (3.0x) - "Mega Kill!"
```

### Other Events
```
Player Damage: -25 points
Wave Complete: +50 points (×wave number)
Milestone Bonus: +5 to +50 points
```

## 🎮 Integration Points

**For UI Systems**:
```csharp
// Subscribe to streak events
gameEvents.OnStreakMilestone += (streak, name) => {
    ShowMilestonePopup(name);
};

// Subscribe to score events
ScoreManagerIntegration.OnScoreEventProcessed += (data) => {
    ShowScoreEffect(data.finalScore);
};
```

**For Gameplay Systems**:
```csharp
// Trigger custom score events
ScoreManagerIntegration.Instance.TriggerScoreEvent(
    ScoreEventType.BonusPickup, 
    25f, // base score
    1.5f // custom multiplier
);
```

---

## ✅ Checklist

- [ ] ScoringSystem GameObject added to scene
- [ ] KillStreakSystem component added to ScoringSystem
- [ ] ScoreManagerIntegration component added to ScoringSystem
- [ ] ScoreManager "Use Integrated Scoring" enabled
- [ ] Console shows successful initialization
- [ ] Kill streaks working (test with 2+ enemy kills)
- [ ] Score events firing properly

**🎉 Setup Complete!** Your centralized scoring system is ready to use.
