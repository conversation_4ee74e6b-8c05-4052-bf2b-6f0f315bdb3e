# April 7

Looking deeper in spawner, received dev response 

> Hi <PERSON>,
> 

> You can inform the spawning system that an item has been destroyed using the static ‘SpawnableItems.InformSpawnableDestroyed(…)’ method. You can simply pass in the transform of the destroyed item and that all spawners should be updated accordingly and proceed as you would expect.
> 

> I hope that helps you. Let me know if there is anything else.
> 

> Regards,
> 

> Trivial Interactive.
> 

Adding this to enemy death method

Works! Sort of...

![Untitled](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled.png)

Not changing to a new Wave - need to see why!

Things to change.... 

Add SimpleWaveHud to scene (Arena Demo has it on the player)

![Untitled](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled%201.png)

Add Wavehint to Canvas

![Untitled](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled%202.png)

Add Enemy Hint?

![Untitled](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled%203.png)

Add event spawn controller to object in scene?

Sent email because I cant seem to get this working, got a quick response so may hear back tomorrow as well

Did some miro documentation - need to fix this wave spawning issue but also need to make a good play moving forward

Once wave spawning works - how do we imaging this all to work? Go to core documents, Electronic music documents, and consider this!