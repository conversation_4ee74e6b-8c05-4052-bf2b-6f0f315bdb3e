# Dec 11

[Featured Blog | How to create a 'fair' auto-aiming system in a robot shooter](https://www.gamedeveloper.com/design/how-to-create-a-fair-auto-aiming-system-in-a-robot-shooter-)

Idea- Shrink and grow cursor to indicate target locking 

Change Reticle over to a system based on Shapes asset pack

Some parts done, working on proejcitle and enemy lock on aspects / animations 

- some difficulties implementing the same shriking box again

Need proper effect for ricochet - blast no longer works?

- may also need to revert to earleir version of how this works - verify
- need adjustments to this in general

Should i just have all projectiles aim for the direction the player is currently facing? will this mean less mvoement? is this worth playnig with?

[Build Optimization](Dec%2011%20Build%20Optimization.md)