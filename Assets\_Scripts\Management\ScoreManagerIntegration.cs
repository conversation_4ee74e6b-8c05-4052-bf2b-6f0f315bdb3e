using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace BTR
{
    /// <summary>
    /// Score event types for the centralized scoring system.
    /// </summary>
    public enum ScoreEventType
    {
        EnemyKill,      // Enemy defeated
        PlayerDamage,   // Player takes damage
        TimeDecay,      // Score decay over time
        BonusPickup,    // Bonus items/milestones
        WaveCompletion, // Wave/section completion
        Milestone       // Kill streak milestones
    }

    /// <summary>
    /// Data structure for score events passed through the system.
    /// </summary>
    [System.Serializable]
    public struct ScoreEventData
    {
        public ScoreEventType eventType;
        public float baseScore;
        public float multiplier;
        public float finalScore;
        public int streakCount;
        public string eventDescription;
        public float timestamp;

        public ScoreEventData(ScoreEventType type, float score, float mult = 1f, int streak = 0, string description = "")
        {
            eventType = type;
            baseScore = score;
            multiplier = mult;
            finalScore = score * mult;
            streakCount = streak;
            eventDescription = description;
            timestamp = Time.time;
        }
    }

    /// <summary>
    /// Centralized scoring integration system that handles all score-related events,
    /// applies multipliers, integrates with kill streak system, and coordinates
    /// with the core ScoreManager for final score updates.
    /// </summary>
    public class ScoreManagerIntegration : MonoBehaviour
    {
        #region Singleton
        private static ScoreManagerIntegration instance;
        public static ScoreManagerIntegration Instance => instance;
        #endregion

        [Header("Integration Settings")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool enableScoreValidation = true;
        [SerializeField] private int maxEventsPerFrame = 5;

        [Header("Score Multipliers")]
        [SerializeField] private float enemyKillBaseScore = 10f;
        [SerializeField] private float damageScoreMultiplier = 1f;
        [SerializeField] private float timeDecayMultiplier = 1f;
        [SerializeField] private float waveCompletionBaseScore = 50f;
        [SerializeField] private float milestoneBaseScore = 5f;

        [Header("System References")]
        [SerializeField] private KillStreakSystem killStreakSystem;

        // Event processing
        private Queue<ScoreEventData> eventQueue = new Queue<ScoreEventData>();
        private Dictionary<ScoreEventType, float> scoreMultipliers = new Dictionary<ScoreEventType, float>();

        // Cached references
        private GameEvents gameEvents => GameEventsManager.Instance?.Events;

        // Static events for external systems
        public static event System.Action<float> OnScoreCalculated;
        public static event System.Action<ScoreEventData> OnScoreEventProcessed;
        public static event System.Action<ScoreEventType, float, float> OnScoreEventQueued; // type, base, multiplier

        #region Unity Lifecycle

        private void Awake()
        {
            // Singleton setup
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSystem();
            }
            else
            {
                Destroy(gameObject);
                return;
            }
        }

        private void Start()
        {
            ValidateReferences();
            SubscribeToEvents();
            
            if (enableDebugLogs)
            {
                Debug.Log("[ScoreManagerIntegration] System initialized and ready");
            }
        }

        private void Update()
        {
            ProcessEventQueue();
        }

        private void OnDestroy()
        {
            UnsubscribeFromEvents();
            
            // Clear static events
            OnScoreCalculated = null;
            OnScoreEventProcessed = null;
            OnScoreEventQueued = null;
        }

        #endregion

        #region Initialization

        private void InitializeSystem()
        {
            // Initialize score multipliers
            scoreMultipliers[ScoreEventType.EnemyKill] = 1f;
            scoreMultipliers[ScoreEventType.PlayerDamage] = damageScoreMultiplier;
            scoreMultipliers[ScoreEventType.TimeDecay] = timeDecayMultiplier;
            scoreMultipliers[ScoreEventType.BonusPickup] = 1f;
            scoreMultipliers[ScoreEventType.WaveCompletion] = 1f;
            scoreMultipliers[ScoreEventType.Milestone] = 1f;
        }

        private void ValidateReferences()
        {
            // ScoreManager is accessed as singleton - no need to assign
            if (ScoreManager.Instance == null)
            {
                Debug.LogError("[ScoreManagerIntegration] ScoreManager singleton not found! Make sure ScoreManager exists in the scene.");
            }

            // KillStreakSystem should be on the same GameObject
            if (killStreakSystem == null)
            {
                killStreakSystem = GetComponent<KillStreakSystem>();
                if (killStreakSystem == null)
                {
                    Debug.LogWarning("[ScoreManagerIntegration] KillStreakSystem not found on same GameObject. Kill streak multipliers will not be applied.");
                }
            }

            if (gameEvents == null)
            {
                Debug.LogError("[ScoreManagerIntegration] GameEvents not found! Make sure GameEventsManager is in the scene.");
            }
        }

        #endregion

        #region Event Subscription

        private void SubscribeToEvents()
        {
            if (gameEvents != null)
            {
                gameEvents.OnEnemyDeath += HandleEnemyDeath;
                gameEvents.OnPlayerDamaged += HandlePlayerDamaged;
                gameEvents.OnWaveCompleted += HandleWaveCompleted;
            }

            if (killStreakSystem != null)
            {
                killStreakSystem.OnMilestoneReached += HandleStreakMilestone;
            }
        }

        private void UnsubscribeFromEvents()
        {
            if (GameEventsManager.Instance != null && gameEvents != null)
            {
                gameEvents.OnEnemyDeath -= HandleEnemyDeath;
                gameEvents.OnPlayerDamaged -= HandlePlayerDamaged;
                gameEvents.OnWaveCompleted -= HandleWaveCompleted;
            }

            if (killStreakSystem != null)
            {
                killStreakSystem.OnMilestoneReached -= HandleStreakMilestone;
            }
        }

        #endregion

        #region Event Handlers

        private void HandleEnemyDeath(Transform enemy)
        {
            TriggerScoreEvent(ScoreEventType.EnemyKill, enemyKillBaseScore);
        }

        private void HandlePlayerDamaged(float damage)
        {
            TriggerScoreEvent(ScoreEventType.PlayerDamage, -damage, damageScoreMultiplier);
        }

        private void HandleWaveCompleted()
        {
            // Calculate wave bonus based on current wave
            float waveBonus = waveCompletionBaseScore;
            if (ScoreManager.Instance != null)
            {
                waveBonus *= ScoreManager.Instance.CurrentSceneWaveCount;
            }
            
            TriggerScoreEvent(ScoreEventType.WaveCompletion, waveBonus);
        }

        private void HandleStreakMilestone(int streakCount, string milestoneName, int bonusScore)
        {
            TriggerScoreEvent(ScoreEventType.Milestone, bonusScore, 1f, streakCount, milestoneName);
        }

        #endregion

        #region Public API

        /// <summary>
        /// Main entry point for triggering score events. All scoring should go through this method.
        /// </summary>
        public void TriggerScoreEvent(ScoreEventType eventType, float baseScore, float customMultiplier = 1f, int streakCount = 0, string description = "")
        {
            // Calculate final multiplier
            float finalMultiplier = CalculateFinalMultiplier(eventType, customMultiplier);
            
            // Create event data
            ScoreEventData eventData = new ScoreEventData(eventType, baseScore, finalMultiplier, streakCount, description);
            
            // Queue the event
            eventQueue.Enqueue(eventData);
            
            // Fire queued event
            OnScoreEventQueued?.Invoke(eventType, baseScore, finalMultiplier);
            
            if (enableDebugLogs)
            {
                Debug.Log($"[ScoreManagerIntegration] Queued score event: {eventType} - Base: {baseScore}, Multiplier: {finalMultiplier:F1}x, Final: {eventData.finalScore}");
            }
        }

        /// <summary>
        /// Gets the current score multiplier for a specific event type.
        /// </summary>
        public float GetScoreMultiplier(ScoreEventType eventType)
        {
            return scoreMultipliers.ContainsKey(eventType) ? scoreMultipliers[eventType] : 1f;
        }

        /// <summary>
        /// Sets a custom multiplier for a specific event type.
        /// </summary>
        public void SetScoreMultiplier(ScoreEventType eventType, float multiplier)
        {
            scoreMultipliers[eventType] = multiplier;
            
            if (enableDebugLogs)
            {
                Debug.Log($"[ScoreManagerIntegration] Set {eventType} multiplier to {multiplier:F1}x");
            }
        }

        /// <summary>
        /// Gets the current event queue size (useful for debugging).
        /// </summary>
        public int GetQueueSize()
        {
            return eventQueue.Count;
        }

        /// <summary>
        /// Clears all pending events in the queue.
        /// </summary>
        public void ClearEventQueue()
        {
            eventQueue.Clear();
            
            if (enableDebugLogs)
            {
                Debug.Log("[ScoreManagerIntegration] Event queue cleared");
            }
        }

        #endregion

        #region Event Processing

        private void ProcessEventQueue()
        {
            int eventsProcessed = 0;
            
            while (eventQueue.Count > 0 && eventsProcessed < maxEventsPerFrame)
            {
                ScoreEventData eventData = eventQueue.Dequeue();
                ProcessScoreEvent(eventData);
                eventsProcessed++;
            }
        }

        private void ProcessScoreEvent(ScoreEventData eventData)
        {
            // Validate score if enabled
            if (enableScoreValidation && !ValidateScoreEvent(eventData))
            {
                Debug.LogWarning($"[ScoreManagerIntegration] Invalid score event rejected: {eventData.eventType}");
                return;
            }

            // Apply the score through ScoreManager singleton
            if (ScoreManager.Instance != null)
            {
                int scoreToAdd = Mathf.RoundToInt(eventData.finalScore);
                ScoreManager.Instance.AddScore(scoreToAdd);
                
                // Handle special event types
                HandleSpecialEventTypes(eventData);
            }

            // Fire events
            OnScoreCalculated?.Invoke(eventData.finalScore);
            OnScoreEventProcessed?.Invoke(eventData);

            if (enableDebugLogs)
            {
                Debug.Log($"[ScoreManagerIntegration] Processed score event: {eventData.eventType} - Final Score: {eventData.finalScore}");
            }
        }

        private void HandleSpecialEventTypes(ScoreEventData eventData)
        {
            switch (eventData.eventType)
            {
                case ScoreEventType.EnemyKill:
                    // Update kill streak system
                    if (killStreakSystem != null)
                    {
                        killStreakSystem.OnEnemyKilled();
                    }
                    
                    // Update kill tally
                    if (ScoreManager.Instance != null)
                    {
                        // The ScoreManager will handle this through its integration hooks
                    }
                    break;

                case ScoreEventType.Milestone:
                    // Fire milestone event for UI
                    if (gameEvents != null && !string.IsNullOrEmpty(eventData.eventDescription))
                    {
                        gameEvents.TriggerStreakMilestone(eventData.streakCount, eventData.eventDescription);
                    }
                    break;
            }
        }

        #endregion

        #region Multiplier Calculation

        private float CalculateFinalMultiplier(ScoreEventType eventType, float customMultiplier)
        {
            float baseMultiplier = GetScoreMultiplier(eventType);
            float killStreakMultiplier = 1f;

            // Apply kill streak multiplier for enemy kills
            if (eventType == ScoreEventType.EnemyKill && killStreakSystem != null)
            {
                killStreakMultiplier = killStreakSystem.GetCurrentMultiplier();
            }

            return baseMultiplier * customMultiplier * killStreakMultiplier;
        }

        #endregion

        #region Validation

        private bool ValidateScoreEvent(ScoreEventData eventData)
        {
            // Basic validation rules
            if (float.IsNaN(eventData.baseScore) || float.IsInfinity(eventData.baseScore))
                return false;

            if (float.IsNaN(eventData.multiplier) || float.IsInfinity(eventData.multiplier))
                return false;

            if (Mathf.Abs(eventData.finalScore) > 10000f) // Prevent extremely large scores
                return false;

            return true;
        }

        #endregion

        #region Debug Methods

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugTriggerEnemyKill()
        {
            TriggerScoreEvent(ScoreEventType.EnemyKill, enemyKillBaseScore);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugTriggerPlayerDamage(float damage = 10f)
        {
            TriggerScoreEvent(ScoreEventType.PlayerDamage, -damage);
        }

        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugTriggerWaveCompletion()
        {
            TriggerScoreEvent(ScoreEventType.WaveCompletion, waveCompletionBaseScore);
        }

        public string GetDebugInfo()
        {
            return $"Queue Size: {eventQueue.Count}, Kill Streak Active: {(killStreakSystem != null ? killStreakSystem.IsStreakActive() : false)}";
        }

        #endregion
    }
}
