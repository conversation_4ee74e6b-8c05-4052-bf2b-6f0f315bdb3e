# World Building

Sanderson’s Laws

1) Your ability to solve problems with magic in a satisfying way is directly proportional to how well the reader understands said magic

Deus Ex Machina is a bad thing! “god from the machine”

“Deus ex machina is a plot device whereby a seemingly unsolvable problem in a story is suddenly and abruptly resolved by an unexpected and unlikely occurrence”

SIDENOTE: the videogame: This name was chosen for multiple reasons. Firstly, its origin in the phrase *deus ex machina* was a nice connection to the plot of the game since multiple forces in *Deus Ex* aspire to attain god-like powers. It was also intended to be a subtle critique of other games at the time with bad story telling (presumably through over-use of deus ex machina to resolve portions of the plot). In addition, according to [<PERSON>](https://deusex.fandom.com/wiki/<PERSON>_<PERSON>), the computer being used to play the game is itself a "God-in-the-machine".[[12]](https://deusex.fandom.com/wiki/Deus_Ex#cite_note-12)

Continum you can be on with the magic system 

Problem solving / magic as science on one side

Sense of wonder / Mystified on the other side

If you give your characters tools, and then watch those character use those tools to problem solve and get out of situations, that’s satisfying to a reader.

<PERSON>’s paw story relies on that sense of wonder, but twists to horror. This is a soft magic. 

LOTR uses a mostly hard magic with the ring. You’re not ultimately sure what can be done with the ring by <PERSON>uron, but you know what it does to others. Makes you invisible and extends your life span, but you can be seen by sauron and you turn into gollum. 

What can gandalf do? This is more of a soft magic, we dont know eveyrthing he can do. He fights the balrog off screen. You dont know how he was revived as gandalf the white, or other things. He is there to make the hobbits feels small.

Compare Beowulf to the hobbit - similar but we got a normal person in the hero role (frodo/bilbo)

Gandalf tells us this is a world much bigger than we understand, and as readers we dont even need to comprehend it. Just feel small in the face of it. 

Name of the wind book - read a summary on this - does a split between engineering as magic and poetry/music as magic - the defined and indefinable - could be helpful!

Read asimov’s three laws stories!

Setup of the 2nd movie with gandalf saying look for me on the 5th day, narrative does lots for us to forget this idea that he will save them if the can last, but ultimately it still leaves so idea of what could happen lingering

Setup of 3rd movie is differnet, mines tieras, they’re fighting for their life, aragorn goes to get the ghosts on their side and save the day.

These seem similar in a top down view, but it’s not quite as satisfying in the 3rd film. 

This is about promises and payoffs. In both, solving a problem with an external force. But it’s all about setup. 

How do you avoid the dread info dump?

Have the character go out and experiment and the teacher guides them along the way. Explains the magic while keeping things exciting. 

Mistborn screenplay example of combining two scenes (learning and a first heist) to explain but keep stakes. 

Point of the scene might be to expalin the magic, but you should be primarily about the character and setting details. EMPHASIS should be making usre the character is interesting and showing as much or more about the character or providing excitement. 

2) Flaws or limitations or costs are more interesting than powers

**Read about Sanderon’s allomancy system. Seems interesting! Like magneto!**

You can fly as long a syour parents are both sleeping example

Scott Card- you can use the magic but one of your living relatives will be killed?

Wheel of time - use the magic but you might go crazy and kill everyone you love

Superman classic example of too much power and difficult to write

Superman stories

1) Someone can punch harder than superman

2) Someone has kryptonite - no powers

3) I am unable to use my powers effectively to solve this problem OR i want someone to fall in love with me but they dont work

Costs of certain actions is where your story is going to occur. 

Flaws are basically things you could change, or the magic you dont understand yet, and through effort and character development you could change these things.

Sandersons book whole story really based around ‘ there’s a flaw in the magic and we dont knwo what it is’ 

Many stories based around the character or people not fully grasping the magic, and if they apply themselves there is a point in the narrative where they will figure it out 

Flaw is something you can work on and fix

Limitations are different

There’s no fixing it, you work with it, rather than fix it. Example is you are born with one arm, want to play in the NFL, need to work within that limitation to achieve their goal.

Cost

What is it emotionally, physically, etc. Equivalent to the bullets in a gunslingers gun. Runs out when its conveinent to do so sometimes. Can get into a different story with losing the magic and trying to get it back. Or rich people can use the magic more - theme! Or three wishes but they go horribly wrong. 

Flaw vs Limitatioin

If the chracter needs to get better at a thing, it’s a flaw. Fighting Darth Vader, first time lost his hand, 3rd movie he knows how to win. 

3) Before adding something new to your magic (setting) see if you can instead expand what you have

Sanderon’s 0th Law

Always air on the side of what is awesome!

**World Building Week 2**

Why?

The impossible made plausible

Theme

Sense of Wonder / Exploration

Sheer coolness

Cultural butterfly effect - when you change one thing how it affects the world

Approach real world ideas in a disconnected way - avoid being immediately polarizing

Play god (like The Sims)

Sci Fi / Fantasy - is the thing that defines us as writers of these areas but is often the least important part of our stories

Good characters - good story - are most important, can do ok with weak world building

World Builders disease - so enthralled with building out the world that you never start your story

World building in service of story

You wanna ‘avoid billboards to watch on the way to your story’

Ultimately you want people to say “these stories and characters are fascinating, and BTW i know a whole bunch about hogwarts now”

Avoid the encylopedia entry approach!

Maid and Butler dialog - comes from stage - where they explain a bunch of the things they already know.  Avoid this! Not very interesting. It’s stilted

Feels like character is not acting in their own motivations, purely the authors motivation to get out certain information.

Give the reader less than you think they need is generally a good thing

Pyramid of Abstraction

Imagine your descriptions as a pyramid

The goal is to form the base of a pyramid so that when you talk about things that are a little more high level or a little more abstract, it’s understood

The more you want to ruminate on art on something in a story, the more the reader will think this is the author giving me a lesson / lecture rather than the character talking

The way you offset that is that you let the abstract be the tip on the pyramid that you earn with the grounded concrete language and place 

![Untitled](World%20Building%20407fdd9819364aefb6274b3ad6823cf0/Untitled.png)

Ground them first in the setting!

Abstract vs Concrete

Love would sit more on the abstract side. You can pull that down by talk about what loves mean to this person. 

Dog would also be abstract. When you say dog to a room, everyone imagines a different thing!

“The character saw a dog”

OR

“A mangy looking white dog missing one leg, whimpering on the side of the street in a puddle”

That pulls the dog down into the concrete. 

![Untitled](World%20Building%20407fdd9819364aefb6274b3ad6823cf0/Untitled%201.png)

But making the dog more concrete is not always better.

Requires way more words! Also think of SHOW dont TELL, sometimes the SHOW is enoguh.

Ex> “He was a nervous wreck. He sat at the table tapping his pen wildly agains teh table”

You dont need the thesis sentence here, the show is enough

Delete these out!

Get rid of the passive voice, get rid of adverbs, get rid of too many descriptive words that dont pull you down on the pyramid

If you can go down the pyramid while using fewer words, that will often be the best choice. 

Dan Wells” If you want the reader to believe you about the big things, there’s this big problem in the political system, you often describe one small thing very concretely that impacts the characters in a wya that really makes you understand this.”

Almost always better to start your character in a sitaution where they 1) wnat something (even if it snot related to the main plot) 2) they cant have it and we get to see them working pro actively to get the thing they want and we explain ONLY as much world building as you need for that isngle instance - that character - that single scene in aworld - to ground us 

BEFORE you pile on more world building

Leading with the magic in fantasy can often work, in that you use a small instance of it to indicate a much larger world to explore, but you need to master that one thing first. Must be grounded in character motivation. 

Can functionally work like  this

Character motivation - world buidling - chracter motivation - concrete description - world building

Avatar the last airbender as great example of world building mixed with character development

It uses a cliche magic system but that becomes ‘a feature not a bug’ cause its done so well and they have to explain yes since its well understood

Have to be careful - vampire example, you carry the baggage - if your vampire is significantly different, you need to constantly expain this 

HAVING A MAP AT HE BEGINNING OF A BOOK IS COOL - Avatar also did this

Firefly - the world emphasizes the desperation and difficulties the chracters have. an uncaring universe where you can get squashed like a bug. Using the wild west metaphor shows they’re in a place where they can get away with things

They are placed in a rebellion that lost - excellent way to explain that theres an evil empire. ties into “i fought, i lost, i stopped caring” but secretly the really still care - that says a lot about the character!

Rule of thumbL Like Firefly, you want your setting to become a character unto itself. Treat it much like charcter. Give it interesting quirks

Dune - The spice is a big part of why it all works. It’s what the universe needs to function, its essential the mythology and lore of the people living on this planet, and immediately important to the political control of the character in charge of it

People all want it for different reasons 

Dune movie - use of negative space to make people feel small

["Rapid" Review - Dune (2021)](https://www.youtube.com/watch?v=hm3nvNPTM8w)

Theme as visual motif ideas present are interesting!

How do you build a system where world building is done in service of your story?

Two categories of world buidling

| **Physical Setting** | **Cultural Setting** |
| --- | --- |
| Weather Patterns | Religion |
| Techtonic Activity | Government |
| Map | Economics |
| Flora and Fauna | Gender Roles |
| Magic | Borders |
| Visuals | Fashion |
| Cosmology | Food Lore |
| Terrain and Climate | History |
| Races? | Rites of Passage |
|  | Social Ladders (hierarchy) |
|  | Languages |
|  | Taboos / Morees |
|  | Military Tradition |
|  | Greetings |
|  | Swear Words |

You can spend your whole life building this out, but you cant do that realistically. A book a year for most writers is necessary! So how do you fill out enough of this?

Sanderson strategy. Pick one from physical side and a couple form cultural side.

Ex. A fancy weather pattern which many things stem from. 

Mistborn - Magic system and Political system

You can only tackle a few things, diminishing returns after that. Pick the few things that heighten your story and characters.

**Several Genres of story** 

Action Adventure

Mystery

Romance

Horror

Example in class: Climate that enhances your action adventure story. 

- Living on a volcanic planet, dangerous biome, would have to avoid falling in lava
- Sudden change in status quo / sudden climate change - great start to Action Adventure

Fashion enhancing a mystery story

- society wearing masks
- Murder happens at comicon and it was Naruto day
- Society of clones / everyone looks the same
- Maybe special embroidery in some clothes, and a detective solves crimes based on the fashion

Military structure enhancing Romance story

- Breaking military hierarchy for love
- On opposite sides of the war

Economics enhacning a Horror Sotry

- Monsters chase you but you can pay people off to put you lower on the list of who is attacked, you have to keep paying this off to stay alive
- Adam SMith’s invisble hand that controls the economy is a real hand
- Moneky’s paw - Economic pressure making people make terrible decisions
- Everyone’s a contract killer

Remember rule #3  - Doing one of these really well is almost always better than trying to do 5% of all of them 

Picking an idea and interconnection it into the rest, is a lot of times the best way to create

Ex. Weather - Epic hurricane hits every 3 days

- will lead to how does it affect climate?
- how does it effect flora and fauna?

You can then mention little things like some animal has a shorter lifecycle due to it, or some aspect of the world is adjusted due to that.

Showing one small effect of the thing you chose allows the readers imagination to take it into many more directions 

Stormlight - the storms that occur bring the stuff needed for the magic to take place, therefore how often they happen and your ability to tap into that is important to the magical system 

SUddenly, we starting asking how does this affect so many things. 

Religion, Economy, Fashion, etc 

Not all of it interconnects - maybe gender roles would be a different thing al together. 

Internal Logic VS External Logic - does he bring this up?

**Worldbuilding Q&A**

Greatest strength is greatest flaw - good way to write characters