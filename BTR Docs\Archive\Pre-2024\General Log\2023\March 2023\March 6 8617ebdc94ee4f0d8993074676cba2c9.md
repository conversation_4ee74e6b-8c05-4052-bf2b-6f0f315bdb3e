# March 6

<aside>
💡 Added Screen resolution script to Managers for testing which resolution gets set when game is loaded. Test this on Steam Deck!

</aside>

Considering UI Overhaul

![Untitled](March%206%208617ebdc94ee4f0d8993074676cba2c9/Untitled.png)

**Left Side**

Health - is this needed?

Score / Time

Bullets Locked - currently a number - Use a status Bar?

Current Wave of enemies

Add Enemies locked? Maybe an attribute you get later 

Working on Pause Menu UI - Integrated Shift UI

Changed colours and fonts

Can enable / disable HUD

Implemented Graphics settings

- Field of View
- Draw Distance
- Resolution (maybe works?)

Partially have controller as curson implemented for Pause menu

Cant seem to click anything! Mouse works fine though

Added Rewind Particle effect - cubes everywhere!

**Lighting**

For moving objects, you can use static light maps - "Lightmap static" (or, I think they renamed it to Contribute GI in newer versions) allows you to do this 

Change Control scheme - Use Triggers for main actions, buttons for time rewind etc