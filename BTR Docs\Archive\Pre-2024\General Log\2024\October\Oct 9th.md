# Oct. 9th

Fixing small issues, like major performance issue in Scene 3. Problems with Death effect playing for all projectile deaths - should not happen when lifetime runs out

This did not fix the problem - but it was a problem regardless.

Verify these work in the right scenarios - and work properly VISUALLY

removing Enemy Death Particles from Projectile Manager to help debug particle system issues in regards to performance 

Scene 3 just not working - look at later I guess

Maybe work on Scene 5?

Oct. 10th 

Looking at Scene 3 again

Trying to find what particle system activity is causing these performance issues

Seems to be an issue with the level ouroboros model? when not being used I dont have these issues

**Optimziations to try**

Projectile Manager

- review job scheduling and completion to ensure not causing unecessary sstalls or idle time.

a. The Update method is doing a lot of work every frame, including completing jobs and processing projectile requests. Consider spreading this work across multiple frames or using coroutines.

b. The RegisterProjectile and UnregisterProjectile methods are resizing native arrays frequently, which can be expensive. Consider pre-allocating larger arrays or using a more efficient data structure.

c. The UpdateProjectilesAfterJob method is iterating through all projectiles every frame. Consider optimizing this to only update active projectiles.

d) The ProcessProjectileRequests method is creating and registering new projectiles every frame. This could lead to frequent transform changes and particle system updates.

ProjectileEffectManager

a. The PlayDeathEffect method is creating new particle systems if the pool is empty. This could lead to frequent instantiation during gameplay. Consider increasing the initial pool size or adding a warning when the pool is close to empty.

b. The ReturnEffectToPoolAfterFinished coroutine is checking if the effect is alive every frame. Consider using a less frequent check or a callback system.

ProjectileSpawning

This script manages projectile spawning. The ProcessShootProjectile method is setting up projectiles every time they're spawned. Consider pre-configuring projectiles in the pool to reduce setup time during gameplay.

a) The ProcessShootProjectile method is creating new particle effects for each projectile. Consider pooling these effects to reduce instantiation and destruction overhead.

b) The ShootStaticEnemyProjectile method is using a coroutine to spawn projectiles over time. This could be optimized to reduce the frequency of spawns or to batch spawn operations.

ProjectileStateBased

a) The SetupProjectile method is setting up new projectiles frequently. Consider batching these operations or using object pooling more effectively.

b) The Death method is creating death effects for each projectile. This could be optimized by pooling these effects.

PROGRESS

Need to revisitng the spiral mobius strip cayuse it got too many problems. 

Using other strucutre instead, working fine

Fixed debug go to next scene issue

Addressing weird load times going into infinite snake. it’s a deformable issue, need to optimzie how that’s handled with scene start / generation if i want to use it. may just find an alternative approach

Fix Mobius Strip 7 

Error 

[Degenerate Geometry A* Error](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/Degenerate%20Geometry%20A%20Error%2011cffc7f811f80e7839cd5d12172382f.md)

If i play any level long enough, this becomes a problem

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image.png)

Figure out whats up! Something to do with enemies

Also an issue in places, second half of  Scene 3

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image%201.png)

Possible optimization for explode enemy ai as well

![image.png](Oct%209th%2011bffc7f811f802d921fff25e5ff9a8b/image%202.png)