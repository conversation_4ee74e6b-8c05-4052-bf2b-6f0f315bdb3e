using System;
using System.Collections;
using System.Collections.Generic;
using FMODUnity;
using Stylo.Cadance;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.SceneManagement;
using BTR;
using Microsoft.Extensions.Logging; // For ILogger

namespace BTR
{
    public class CrosshairCore : MonoBehaviour
    {
        // ZLogger for performance-optimized logging
        private static readonly Microsoft.Extensions.Logging.ILogger _logger = BTR.Logging.LoggerProvider.CreateLogger<CrosshairCore>();

        [SerializeField]
        private bool enableDebugLogs = true;
        public static CrosshairCore Instance { get; private set; }

        #region Core References
        [Header("Core References")]
        [SerializeField]
        private GameObject Player;

        [SerializeField]
        private GameObject LineToTarget;

        [SerializeField]
        public GameObject RaySpawn;

        [SerializeField]
        public GameObject RaySpawnEnemyLocking;

        [SerializeField]
        public GameObject Reticle;

        [SerializeField]
        public GameObject BonusDamage;

        private PlayerShooting playerShooting;
        private PlayerTimeControl playerTimeControl;
        private PlayerLocking playerLocking;
        #endregion

        #region Input and Time
        private DefaultControls playerInputActions;
        public float lastProjectileLaunchTime;
        public const float QTE_TRIGGER_WINDOW = 1f;
        #endregion

        #region State Variables
        public bool isQuickTap;
        public RaycastHit hitEnemy;
        #endregion

        #region Events
        public event Action<float> OnRewindStart;
        public event Action OnRewindEnd;
        #endregion

        #region Components
        [HideInInspector]
        public StudioEventEmitter musicPlayback;  // Keep for backwards compatibility
        private AudioManager audioManager;
        #endregion

        #region Cadance Events
        [Header("Cadance Events")]
        [EventID]
        public string eventIDShooting = "8th"; // 8th note events for shooting timing

        [EventID]
        public string eventIDRewindTime = "1 Bar"; // Measure events for time rewind
        #endregion

        [SerializeField]
        private LayerMask groundMask;
        public bool showRaycastGizmo = true;
        public bool debugRays = false;
        private int debugFrameInterval = 30;
        private int frameCount = 0;
        private float tapStartTime;
        private const float tapThreshold = 0.1f;

        [Header("Debug Shooting Mode")]
        [SerializeField] private bool enableDirectShootingMode = true;
        [Tooltip("When enabled, shooting happens immediately on button release without waiting for musical timing")]
        [SerializeField] private bool bypassMusicalTiming = true;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            playerInputActions = new DefaultControls();
            playerInputActions.Player.Enable();
            SceneManager.sceneLoaded += GetComponent<PlayerLocking>().OnSceneLoaded;

            playerLocking = GetComponent<PlayerLocking>();
            playerShooting = GetComponent<PlayerShooting>();
            playerTimeControl = GetComponent<PlayerTimeControl>();

            if (playerShooting == null || playerTimeControl == null || playerLocking == null)
            {
                _logger?.LogError("Required components not found on the same GameObject.");
            }
        }

        private void OnEnable()
        {
            RegisterCadanceEvents();
        }

        private void OnDisable()
        {
            UnregisterCadanceEvents();
        }

        private void OnDestroy()
        {
            SceneManager.sceneLoaded -= GetComponent<PlayerLocking>().OnSceneLoaded;
        }

        private void Start()
        {
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            audioManager = AudioManager.Instance;
            if (audioManager == null)
            {
                _logger?.LogError("AudioManager instance not found in the scene.");
            }

            GameObject musicGameObject = GameObject.Find("FMOD Music");
            if (musicGameObject != null)
            {
                musicPlayback = musicGameObject.GetComponent<StudioEventEmitter>();
                if (musicPlayback == null)
                {
                    _logger?.LogError("StudioEventEmitter component not found on 'FMOD Music' GameObject.");
                }
            }
            else
            {
                _logger?.LogError("GameObject with name 'FMOD Music' not found in the scene.");
            }
        }

        private void Update()
        {
            HandleInput();
            UpdatePlayerComponents();
        }

        private void InitializeSingleton()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeInputActions()
        {
            playerInputActions = new DefaultControls();
            playerInputActions.Player.Enable();
            SceneManager.sceneLoaded += GetComponent<PlayerLocking>().OnSceneLoaded;
        }

        private void UpdatePlayerComponents()
        {
            if (debugRays && frameCount++ % debugFrameInterval == 0)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    _logger?.LogDebug("Drawing ray from {RaySpawnPosition} in direction {RaySpawnDirection}",
                        RaySpawn.transform.position, RaySpawn.transform.forward);
                }
#endif
            }
            playerLocking.OnLock();
            playerLocking.CheckEnemyLock(); // Make sure this line is present
            playerLocking.HandleLockFire(); // Handle lock fire state every frame
            _ = playerTimeControl.HandleRewindToBeat();
            _ = playerTimeControl.HandleSlowToBeat();
        }

        private bool wasLockPressed = false;

        private void HandleInput()
        {
            bool isLockPressed = CheckLockProjectiles();

            if (CheckLockProjectilesButtonDown())
                tapStartTime = Time.time;

            // Detect button release: was pressed last frame, not pressed this frame
            if (wasLockPressed && !isLockPressed)
            {
                Debug.Log($"[CrosshairCore] 🔄 Lock button RELEASED!");
                
                // Set triggeredLockFire when lock button is released and we have locked projectiles
                if (playerLocking.GetLockedProjectileCount() > 0)
                {
                    playerLocking.triggeredLockFire = true;
                    Debug.Log($"[CrosshairCore] ✅ triggeredLockFire set to TRUE - Locked Count: {playerLocking.GetLockedProjectileCount()}");
                    Debug.Log($"[CrosshairCore] 🎯 Now waiting for Cadance musical event to fire projectiles...");
                    
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    if (enableDebugLogs)
                    {
                        _logger?.LogDebug("Lock button released - triggeredLockFire set to true, Locked Count: {LockedCount}",
                            playerLocking.GetLockedProjectileCount());
                    }
#endif

                    // PHASE 1 FIX: Direct shooting mode for testing/debugging
                    if (enableDirectShootingMode || bypassMusicalTiming)
                    {
                        Debug.Log($"[CrosshairCore] 🎯 DIRECT SHOOTING MODE TRIGGERED!");
                        
                        // CHECK PROJECTILE SYSTEM BEFORE SHOOTING
                        Debug.Log($"[CrosshairCore] 🚀 PROJECTILE SYSTEM CHECK:");
                        Debug.Log($"[CrosshairCore] - ProjectileSpawner.Instance: {(ProjectileSpawner.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
                        Debug.Log($"[CrosshairCore] - ProjectileManager.Instance: {(ProjectileManager.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
                        Debug.Log($"[CrosshairCore] - ProjectilePool.Instance: {(ProjectilePool.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
                        
                        if (ProjectileSpawner.Instance != null)
                        {
                            Debug.Log($"[CrosshairCore] - ProjectileSpawner.IsFullyInitialized: {(ProjectileSpawner.Instance.IsFullyInitialized ? "✅ YES" : "❌ NO")}");
                        }
                        
                        if (ProjectileSpawner.Instance == null)
                        {
                            Debug.LogError($"[CrosshairCore] 🚨 DIRECT SHOOTING FAILED: ProjectileSpawner.Instance is NULL!");
                            return;
                        }
                        
                        if (!ProjectileSpawner.Instance.IsFullyInitialized)
                        {
                            Debug.LogError($"[CrosshairCore] 🚨 DIRECT SHOOTING FAILED: ProjectileSpawner not fully initialized!");
                            return;
                        }
                        
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                        if (enableDebugLogs)
                        {
                            _logger?.LogDebug("Direct shooting mode enabled - launching projectiles immediately");
                        }
#endif
                        Debug.Log($"[CrosshairCore] 🚀 Calling LaunchProjectilesWithDelay()...");
                        StartCoroutine(playerShooting.LaunchProjectilesWithDelay());
                        Debug.Log($"[CrosshairCore] 🚀 Calling HandleShootingEffects()...");
                        playerShooting.HandleShootingEffects();

                        // Reset music parameters
                        if (audioManager != null)
                        {
                            audioManager.SetMusicParameter("Lock State", 0);
                            audioManager.SetMusicParameter("Player_Lock_State", 0);
                        }
                    }
                }

                if (Time.time - tapStartTime <= tapThreshold)
                {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    if (enableDebugLogs)
                    {
                        _logger?.LogDebug("Quick tap detected - this would be a parry");
                    }
#endif
                }
                isQuickTap = false;
            }

            // Update the previous state for next frame
            wasLockPressed = isLockPressed;
        }

        public bool CheckLockProjectilesButtonDown() =>
            playerInputActions.Player.LockProjectiles.triggered;

        public bool CheckLockProjectilesButtonUp()
        {
            return !playerInputActions.Player.LockProjectiles.IsPressed();
        }

        public bool CheckLockProjectiles() =>
            playerInputActions.Player.LockProjectiles.ReadValue<float>() > 0;

        public bool CheckLockEnemies() => playerInputActions.Player.LockEnemies.ReadValue<float>() > 0;

        public bool CheckRewindToBeat() => playerInputActions.Player.RewindTime.ReadValue<float>() > 0;

        public bool CheckSlowToBeat() => playerInputActions.Player.SlowTime.ReadValue<float>() > 0;

        private void RegisterCadanceEvents()
        {
            Debug.Log("[CrosshairCore] Attempting to register Cadance events...");
            
            if (Cadance.Instance != null)
            {
                Debug.Log($"[CrosshairCore] Cadance.Instance found! Registering events...");
                Debug.Log($"[CrosshairCore] Event ID Shooting: '{eventIDShooting}'");
                Debug.Log($"[CrosshairCore] Event ID Rewind Time: '{eventIDRewindTime}'");
                
                try
                {
                    Cadance.Instance.RegisterForEvents(eventIDShooting, OnMusicalShoot);
                    Debug.Log($"[CrosshairCore] ✅ Successfully registered OnMusicalShoot for '{eventIDShooting}'");
                    
                    Cadance.Instance.RegisterForEvents(eventIDShooting, OnMusicalLock);
                    Debug.Log($"[CrosshairCore] ✅ Successfully registered OnMusicalLock for '{eventIDShooting}'");
                    
                    Cadance.Instance.RegisterForEvents(eventIDRewindTime, UpdateTime);
                    Debug.Log($"[CrosshairCore] ✅ Successfully registered UpdateTime for '{eventIDRewindTime}'");
                    
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    if (enableDebugLogs)
                    {
                        _logger?.LogDebug("Events registered successfully");
                    }
#endif
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CrosshairCore] ❌ Failed to register events: {e.Message}");
                    Debug.LogError($"[CrosshairCore] Stack trace: {e.StackTrace}");
                }
            }
            else
            {
                Debug.LogError("[CrosshairCore] ❌ CADANCE INSTANCE IS NULL! This is why shooting events won't fire.");
                Debug.LogError("[CrosshairCore] Check if Cadance system is properly initialized in the scene.");
                _logger?.LogError("Failed to register events: Cadance instance is null");
            }
        }

        private void UnregisterCadanceEvents()
        {
            if (Cadance.Instance != null)
            {
                Cadance.Instance.UnregisterForEvents(eventIDShooting, OnMusicalShoot);
                Cadance.Instance.UnregisterForEvents(eventIDShooting, OnMusicalLock);
                Cadance.Instance.UnregisterForEvents(eventIDRewindTime, UpdateTime);
            }
        }

        private void OnMusicalShoot(CadanceEvent evt)
        {
            PlayerShooting playerShooting = GetComponent<PlayerShooting>();
            PlayerLocking playerLocking = GetComponent<PlayerLocking>();

            bool lockPressed = CheckLockProjectiles();
            bool triggeredFire = playerLocking.triggeredLockFire;
            int lockedCount = playerLocking.GetLockedProjectileCount();

            // ENHANCED DEBUG LOGGING
            Debug.Log($"[CrosshairCore] 🎵 OnMusicalShoot EVENT FIRED!");
            Debug.Log($"[CrosshairCore] - Lock Pressed: {lockPressed}");
            Debug.Log($"[CrosshairCore] - Triggered Fire: {triggeredFire}");
            Debug.Log($"[CrosshairCore] - Locked Count: {lockedCount}");
            Debug.Log($"[CrosshairCore] - Time Scale: {Time.timeScale}");
            Debug.Log($"[CrosshairCore] - Direct Mode: {enableDirectShootingMode}");
            Debug.Log($"[CrosshairCore] - Bypass Musical: {bypassMusicalTiming}");
            
            // CHECK PROJECTILE SYSTEM AVAILABILITY
            Debug.Log($"[CrosshairCore] 🚀 PROJECTILE SYSTEM STATUS:");
            Debug.Log($"[CrosshairCore] - ProjectileSpawner.Instance: {(ProjectileSpawner.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
            Debug.Log($"[CrosshairCore] - ProjectileManager.Instance: {(ProjectileManager.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
            Debug.Log($"[CrosshairCore] - ProjectilePool.Instance: {(ProjectilePool.Instance != null ? "✅ AVAILABLE" : "❌ NULL")}");
            
            if (ProjectileSpawner.Instance != null)
            {
                Debug.Log($"[CrosshairCore] - ProjectileSpawner.IsFullyInitialized: {(ProjectileSpawner.Instance.IsFullyInitialized ? "✅ YES" : "❌ NO")}");
            }
            else
            {
                Debug.LogError($"[CrosshairCore] 🚨 CRITICAL: ProjectileSpawner.Instance is NULL! This is why shooting fails!");
            }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                _logger?.LogDebug("OnMusicalShoot EVENT FIRED - LockPressed: {LockPressed}, TriggeredFire: {TriggeredFire}, LockedCount: {LockedCount}, TimeScale: {TimeScale}",
                    lockPressed, triggeredFire, lockedCount, Time.timeScale);
            }
#endif

            // Skip musical shooting if direct mode is enabled
            if (enableDirectShootingMode || bypassMusicalTiming)
            {
                Debug.Log("[CrosshairCore] ⏭️ Skipping musical shooting - direct mode enabled");
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    _logger?.LogDebug("OnMusicalShoot - Skipping musical shooting due to direct mode enabled");
                }
#endif
                return;
            }

            // Check shooting conditions
            bool conditionsMet = (!lockPressed || triggeredFire) && lockedCount > 0 && Time.timeScale != 0f;
            Debug.Log($"[CrosshairCore] - Conditions Met: {conditionsMet}");
            
            if (conditionsMet)
            {
                Debug.Log("[CrosshairCore] 🚀 CONDITIONS MET - LAUNCHING PROJECTILES!");
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    _logger?.LogDebug("OnMusicalShoot - CONDITIONS MET - Launching projectiles!");
                }
#endif

                StartCoroutine(playerShooting.LaunchProjectilesWithDelay());
                playerShooting.HandleShootingEffects();

                // Use AudioManager to reset parameters
                if (audioManager != null)
                {
                    audioManager.SetMusicParameter("Lock State", 0);
                    audioManager.SetMusicParameter("Player_Lock_State", 0);
                }
            }
            else
            {
                Debug.Log("[CrosshairCore] ❌ CONDITIONS NOT MET - No shooting will occur");
                Debug.Log($"[CrosshairCore] - Condition breakdown:");
                Debug.Log($"[CrosshairCore]   - (!lockPressed || triggeredFire): {(!lockPressed || triggeredFire)}");
                Debug.Log($"[CrosshairCore]   - lockedCount > 0: {lockedCount > 0}");
                Debug.Log($"[CrosshairCore]   - Time.timeScale != 0: {Time.timeScale != 0f}");
                
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    _logger?.LogDebug("OnMusicalShoot - CONDITIONS NOT MET - No shooting. LockPressed: {LockPressed}, TriggeredFire: {TriggeredFire}, LockedCount: {LockedCount}, TimeScale: {TimeScale}",
                        lockPressed, triggeredFire, lockedCount, Time.timeScale);
                }
#endif
            }
        }

        private void OnMusicalLock(CadanceEvent cadanceEvent)
        {
            // Always try to lock if the button is held, regardless of musical timing
            if (CheckLockProjectiles() && playerLocking.GetLockedProjectileCount() < playerLocking.maxTargets && Time.timeScale != 0f)
            {
                playerLocking.OnLock();
            }
        }

        private void UpdateTime(CadanceEvent evt)
        {
            _ = GetComponent<PlayerTimeControl>().HandleRewindToBeat();
            GetComponent<PlayerLocking>().HandleLockFire();
        }

        public void TriggerRewindStart(float timeScale)
        {
            OnRewindStart?.Invoke(timeScale);
        }

        public void TriggerRewindEnd()
        {
            OnRewindEnd?.Invoke();
        }
    }
}
