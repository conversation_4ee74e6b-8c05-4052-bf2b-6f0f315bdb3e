%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7c8c4c4c4c4c4c4c4c4c4c4c4c4c4c4c, type: 3}
  m_Name: CadanceRuntimeConfig
  enableDebugLogging: 1
  persistAcrossSceneLoads: 1
  eventMappings:
  - eventID: Beat
    description: Musical beat events for shooting
    eventColor: {r: 0, g: 1, b: 1, a: 1}
  - eventID: Measure
    description: Musical measure events
    eventColor: {r: 1, g: 0.5, b: 0, a: 1}
  - eventID: Note
    description: Musical note events
    eventColor: {r: 1, g: 1, b: 0, a: 1}
  - eventID: Gameplay
    description: Generic gameplay events
    eventColor: {r: 0, g: 1, b: 0, a: 1}
  - eventID: Shooting
    description: Player shooting timing events
    eventColor: {r: 1, g: 0, b: 0, a: 1}
  defaultSampleRate: 44100
  autoDetectFMODEvents: 1
  maxEventsPerFrame: 100
  useBatchProcessing: 1
  eventBufferSize: 2
