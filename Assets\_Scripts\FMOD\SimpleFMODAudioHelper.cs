using UnityEngine;
using FMODUnity;
using FMOD.Studio;

namespace BTR.Audio
{
    /// <summary>
    /// Simple FMOD Audio Helper - Replaces over-engineered audio systems.
    /// Uses FMOD's native features instead of reimplementing them.
    /// </summary>
    public static class SimpleFMODAudioHelper
    {
        /// <summary>
        /// Play an FMOD event at a specific position.
        /// FMOD handles 3D attenuation, memory management, and performance optimization automatically.
        /// </summary>
        public static EventInstance PlayEvent(EventReference eventRef, Vector3 position)
        {
            if (!eventRef.IsNull)
            {
                var instance = RuntimeManager.CreateInstance(eventRef);
                instance.set3DAttributes(RuntimeUtils.To3DAttributes(position));
                instance.start();
                return instance;
            }
            return default;
        }

        /// <summary>
        /// Play an FMOD event without 3D positioning (2D audio).
        /// </summary>
        public static EventInstance PlayEvent(EventReference eventRef)
        {
            if (!eventRef.IsNull)
            {
                var instance = RuntimeManager.CreateInstance(eventRef);
                instance.start();
                return instance;
            }
            return default;
        }

        /// <summary>
        /// Get FMOD memory usage (simple monitoring).
        /// Use FMOD Profiler for detailed analysis instead of custom monitoring systems.
        /// </summary>
        public static void GetMemoryUsage(out int currentMB, out int maxMB)
        {
            int current, max;
            if (FMOD.Memory.GetStats(out current, out max) == FMOD.RESULT.OK)
            {
                currentMB = current / (1024 * 1024);
                maxMB = max / (1024 * 1024);
            }
            else
            {
                currentMB = 0;
                maxMB = 0;
            }
        }

        /// <summary>
        /// Get basic FMOD performance info.
        /// For detailed profiling, use FMOD Studio Profiler instead.
        /// </summary>
        public static void GetPerformanceInfo(out float dspUsage, out float studioUsage)
        {
            // FMOD C# API: getCPUUsage uses FMOD.CPU_USAGE struct
            FMOD.CPU_USAGE cpuUsage;
            RuntimeManager.CoreSystem.getCPUUsage(out cpuUsage);
            dspUsage = cpuUsage.dsp;
            studioUsage = cpuUsage.stream;
        }

        /// <summary>
        /// Simple debug info - use this instead of complex monitoring systems.
        /// </summary>
        public static string GetDebugInfo()
        {
            GetMemoryUsage(out int currentMB, out int maxMB);
            GetPerformanceInfo(out float dspUsage, out float studioUsage);
            
            return $"FMOD - Memory: {currentMB}MB/{maxMB}MB, CPU: DSP {dspUsage:F1}% Studio {studioUsage:F1}%";
        }
    }
}
