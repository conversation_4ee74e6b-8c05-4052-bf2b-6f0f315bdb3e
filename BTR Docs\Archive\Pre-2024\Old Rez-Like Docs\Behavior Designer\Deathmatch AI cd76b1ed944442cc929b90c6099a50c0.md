# Deathmatch AI

Repeat Forever loop for Character behavior - this is after any initial wait time and setup

Repeat → Selector (no aborts) → 

Avoid Grenades as top priority

Determine if agent should flee or switch targets if attacked

Then large Sequence

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Behavior%20Designer/Deathmatch%20AI/Untitled.png)

In The Attack Behavior from Deathmatch AI

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Behavior%20Designer/Deathmatch%20AI/Untitled%201.png)