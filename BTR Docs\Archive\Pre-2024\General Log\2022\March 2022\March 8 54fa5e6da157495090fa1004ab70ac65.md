# March 8

Need to recap issues from last week! But first

How to stop projectile audio? Not disappearing 

trying this with projectile movement sound NOT tied to koreographer- will be out of sync with particles this way though

Pulsing sound - hard not to be annoying !!

Changing Cubes that trigger sound to gates - visually - looks better

Added rewind sound to action - 50/50 on whether its needed!

Enemy Fire sound is not great - would like a better one! Trying a replacement

Reduced enemy fire rate to see if i can get things to sit better

IDEA: Interesting pattern in midi Koreo + low enemy fire rate = patterns?

TRY THIS - for hi hat bullets?

IDEA: Representing the music through background color changes

Koreographer good for this - SPAN for draw out sounds

Adding shooting particle effect from enemies

Added Unlauchable Projectile to the mix

- What does this add to the gameplay?
- Should I be able to shoot these or have to shield from them?

MMFeedbacks not playing - fixing this and adding a Rewind Feedback

Looks like there’s a new MMFeedbacks system - attempting to integrate to fix these issues

Nothing seems to work? Upgrading to latest LTS Unity

UPGRADED - fixed issues from upgrading

ALSO got MMFeedbakcs new version working, trying out some cool new things

Attempt to fix enemy lock on aiming!

A* broke for some reason? Reverting to old unity, hopefully dont break anything else ☹️

FIXED Enemy Plane was out of wack and causing the issue

Con<PERSON> aiming works!!!!

MM Feedbakcs work!!!

Need to tighten up Cone aiming and possibly convert Projectile collection this way too?

Looking at options for changing out radar

Pro Radar Build - dig into this tomorrow! Also look over all March assessments to date