# Stylo.Epoch - Time Manipulation Framework

*Created: 2025-07-18*
*Status: Current - Core System*
*System Location: `Assets/Stylo/Epoch/`*

## Overview

**Stylo.Epoch** is a sophisticated time manipulation framework designed as a modern replacement for the Chronos system in Unity. It provides **hierarchical time control**, **recording/rewind functionality**, and **seamless integration** with Unity components and audio systems (FMOD). The architecture emphasizes performance, DOTS compatibility, and extensibility while maintaining compatibility with legacy Chronos APIs.

## System Architecture

```mermaid
flowchart TB
    %% Core System
    ETK[EpochTimekeeper<br/>Singleton Manager]
    ETS[EpochTimeSystem<br/>Core Engine]
    CS[ClockState<br/>DOTS Data]
    
    %% Clock Types
    EGC[EpochGlobalClock<br/>Named Clocks]
    ELC[EpochLocalClock<br/>Per-GameObject]
    EAC[EpochAreaClock<br/>Spatial Effects]
    
    %% Timeline System
    ETL[EpochTimeline<br/>GameObject Integration]
    ER[EpochRecorder<T><br/>Recording System]
    EO[EpochOccurrences<br/>Event Scheduling]
    
    %% Integration
    FEI[FMODEpochIntegration<br/>Audio Integration]
    ECH[EpochCadanceHandler<br/>Music Sync]
    CL[Chronos Legacy<br/>API Compatibility]
    
    %% Data Flow
    ETK --> ETS
    ETS --> CS
    CS --> |Time Data| EGC
    CS --> |Time Data| ELC
    CS --> |Time Data| EAC
    
    %% Timeline Integration
    ETL --> EGC
    ETL --> ELC
    ETL --> ER
    ETL --> EO
    
    %% External Integration
    FEI --> ETL
    ECH --> ETL
    CL --> ETK
    
    %% Hierarchical Relationships
    EGC --> |Parent| EGC
    ELC --> |Parent| EGC
    EAC --> |Affects| ELC
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef clocks fill:#bbf,stroke:#333,stroke-width:2px
    classDef timeline fill:#bfb,stroke:#333,stroke-width:2px
    classDef integration fill:#fbb,stroke:#333,stroke-width:2px
    
    class ETK,ETS,CS core
    class EGC,ELC,EAC clocks
    class ETL,ER,EO timeline
    class FEI,ECH,CL integration
```

## Core Components

### **EpochTimekeeper** (Singleton Manager)
- **Role**: Central hub for all time management operations
- **Pattern**: Singleton with scene persistence
- **Location**: `Stylo/Epoch/EpochTimekeeper.cs`

**Key Features**:
- Manages the EpochTimeSystem instance and provides global access
- Implements performance-optimized clock caching system
- Maintains Chronos API compatibility for smooth migration
- Handles scene persistence via DontDestroyOnLoad
- Provides convenient access methods for all clock types

### **EpochTimeSystem** (Core Engine)
- **Role**: Centralized time computation using DOTS-compatible data structures
- **Pattern**: DOTS-compatible system with NativeCollections
- **Location**: `Stylo/Epoch/EpochTimeSystem.cs`

**Key Features**:
- Manages NativeList<ClockState> for Burst performance
- Handles clock registration, unregistration, and lifecycle
- Implements hierarchical time scale computation
- Provides efficient clock lookup via ID and string key mapping
- DOTS-ready architecture for future Job System integration

### **ClockState** (DOTS Data Structure)
- **Role**: Core data structure for time information
- **Pattern**: Burst-compatible struct with minimal overhead
- **Location**: `Stylo/Epoch/ClockState.cs`

```csharp
public struct ClockState
{
    public int ClockID;                 // Unique identifier
    public FixedString64Bytes Key;      // Optional string key for global clocks
    public float LocalTimeScale;        // User-defined time scale
    public float ComputedTimeScale;     // Final scale after hierarchy
    public float DeltaTime;             // Scaled delta time
    public double CurrentTime;          // Accumulated scaled time
    public int ParentClockID;           // Hierarchy support (-1 for root)
    public bool IsPaused;              // Pause state
}
```

## Clock Hierarchy System

### **EpochGlobalClock** (Named Clocks)
- **Role**: Game-wide time control with named access
- **Pattern**: Named singleton pattern with hierarchy support
- **Location**: `Stylo/Epoch/EpochGlobalClock.cs`

**Key Features**:
- Named clocks accessible throughout the application
- Support hierarchical parenting to other global clocks
- Cached for performance via EpochTimekeeper
- Used for game-wide time effects (bullet time, slow motion, etc.)

### **EpochLocalClock** (Per-GameObject)
- **Role**: Object-specific time control
- **Pattern**: Component-based with automatic registration
- **Location**: `Stylo/Epoch/EpochLocalClock.cs`

**Key Features**:
- Per-GameObject time control with optional hierarchy
- Can parent to global clocks for inheritance
- Primarily used for object-specific time effects
- Automatic registration with parent resolution

### **EpochAreaClock** (Spatial Effects)
- **Role**: Spatial time manipulation using collider triggers
- **Pattern**: Trigger-based spatial effects system
- **Location**: `Stylo/Epoch/EpochAreaClock.cs`

**Key Features**:
- Spatial time manipulation using collider triggers
- Supports multiple blending modes (Instant, PointToEdge, DistanceFromEntry)
- Implements ClockBlend enum (Multiplicative/Additive)
- Tag-based filtering for selective object influence

### **Hierarchy Computation**
The system calculates final time scales through a two-pass update:
1. **Parent Resolution**: Computes ComputedTimeScale = LocalTimeScale × ParentTimeScale
2. **Time Advancement**: Applies scaled delta time to CurrentTime accumulation

## Timeline System

### **EpochTimeline** (Core Component)
- **Role**: Central component for GameObject time experience
- **Pattern**: Component-based time integration with auto-resolution
- **Location**: `Stylo/Epoch/EpochTimeline.cs`

**Key Features**:
- Central component for GameObject time experience
- Manages automatic clock resolution with predictable priority:
  1. Source clock override (highest priority)
  2. Local clock on same GameObject
  3. Specified global clock by key
  4. Default global clock ("Global")
  5. Unity time (fallback)

### **Component Integration**
The system automatically scales Unity components:
```csharp
// Automatic component scaling
Animator animator;        // Adjusts speed property
ParticleSystem particles; // Modifies simulationSpeed
AudioSource audioSource;  // Controls pitch for time effects
Rigidbody rigidbody;     // Physics-aware velocity scaling
```

### **Event System (Occurrences)**
```csharp
// Schedule events on timeline
timeline.Plan(2.0f, () => Debug.Log("Delayed action"));
timeline.Schedule(10.0f, true, forwardAction, backwardAction);

// Event with rewind support
timeline.ScheduleRewindable(5.0f, 
    forward: () => SpawnEnemy(),
    backward: () => DespawnEnemy()
);
```

## Recording and Rewind System

### **Recorder Architecture**
- **EpochRecorder<TSnapshot>**: Generic base class for component recording
- **EpochTransformRecorder**: Records position, rotation, scale
- **EpochRigidbodyRecorder**: Captures physics state
- **EpochAnimatorRecorder**: Records animation state

### **Rewind Implementation**
```csharp
// Configure rewind capability
timeline.rewindable = true;
timeline.recordingDuration = 30f; // 30 seconds of rewind data

// Start rewind mode
timeline.StartRewind();

// Rewind to specific time
timeline.RewindTo(timeline.time - 5f); // Rewind 5 seconds

// Stop rewind and resume normal time
timeline.StopRewind();
```

### **Memory Management**
- Configurable snapshot limits (default: 300 snapshots ≈ 5 seconds at 60fps)
- Automatic cleanup of invalid snapshots
- Memory usage estimation tools for optimization
- Ring buffer approach for efficient storage

## FMOD Audio Integration

### **FMODEpochIntegration** (Audio Time Scaling)
- **Role**: Sophisticated audio time scaling with selective control
- **Pattern**: Component-based audio integration with category management
- **Location**: `Stylo/Epoch/FMODEpochIntegration.cs`

**Registration Modes**:
- **Manual**: Only explicitly registered instances are time-scaled
- **Automatic**: All instances automatically receive time scaling
- **CategoryBased**: Time scaling based on audio category defaults

**Audio Categories**:
```csharp
public enum AudioCategory
{
    Combat,      // Usually time-scaled
    Environment, // Usually time-scaled  
    Music,       // Usually time-independent
    UI,          // Usually time-independent
    Voice        // Usually time-independent
}
```

### **Dynamic Audio Control**
```csharp
// Register FMOD instance with selective scaling
fmodIntegration.RegisterInstance(eventInstance, "gunshot", true, AudioCategory.Combat);

// Toggle time scaling at runtime
fmodIntegration.SetInstanceTimeScaling(eventInstance, false);

// Configure integration behavior
fmodIntegration.ConfigureIntegration(
    enablePitchScaling: true,
    enableLogging: false,
    updateInterval: 0.1f
);
```

## Performance Optimizations

### **Caching Systems**
- **Clock Component Cache**: Fast O(1) lookup for clock components by ID/key
- **Area Clock Tracking**: Efficient collision-based area clock management
- **Snapshot Management**: Ring buffer approach for rewind data
- **Component Cache**: Cached references to Unity components for scaling

### **DOTS Compatibility**
- Burst-compatible ClockState struct
- NativeList usage for memory efficiency
- Job system ready for future parallelization
- Zero-allocation time computation paths

### **Update Optimization**
- Single-pass time computation for all clocks
- Minimal per-frame allocation
- Lazy initialization patterns
- Efficient hierarchy traversal

## Integration Points

### **Chronos Migration Support**
The system provides full API compatibility:
```csharp
// Legacy Chronos code still works
Timekeeper.Instance.Clock("Global").timeScale = 0.5f;

// Modern Epoch equivalent
EpochTimekeeper.Instance.GetGlobalClockComponent("Global").LocalTimeScale = 0.5f;
```

### **Game System Integration**
- **Cadance Integration**: Beat-synchronized time effects via EpochCadanceHandler
- **Component Timeline System**: Automatic Unity component scaling
- **Event Timeline**: Time-aware event scheduling and execution
- **Physics Integration**: Rigidbody velocity scaling with physics awareness

### **Third-Party Compatibility**
- **FMOD Studio**: Complete integration with selective time scaling
- **Cinemachine**: Camera time effects integration
- **Timeline**: Unity Timeline system compatibility

## Configuration and Usage

### **Basic Setup**
```csharp
// Add timeline to GameObject
EpochTimeline timeline = gameObject.AddComponent<EpochTimeline>();

// Configure clock source
timeline.globalClockKey = "PlayerTime";
timeline.rewindable = true;
timeline.recordingDuration = 60f;

// Enable FMOD integration
FMODEpochIntegration fmodIntegration = gameObject.AddComponent<FMODEpochIntegration>();
fmodIntegration.registrationMode = RegistrationMode.Automatic;
```

### **Common Time Effects**
```csharp
// Bullet time effect
EpochGlobalClock bulletTime = EpochTimekeeper.Instance.GetGlobalClockComponent("BulletTime");
bulletTime.LerpTimeScale(0.1f, 1.0f);

// Area-based slow motion
EpochAreaClock areaEffect = GetComponent<EpochAreaClock>();
areaEffect.localTimeScale = 0.3f;
areaEffect.blendMode = ClockBlend.Multiplicative;

// Object-specific speed boost
EpochLocalClock localClock = GetComponent<EpochLocalClock>();
localClock.LocalTimeScale = 2.0f;
```

### **Rewind Mechanics**
```csharp
// Player rewind ability
if (Input.GetKeyDown(KeyCode.R) && timeline.CanRewind)
{
    timeline.StartRewind();
    timeline.RewindTo(timeline.time - rewindDuration);
}

// Stop rewind
if (Input.GetKeyUp(KeyCode.R))
{
    timeline.StopRewind();
}
```

## Error Handling and Debugging

### **Centralized Error Management**
- **EpochErrorHandler**: Consistent error handling across the system
- Categorized error types (ClockResolution, ComponentLifecycle, etc.)
- Severity-based logging (Info, Warning, Error, Critical)
- Graceful degradation patterns

### **Development Tools**
- Clock configuration validation
- Integration statistics monitoring
- Memory usage estimation
- Real-time performance metrics
- Timeline visualization tools

### **Debug Features**
```csharp
// Enable debug logging
EpochTimekeeper.Instance.enableDebugLogging = true;

// Monitor clock performance
EpochTimekeeper.Instance.LogClockStatistics();

// Validate configuration
timeline.ValidateConfiguration();
```

## Best Practices

### **Performance**
- Use global clocks for game-wide effects
- Minimize area clock overlaps for performance
- Configure appropriate recording durations
- Monitor memory usage in rewind systems

### **Architecture**
- Follow hierarchical clock design patterns
- Use appropriate clock types for different scenarios
- Implement proper cleanup for dynamic objects
- Design rewind-aware event systems

### **Integration**
- Register audio events with appropriate categories
- Test time effects with different component types
- Validate clock resolution chains
- Implement fallback behaviors for missing clocks

## Future Enhancements

### **Planned Features**
- **Job System Integration**: Full DOTS parallelization
- **Network Synchronization**: Multiplayer time synchronization
- **Advanced Recording**: Custom snapshot compression
- **Timeline Visualization**: Runtime timeline debugging tools

### **Performance Improvements**
- **Burst Compilation**: Full Burst optimization for time computation
- **Memory Optimization**: Compressed snapshot storage
- **Culling**: Smart component update culling
- **Streaming**: Temporal data streaming for long recordings

## Related Systems

- **[[Stylo.Cadance - Music Synchronization Framework]]** - Music-synchronized time effects
- **[[Advanced FMOD Integration]]** - Audio time scaling implementation
- **[[Player System Architecture]]** - Player time control abilities
- **[[Enemy System Architecture]]** - Enemy time-based behaviors

## Notes

Stylo.Epoch represents a **mature, well-architected time manipulation system** that successfully balances performance, functionality, and ease of use. Its hierarchical design, DOTS compatibility, and comprehensive audio integration make it a robust foundation for time-based gameplay mechanics.

The system's emphasis on migration compatibility and extensibility ensures it can serve as a long-term solution for complex time manipulation requirements in Unity projects. The architecture demonstrates careful consideration of real-world game development needs, from performance optimization to developer workflow.