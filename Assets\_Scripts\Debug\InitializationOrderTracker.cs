using UnityEngine;
using BTR;
using BTR.Projectiles;
using System.Collections;

/// <summary>
/// Tracks the initialization order of all projectile system components
/// to identify timing issues and missing GameObjects.
/// </summary>
public class InitializationOrderTracker : MonoBehaviour
{
    [Header("Tracking Settings")]
    [SerializeField] private bool enableContinuousTracking = true;
    [SerializeField] private float trackingInterval = 0.5f;
    
    [Header("Initialization Status")]
    [SerializeField] private bool projectileManagerAwake = false;
    [SerializeField] private bool projectileSpawnerAwake = false;
    [SerializeField] private bool projectilePoolAwake = false;
    [SerializeField] private bool projectileEffectManagerAwake = false;
    
    [Header("Singleton Status")]
    [SerializeField] private bool projectileManagerInstance = false;
    [SerializeField] private bool projectileSpawnerInstance = false;
    [SerializeField] private bool projectilePoolInstance = false;
    [SerializeField] private bool projectileEffectManagerInstance = false;
    
    [Header("GameObject Detection")]
    [SerializeField] private string[] foundGameObjects = new string[0];
    [SerializeField] private string[] missingGameObjects = new string[0];
    
    private void Awake()
    {
        Debug.Log("🔍 [InitializationOrderTracker] Awake - Starting initialization tracking");
        
        // Start tracking immediately
        StartCoroutine(TrackInitialization());
    }
    
    private IEnumerator TrackInitialization()
    {
        int frameCount = 0;
        
        while (enableContinuousTracking)
        {
            frameCount++;
            
            Debug.Log($"🔍 [InitializationOrderTracker] Frame {frameCount} - Checking initialization status...");
            
            // Check for GameObjects in scene
            CheckGameObjectsInScene();
            
            // Check singleton instances
            CheckSingletonInstances();
            
            // Log current status
            LogCurrentStatus();
            
            // Check if everything is ready
            if (AllSystemsReady())
            {
                Debug.Log("🟢 [InitializationOrderTracker] ALL SYSTEMS READY! Stopping tracking.");
                break;
            }
            
            yield return new WaitForSeconds(trackingInterval);
        }
    }
    
    private void CheckGameObjectsInScene()
    {
        var foundList = new System.Collections.Generic.List<string>();
        var missingList = new System.Collections.Generic.List<string>();
        
        // Check for specific GameObjects
        string[] expectedGameObjects = {
            "ProjectileManager",
            "ProjectileSpawner", 
            "ProjectilePool",
            "ProjectileEffectManager",
            "Player",
            "CrosshairCore"
        };
        
        foreach (string goName in expectedGameObjects)
        {
            GameObject go = GameObject.Find(goName);
            if (go != null)
            {
                foundList.Add($"{goName} (✅)");
                
                // Check for components on found GameObjects
                if (goName == "ProjectileManager" || goName == "ProjectileSpawner" || goName == "ProjectilePool")
                {
                    var components = go.GetComponents<MonoBehaviour>();
                    foreach (var comp in components)
                    {
                        if (comp != null && comp.GetType().Namespace != null && comp.GetType().Namespace.Contains("BTR"))
                        {
                            foundList.Add($"  └─ {comp.GetType().Name} (✅)");
                        }
                    }
                }
            }
            else
            {
                missingList.Add($"{goName} (❌)");
            }
        }
        
        foundGameObjects = foundList.ToArray();
        missingGameObjects = missingList.ToArray();
    }
    
    private void CheckSingletonInstances()
    {
        projectileManagerInstance = ProjectileManager.Instance != null;
        projectileSpawnerInstance = ProjectileSpawner.Instance != null;
        projectilePoolInstance = ProjectilePool.Instance != null;
        projectileEffectManagerInstance = ProjectileEffectManager.Instance != null;
    }
    
    private void LogCurrentStatus()
    {
        Debug.Log($"🔍 [InitializationOrderTracker] CURRENT STATUS:");
        Debug.Log($"   Singletons: PM:{(projectileManagerInstance ? "✅" : "❌")} PS:{(projectileSpawnerInstance ? "✅" : "❌")} PP:{(projectilePoolInstance ? "✅" : "❌")} PE:{(projectileEffectManagerInstance ? "✅" : "❌")}");
        
        if (foundGameObjects.Length > 0)
        {
            Debug.Log($"   Found GameObjects: {string.Join(", ", foundGameObjects)}");
        }
        
        if (missingGameObjects.Length > 0)
        {
            Debug.LogWarning($"   Missing GameObjects: {string.Join(", ", missingGameObjects)}");
        }
        
        // Check ProjectileSpawner initialization if available
        if (ProjectileSpawner.Instance != null)
        {
            bool fullyInitialized = ProjectileSpawner.Instance.IsFullyInitialized;
            Debug.Log($"   ProjectileSpawner.IsFullyInitialized: {(fullyInitialized ? "✅" : "❌")}");
        }
    }
    
    private bool AllSystemsReady()
    {
        return projectileManagerInstance && 
               projectileSpawnerInstance && 
               projectilePoolInstance && 
               projectileEffectManagerInstance &&
               (ProjectileSpawner.Instance?.IsFullyInitialized ?? false);
    }
    
    [ContextMenu("Force Check Now")]
    public void ForceCheckNow()
    {
        CheckGameObjectsInScene();
        CheckSingletonInstances();
        LogCurrentStatus();
    }
    
    [ContextMenu("List All GameObjects")]
    public void ListAllGameObjects()
    {
        Debug.Log("📋 [InitializationOrderTracker] ALL GAMEOBJECTS IN SCENE:");
        
        GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);
        foreach (GameObject obj in allObjects)
        {
            if (obj.transform.parent == null) // Only root objects
            {
                Debug.Log($"  - {obj.name}");
                
                // Check for BTR components
                var components = obj.GetComponents<MonoBehaviour>();
                foreach (var comp in components)
                {
                    if (comp != null && comp.GetType().Namespace != null && 
                        (comp.GetType().Namespace.Contains("BTR") || comp.GetType().Name.Contains("Projectile")))
                    {
                        Debug.Log($"    └─ {comp.GetType().Name}");
                    }
                }
            }
        }
    }
    
    [ContextMenu("Find ProjectileSpawner GameObject")]
    public void FindProjectileSpawnerGameObject()
    {
        Debug.Log("🔍 [InitializationOrderTracker] SEARCHING FOR PROJECTILE SPAWNER...");
        
        // Search by name
        GameObject spawnerGO = GameObject.Find("ProjectileSpawner");
        if (spawnerGO != null)
        {
            Debug.Log($"✅ Found ProjectileSpawner GameObject: {spawnerGO.name}");
            
            var spawnerComp = spawnerGO.GetComponent<ProjectileSpawner>();
            Debug.Log($"   ProjectileSpawner component: {(spawnerComp != null ? "✅ FOUND" : "❌ MISSING")}");
            
            if (spawnerComp != null)
            {
                Debug.Log($"   Is Instance set: {(ProjectileSpawner.Instance == spawnerComp ? "✅ YES" : "❌ NO")}");
                Debug.Log($"   Is Fully Initialized: {(spawnerComp.IsFullyInitialized ? "✅ YES" : "❌ NO")}");
            }
        }
        else
        {
            Debug.LogError("❌ ProjectileSpawner GameObject NOT FOUND!");
        }
        
        // Search by component type
        ProjectileSpawner[] allSpawners = FindObjectsByType<ProjectileSpawner>(FindObjectsSortMode.None);
        Debug.Log($"Found {allSpawners.Length} ProjectileSpawner components in scene:");
        
        for (int i = 0; i < allSpawners.Length; i++)
        {
            var spawner = allSpawners[i];
            Debug.Log($"  [{i}] {spawner.gameObject.name} - IsFullyInitialized: {spawner.IsFullyInitialized}");
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, Screen.height - 300, 400, 290));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔍 INITIALIZATION TRACKER", GUI.skin.label);
        
        GUILayout.Label("Singletons:");
        GUILayout.Label($"ProjectileManager: {(projectileManagerInstance ? "✅" : "❌")}");
        GUILayout.Label($"ProjectileSpawner: {(projectileSpawnerInstance ? "✅" : "❌")}");
        GUILayout.Label($"ProjectilePool: {(projectilePoolInstance ? "✅" : "❌")}");
        GUILayout.Label($"ProjectileEffectManager: {(projectileEffectManagerInstance ? "✅" : "❌")}");
        
        if (ProjectileSpawner.Instance != null)
        {
            GUILayout.Label($"Spawner Initialized: {(ProjectileSpawner.Instance.IsFullyInitialized ? "✅" : "❌")}");
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔍 FORCE CHECK NOW"))
        {
            ForceCheckNow();
        }
        
        if (GUILayout.Button("📋 LIST ALL GAMEOBJECTS"))
        {
            ListAllGameObjects();
        }
        
        if (GUILayout.Button("🔍 FIND PROJECTILE SPAWNER"))
        {
            FindProjectileSpawnerGameObject();
        }
        
        GUILayout.Space(10);
        enableContinuousTracking = GUILayout.Toggle(enableContinuousTracking, "Continuous Tracking");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
