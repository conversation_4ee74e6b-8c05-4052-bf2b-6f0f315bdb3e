# May 10

Change material properties at runtime

[http://gyanendushekhar.com/2018/09/16/change-material-and-its-properties-at-runtime-unity-tutorial/](http://gyanendushekhar.com/2018/09/16/change-material-and-its-properties-at-runtime-unity-tutorial/)

Focus on objects in Scene view

[https://www.youtube.com/watch?v=4sUXxaXlYY8](https://www.youtube.com/watch?v=4sUXxaXlYY8)

Alpha on Outline shader issues

[](https://forum.unity.com/threads/alpha-not-working-on-outline-shader.929733/)

[Particle System Trails | Unity Particle Effects | Visual FX](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)

Pool Manager Docs - Refer for issues with pooling enemies!

[2. Per-Prefab Options - PoolManager4 Docs](http://docs.poolmanager.path-o-logical.com/home/<USER>

[.Despawn() - PoolManager4 Docs](http://docs.poolmanager.path-o-logical.com/code-reference/spawnpool/spawnpool-despawn)

Doing some tutorial videos

[Better Coding in Unity With Just a Few Lines of Code](https://www.youtube.com/watch?v=r-RCfmQqLA0)

Dealing with loads of nested if statements and code clarity

The solution is a statemachine - UGH it’s unavoidable!

This is quite contested in the comments - not always the best approach to make a bunch of classes and a state machine! Maybe I don’t need this - but I do need something better than all the flags in my update method. I could possibly organize those flags better as well

Using Unity UI Builder to make a Pause Menu

[Unity's UI Toolkit & UI Builder: What is It, and How Do You Make It Work ? - Space's Aces Devlog #03](https://www.youtube.com/watch?v=EVdtUPnl3Do)

Seems interesting but from Unity themselves - a conundrum!

> For 2021.2, UI Toolkit is recommended as an alternative to Unity UI for creating screen overlay UI that runs on a wide variety of screen resolutions. It should be considered by those who: **Produce work with a significant amount of user interfaces, Require familiar authoring workflows for artists and designers, Seek textureless UI rendering capabilities**. Otherwise, if you need **UI positioned and lit in a 3D world, as well as VFX with custom shaders and materials**, Unity UI is stil the recommended solution.
> 

Going with old method for pause menu

Starting with good ol Brackeys 

[PAUSE MENU in Unity](https://www.youtube.com/watch?v=JivuXdrIHK0)

Interruption!

Major crashing from Visual Studio - going back to an old version 

Back to 2019 version. Did not crash as much previously!

Not sure why Visual studio is crashing so much 

Seems to be working okay now

May want to animate menu later! Mentioned in later half of brakeys video

Integrating with my controller 

[Using Menus with A Controller/Keyboard in Unity](https://www.youtube.com/watch?v=SXBgBmUcTe0)

Some difficulty - need to replace parts with Rewired

There’s documentation for this from Rewired - replacing standard event system

[Rewired Standalone Input Module](https://guavaman.com/projects/rewired/docs/RewiredStandaloneInputModule.html)

Got Rewired working with the pause menu finally!!

Can continue this into shifting to an options menu like shown in the video below

[START MENU in Unity](https://www.youtube.com/watch?v=zc8ac_qUXQY)

Rewired has control remapping example scenes that can be pulled in for the options menu

This is important!

Likely need to define Pause as an action and setup better than I currently have it setup - DID THIS!!!

Need to include a CURRENT CONTROLS pop up for when CONTROLS are choosen 

Should I integrate the radar target tracking UI stuff??? Look into it

Look into Making lock on cube on enemies transparent! Interesting look right now but could probably be better