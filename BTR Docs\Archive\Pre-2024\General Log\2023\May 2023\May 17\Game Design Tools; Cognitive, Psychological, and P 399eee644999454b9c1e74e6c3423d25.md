# Game Design Tools; Cognitive, Psychological, and Practical Approaches Exercises

Here are some exercises, tools, and strategies that you can apply to improve the game you're currently making:

1. **Documentation**: Keep a detailed record of your game design process. This can help you track your progress, identify areas of improvement, and serve as a reference for future projects.
2. **Production**: Understand the production process and how to manage it effectively. This includes planning, scheduling, and coordinating different aspects of game development.
3. **Evaluation**: Regularly evaluate your game design. This can involve playtesting, feedback collection, and analysis to understand what's working and what's not.
4. **Analysis and Marketing Tools**: Use analysis tools to understand player behavior and preferences. Marketing tools can help you understand your target audience and how to reach them effectively.
5. **Task Guide**: The book suggests completing tasks at the end of each design technique to consolidate your knowledge. Practicing these tasks can help you improve your game design skills.
6. **Iterative Process**: If something goes wrong in your game design, use the iterative process to understand what went wrong and why, in order to provide a better experience the next time.
7. **Manipulation**: Manipulate your players to provide them with the best possible experience. This can involve using design tools to direct users into the best possible experience.
8. **Affordance**: Understand the concept of affordance, which is the relationship between the properties of an object and the capabilities of the agent that determine just how the object could possibly be used.
9. **Respecting Players' Time**: As game designers, we are the custodians of the players' time. Every game makes a promise on an emotion, a tone, or an experience in return for borrowing time from our players' lives. As designers, it is our job to deliver on those promises, ensuring that the transaction of a player's time for our experience is fulfilled, fair, and treated with respect.
10. **Experimentation**: Experiment with different elements of your game design. This can lead to a positive dynamic where you accomplish an action or a negative one where you fail to perform the action. Experimentation can help you understand what works and what doesn't in your game design.
11. **Understanding Game Rules**: Understand the rules of your game and how they affect the player's actions. For example, if a rule says “you can’t drive cars in this part of the map”, players won't be able to drive cars in that part of the map. This understanding can help you design better gameplay experiences.
12. **Design Techniques**: The document suggests various design techniques that can be used to define the initial concept of the game or solve specific problems. These techniques are based on psychological manipulation and should be used wisely.
13. **Dynamic Difficulty Adjustment (DDA)**: This tool can be used when the player is struggling in a certain gameplay segment. Designers can make the game easier, for example by lowering NPCs’ difficulty without telling the player, adjusting the challenge to their skill level.
14. **Forgiveness Mechanics**: These mechanics can make it look like the player succeeded, even if they would have lost. For example, if 20 bullets are going to hit the player, but they can only take 19 before dying, one of those bullets, let's say, missed the player.
15. **Guidance Techniques**: These techniques can guide players into following a path or performing a certain action. For example, if there is an item on a desk that only half the players notice, in the next version of the game, the lamp on the desk that casually points toward the object flickers a bit when players get close.
16. M**Random Number Generators (RNG)**: RNG can be used to overcome a challenge that players didn't have a high enough skill for. For example, while playing any of the Souls series, a player might grind until they are statistically stronger, rather than getting better at the game.
17. **Grading Systems**: Designers can communicate to the players their results with a grade. Even the worst grade can sound cool, motivating players to improve.
18. **Progression Systems**: Players can get a promotion, like a badge or a new title, but nothing really changes in the game. If players can't tell the difference between real progress and a fake, awarding them with a title will make them feel better about their skills.
19. **COM-B**: The COM-B is a design tool of behavioural game design, and it divides the process of performing an action into three categories: capability, opportunity, and motivation. Each one has a checklist of questions you need to answer to find the users’ perception of the action and identify possible flaws.

Remember, these are tools and exercises to help you improve your game design skills. How you apply them will depend on the specific needs and context of your game.