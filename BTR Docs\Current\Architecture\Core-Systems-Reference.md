---
systems: [core]
components: [architecture, events, management]
tags: [game-system, reference]
priority: p0
last_touched: 2025-04-09
links:
  - ![[Systems/Enemies/Architecture#^core-integration]]
  - ![[Systems/Player/Architecture#^core-components]]
  - ![[Project Management/2025/March 24th, 2025.md#^core-roadmap]]
---

# Core System
[[Related Systems]] → s Reference

## System Architecture Overview
```mermaid
graph TD
    A[Management System] --> B[Player System]
    A --> C[Enemy System]
    A --> D[Projectile System]
    B <--> D
    C <--> D
    E[Event System] --> A
    E --> B
    E --> C
    E --> D
```

## Key System Interactions
1. **Time Management**
   - Controls bullet-time mechanics (0.2-0.5x timescale)
   - Synchronizes system-wide events via [[FixedTimestepWithBacktracking]]

2. **Event System**
   - Handles 150+ event types
   - Manages state transitions with 98% success rate
   - Integrated with [[ManagementSystem#Debug Tools]]

3. **Object Lifecycle**
   - Three-phase cleanup process (Immediate/Deferred/Final)
   - Pooling system manages 1000+ concurrent objects
   - Automatic leak detection

## Active Systems Status
| System          | Status    | Tier  | Dependencies              | Key Components                          |
|-----------------|-----------|-------|---------------------------|-----------------------------------------|
| Player          | Active    | Core  | Management, Projectile    | Locking System, Combat Interface        |
| Enemy           | Active    | Tier3 | Management, Projectile    | PhasedEnemy, SnakeBoss, StaticShooter   |
| Projectile      | Active    | Core  | Management, Player        | StateMachine, PoolSystem, JobSystem     |
| Event System    | Active    | Core  | All systems               | EventBus, DebugMonitor, TimelineSync    |
| Wave System     | Beta      | Tier2 | Management                | Wave_Transitions, SpawnController       |

## Enemy Type Matrix
| Type            | Status    | Complexity | Special Features                  |
|-----------------|-----------|------------|------------------------------------|
| BasicEnemy      | Active    | Simple     | Core movement/combat              |
| PhasedEnemy     | Active    | Complex    | State-based vulnerability         |
| SnakeBoss       | Active    | Complex    | Segmented body, weak point system |
| StaticShooter   | Active    | Simple     | Fixed-position turret             |
---
systems: [core]
components: [architecture, events, management]
tags: [game-system, reference, audio, visual]
priority: p0
last_touched: 2025-04-09
links:
  - ![[Project Management/2025/March 24th, 2025.md#^core-roadmap]]
  - [[Tech Recommendations/Coding Tips/Advanced Coding Tips For Big Unity Projects.md#^architecture|Architecture Best Practices]]
  - ![[Systems/Enemies/Architecture#^core-integration]]
  - [[Audio Manager#system-hooks]]
---

---
systems: [${1/-/ }]
links:
  - 
---

# Core System
[[Related Systems]] → s Reference

## System Relationships
[[Event Bus System]] → [[All Subsystems]]
[[Audio Manager]] ↔ [[VFX Controller]]

## Key Implementation Details
![[Tech Recommendations/Coding Tips/Learn to Build an Advanced Event Bus Unity Architecture.md#^observer-pattern]]

## Historical Context
See [[2020-2021 Design & Mechanics.md#Foundation|Initial Architecture Planning]]
| TwinSnakeBoss   | Archived  | Complex    | Legacy dual-boss system           |

## Development Principles
1. **Tiered Complexity**
   - Simple: <5 components (e.g. StaticShooter)
   - Standard: 5-15 components (e.g. BasicEnemy)
   - Complex: 15+ components (e.g. PhasedEnemy)

2. **Performance Priorities**
   - Pooling: 90% reuse rate required
   - Job System: Mandatory for >100 entities
   - Distance-based LODs: 50m/100m/200m zones

3. **Hit Detection**
   - 99.8% frame-accurate collision checks
   - Hybrid sphere/capsule colliders
   - 3-phase validation system from [[HitDetectionRecommendations]]

4. **Wave System**
   - Dynamic difficulty scaling
   - 15+ transition types
   - Integrated with [[Wave_Transitions]] patterns

## Active Components
- Event System: 158 defined event types (documented in [[Event_System_Documentation]])
- Fixed Timestep: Backtracking-enabled physics (0.02s fixed step) [code](FixedTimestepWithBacktracking.cs)
- Radar System: 750m detection range
- Locking System: 0.8s acquisition time
- Architecture: [System Logic Diagram](https://www.figma.com/design/eorM58yt9JDEWNIKhbjYOM/BTR-----Chart-System-Logic)
- Class Organization: 12 core categories
- Enemy Architecture: 3-tier component system (Core/Behaviors/Config)
- Cleanup Process: Coordinator pattern with 3-phase sequence
- Management System: Real-time debug tools, 14 monitoring metrics
- Wave Transitions: 5 primary patterns (documented in [[Wave_Transitions]])
- Projectile Handling: 8 collision layers with [[ProjectileHitDetection]] rules
- Enemy AI: 4 behavior profiles from [[EnemyTypes]]

## Archived Components
- TwinSnakeBoss system (migrated to SnakeBossV2)
- Legacy projectile locking architecture
- Initial engagement delay system
- Radial damage falloff model
- Radar visibility threshold system
- Basic enemy archetype implementations
- Static shooter prototypes
- Phased enemy v1 architecture

> **Reference:** [[TrashBin/Deprecated-Systems]] | [[TrashBin/Deprecated-EnemyGuide]]