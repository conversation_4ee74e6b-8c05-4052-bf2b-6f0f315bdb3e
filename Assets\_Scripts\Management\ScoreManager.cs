using UnityEngine;
using Stylo.Epoch;

namespace BTR
{
    public class ScoreManager : MonoBehaviour
    {
        private static ScoreManager instance;
        public static ScoreManager Instance => instance;

        [Header("Game Settings")]
        [SerializeField] private float scoreDecayRate = 1f; // Points lost per second
        [SerializeField] private int initialScore = 2000;
        public int InitialScore => initialScore;
        private const int SCORE_ON_KILL = 10;

        [Header("Wave Management")]
        [SerializeField] private int _totalWaveCount = 0;
        [SerializeField] private int _currentSceneWaveCount = 0;

        [Header("Integration Settings")]
        [SerializeField] private bool useIntegratedScoring = true;

        private GameEvents gameEvents => GameEventsManager.Instance?.Events;
        private PlayerUI playerUI;
        private EpochTimeline timeline;
        private float lastUpdateTime;
        private float accumulatedScoreDecrease = 0f;
        private KillStreakSystem killStreakSystem;

        public int Score { get; private set; }
        public int ShotTally { get; private set; }
        public int RicochetTally { get; private set; }
        public int EnemyKillTally { get; private set; }
        public float TimeAlive { get; private set; }

        private const int SECTION_TRANSITION_SCORE_BOOST = 200;
        private const int SCORE_CHANGE_THRESHOLD = 2;

        public int TotalWaveCount
        {
            get => _totalWaveCount;
            private set
            {
                _totalWaveCount = value;
                OnValueChanged();
            }
        }

        public int CurrentSceneWaveCount
        {
            get => _currentSceneWaveCount;
            set
            {
                _currentSceneWaveCount = value;
                OnValueChanged();
            }
        }

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            timeline = GetComponent<EpochTimeline>();
            if (timeline == null)
            {
                Debug.LogError("EpochTimeline component not found on ScoreManager");
            }
        }

        private void Start()
        {
            // Initialize integrated scoring if enabled
            if (useIntegratedScoring)
            {
                InitializeIntegratedScoring();
            }
            
            playerUI = FindFirstObjectByType<PlayerUI>();
            if (playerUI == null)
            {
                Debug.LogError($"[{GetType().Name}] PlayerUI not found in scene!");
            }

            if (gameEvents == null)
            {
                Debug.LogError($"[{GetType().Name}] GameEvents not found! Make sure GameEventsManager is in the scene with assigned GameEvents asset.");
                return;
            }

            // Initialize kill streak system reference
            if (killStreakSystem == null)
            {
                killStreakSystem = FindFirstObjectByType<KillStreakSystem>();
            }

            SubscribeToEvents();
            ResetScore();
        }

        private void Update()
        {
            if (!GameManager.Instance.IsGamePaused() && !GameManager.Instance.IsGameOver())
            {
                float deltaTime = timeline != null ? timeline.DeltaTime : Time.deltaTime;

                // Update score decay based on time
                accumulatedScoreDecrease += scoreDecayRate * deltaTime;
                int scoreDecrease = Mathf.FloorToInt(accumulatedScoreDecrease);

                if (scoreDecrease > 0)
                {
                    Score = Mathf.Max(0, Score - scoreDecrease);
                    accumulatedScoreDecrease -= scoreDecrease;

                    if (Score <= 0 && !GameManager.Instance.IsGameOver())
                    {
                        gameEvents.TriggerGameOver();
                    }
                    else if (scoreDecrease >= SCORE_CHANGE_THRESHOLD)
                    {
                        gameEvents.TriggerScoreUpdated(Score);
                        gameEvents.TriggerPlayerHealthChanged(Score, initialScore);
                    }
                }
            }
        }

        public void AddScore(int amount)
        {
            int previousScore = Score;
            Score = Mathf.Min(initialScore, Score + amount);

            if (Mathf.Abs(amount) >= SCORE_CHANGE_THRESHOLD)
            {
                gameEvents.TriggerScoreUpdated(Score);
                gameEvents.TriggerPlayerHealthChanged(Score, initialScore);
            }
        }

        public void OnEnemyKilled()
        {
            // If using integrated scoring, let the integration system handle this
            if (useIntegratedScoring)
            {
                // The ScoreManagerIntegration will handle the scoring
                // We just need to notify the kill streak system
                if (killStreakSystem != null)
                {
                    killStreakSystem.OnEnemyKilled();
                }
                return;
            }
            
            // Legacy scoring logic (kept for backward compatibility)
            // Notify kill streak system
            if (killStreakSystem != null)
            {
                killStreakSystem.OnEnemyKilled();
            }
            
            // Calculate score with multiplier
            float multiplier = killStreakSystem != null ? killStreakSystem.GetCurrentMultiplier() : 1f;
            int scoreToAdd = Mathf.RoundToInt(SCORE_ON_KILL * multiplier);
            AddScore(scoreToAdd);
            EnemyKillTally++;
            
            // Trigger appropriate events based on streak status
            if (killStreakSystem != null && killStreakSystem.IsStreakActive())
            {
                gameEvents.TriggerEnemyKilledWithStreak(killStreakSystem.GetCurrentStreak(), multiplier);
            }
        }

        public void ReportDamage(int damage)
        {
            // If using integrated scoring, let the integration system handle this
            if (useIntegratedScoring)
            {
                // The ScoreManagerIntegration will handle the scoring through events
                // We just apply the damage directly here for immediate feedback
                AddScore(-damage);
                return;
            }
            
            // Legacy damage reporting
            AddScore(-damage);
        }

        public void ResetScore()
        {
            Score = initialScore;
            ShotTally = 0;
            RicochetTally = 0;
            EnemyKillTally = 0;
            TimeAlive = 0;
            accumulatedScoreDecrease = 0f;
            gameEvents.TriggerScoreUpdated(Score);
            gameEvents.TriggerPlayerHealthChanged(Score, initialScore);
        }

        private void OnDestroy()
        {
            // Unsubscribe from integrated scoring events
            if (useIntegratedScoring)
            {
                UnsubscribeFromIntegratedScoring();
            }
            
            UnsubscribeFromEvents();
        }

        private void SubscribeToEvents()
        {
            if (gameEvents != null)
            {
                gameEvents.OnGameRestarted += HandleGameRestarted;
                gameEvents.OnGameOver += HandleGameOver;
            }
        }

        private void UnsubscribeFromEvents()
        {
            if (GameEventsManager.Instance != null && gameEvents != null)
            {
                gameEvents.OnGameRestarted -= HandleGameRestarted;
                gameEvents.OnGameOver -= HandleGameOver;
            }
        }

        public void AddShotTally(int shotsToAdd)
        {
            ShotTally += shotsToAdd;
        }

        private void HandleGameRestarted()
        {
            ResetScore();
        }

        private void HandleGameOver()
        {
            // Implementation
        }

        private void OnValueChanged()
        {
            // This method is intentionally left empty or you can add logic here if needed
        }

        public void AddSectionTransitionBoost()
        {
            AddScore(SECTION_TRANSITION_SCORE_BOOST);
        }

        public void waveCounterAdd()
        {
            CurrentSceneWaveCount++;
            TotalWaveCount++;
            Debug.Log($"[{GetType().Name}] Wave added. Current scene wave: {CurrentSceneWaveCount}, Total waves: {TotalWaveCount}");
        }

        public float GetRemainingTime()
        {
            return Score;
        }
        
        #region Integration Methods
        
        private void InitializeIntegratedScoring()
        {
            // Check if ScoreManagerIntegration exists in scene
            var integration = FindFirstObjectByType<ScoreManagerIntegration>();
            
            if (integration != null)
            {
                // Subscribe to integrated scoring events
                ScoreManagerIntegration.OnScoreCalculated += HandleIntegratedScore;
                ScoreManagerIntegration.OnScoreEventProcessed += HandleScoreEventProcessed;
                
                Debug.Log("[ScoreManager] Integrated scoring system connected");
            }
            else
            {
                Debug.LogWarning("[ScoreManager] ScoreManagerIntegration not found - using legacy scoring");
                useIntegratedScoring = false;
            }
        }
        
        private void UnsubscribeFromIntegratedScoring()
        {
            ScoreManagerIntegration.OnScoreCalculated -= HandleIntegratedScore;
            ScoreManagerIntegration.OnScoreEventProcessed -= HandleScoreEventProcessed;
        }
        
        private void HandleIntegratedScore(float calculatedScore)
        {
            // This is called when the integrated system calculates a score
            // The actual score application is handled by the integration system
            // We can use this for UI updates or additional effects
        }
        
        private void HandleScoreEventProcessed(ScoreEventData scoreData)
        {
            // Handle specific score event types for additional effects
            switch (scoreData.eventType)
            {
                case ScoreEventType.EnemyKill:
                    // Update kill tally
                    EnemyKillTally++;
                    
                    // Trigger appropriate events based on streak status
                    if (scoreData.streakCount > 0)
                    {
                        gameEvents.TriggerEnemyKilledWithStreak(scoreData.streakCount, scoreData.multiplier);
                    }
                    break;
                    
                case ScoreEventType.PlayerDamage:
                    // Additional damage handling if needed
                    break;
                    
                case ScoreEventType.WaveCompletion:
                    // Wave completion effects
                    break;
            }
        }
        
        #endregion
    }
}
