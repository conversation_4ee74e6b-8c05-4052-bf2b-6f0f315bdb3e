# February 19th

colour palette - develop one!
shadows never black - blue?
look at color theory
adobe colour

know what not to polish!

- feature creep

every step of the way, ask 'why is the character doing that' and revise - this is how to write bullet proof script

Point lights for color chaging??

Depth Testing in this shader - do this effect?

[https://www.youtube.com/watch?v=nKMTU6TbUF0](https://www.youtube.com/watch?v=nKMTU6TbUF0)

Adjust radius in which bullets lose targetting> May be an issues

Fixed sizing on light trails of diamond enemies

How to remove unnecessary assets? Export approach caused many issues

Delete most scenes, than use something that finds uneeded assets

[Rez Analysis](February%2019th%20bfdc80c324114c19b31dc1378c46a9b2/Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3.md)

Bullet issues not always firing at player? May be enemy rotation issues

No real need to use A* movement path finding on Ophanim level - change to standard? But things are moving around me, so may still be necessary

SHould I have enemy shooting Particles always face the player? 

Doesnt effect things - need to try earlier version and see the difference - must have changed some value that is important!

Geometric enemies with points reduced as we move forward?

Square to triangle to line etc