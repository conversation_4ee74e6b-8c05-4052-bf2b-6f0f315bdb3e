# May 13
 2 | 
 3 | Things to try / Implement!
 4 | 
 5 | [May 1](May%201%20038edfee9455402aa54b8dce20ccf465.md) 
 6 | 
 7 | Is this an issue? Investigate
 8 | 
 9 | ```jsx
10 | warning: You are currently using Visual Studio 2019 to compile and link C++ code. It is recommended that you upgrade to Visual Studio 2022 for better performance.
11 | UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)
12 | ```
13 | 
14 | Tracking chart
15 | 
16 | | Task | Details | Status |
17 | | --- | --- | --- |
18 | | New UI for Projectile and Enemy Locks | Current one that follows the reticle is not very useful and not working properly |  |
19 | | Refine Reticle Control and Enemy / Projectile Lock On | Doesn’t feel as good as it should, hard to lock on to enemies |  |
20 | | Play with Parry Feature | Doesn’t feel responsive, logic seemed off when trying to use it, may need some rethinking |  |
21 | | Motion Extraction Effect | Figure this out! |  |  |
22 | | May 1st Ideas Implementation | Go through May 1st Ideas and write up implementations |  |  |
23 | | Make Enemy Death Effect More Visible | Looks like its happening in scene view but barely see it in Play mode |  |
24 | | Motion Extraction Effect | Figure this out! |  |
25 | | A* or Ground Fitter Fix | Y rotation issues - disabled for now |  |
26 | | Skip Between Scenes | Previous method didn’t work and is removed |  |
27 | | Toonkit Manager Adjustments | Adjust shadows and light as appropriate |  |
28 | | Keijiro Duotone | Look at how to use this - test as scene transition effect? |  |
29 | | Reticle Spin | Not tied to koreographer - look at if this is ok implementation |  |
30 | | Game Manager Controls Music Transition | Need to build this out more - tied to scene list? |  |
31 | | Game Objects Inactive on Scene Load | Need to find proper solution for this |  |
32 | | Visual Studio 2019 Warning | Upgrade to Visual Studio 2022 for better performance |  |
33 | 
34 | Idea 
35 | 
 36 | Time / music / health
 37 | 
 38 | All the same
 39 | 
 40 | Music dies when time stops!
 41 | 
 42 | Build curiosity to make level repayable
 43 | 
 44 | X # of secrets found 
 45 | 
 46 | What are the rewards for replaying a level?
 47 | 
 48 | Make it so game plays has an Obvious approach + many non obvious options
 49 | 
 50 | How can there be endless mastery? Think devil daggers and others 
 51 | 
 52 | Speed running is a popular approach but how does that align with my game at all?
 53 | 
 54 | Need to think through how to better intermingle all my game mechanics
 55 | 
 56 | Instead of 5 different options, have them exist as multipliers for each other to create whole new actions 
 57 | 
 58 | Could I do a pacifist run?
 59 | 
 60 | Watch people play a game to see how they make their own fun