# Sept 6

Tried using target tracker and improving radar. Frustrating! Maybe off screen targetting is good, unsure, need a good way to test. 

Radar square and radar positioning not working out great 😟

On transition - need to freeze all player x / y / x positions then unfreeze when next wave starts

Currently changed to just disable and re-enable, which seems to work ok!

Need to get further levels setup in unity

Visually build them out a bit

Consider technical infrastructure, such as Metatron’s cube and others

trees / vines ? nav mesh too complicated - simplify? Or try to fully scan the vines?