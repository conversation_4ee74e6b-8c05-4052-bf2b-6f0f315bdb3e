# Sept 8

Need to fix lock on 

Then look through previous notes

- Fixed, lock on effect working

Making adjustments to spline to help prevent fall off of main character

Largely an issue with turning at certain places - perspective turning that is

Might need to tighten how turning works so player doesnt get stuck 

Attempting to increase enemy and projectile visibility while balancing fog effect 

Attempting to fix player fall off issues. Sphere collider restricting movement space

Seems to work!

Transparency on enemy cube lock on nedds to be fixed 

Mobius 2 Variation has enemies falling off - look into this!

may hve ruined today’s project - go to backup overnight if so!

BTRemake is old version

Beat Remake is new / partially broken one

Check this stuff out and revise if needed!