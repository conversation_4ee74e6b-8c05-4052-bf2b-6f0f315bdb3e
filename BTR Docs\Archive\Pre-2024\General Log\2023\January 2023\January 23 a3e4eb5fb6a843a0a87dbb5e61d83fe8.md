# January 23

Rail-shooter along rings of the ophanim

![Untitled](January%2023%20a3e4eb5fb6a843a0a87dbb5e61d83fe8/Untitled.png)

Consider modern game / genres and how they apply to rhythm shooter

**How To Market a Game / GameDiscoverCo**

[Pay attention to these secretly popular sub-genres](https://howtomarketagame.com/2023/01/23/pay-attention-to-these-secretly-popular-sub-genres/)

Vampire Survivor-like

[Are Vampire Survivor-Likes Dead?](https://howtomarketagame.com/2023/01/09/are-vampire-survivor-likes-dead/)

Extraction Shooter

[How a pixel art shooter achieved a million dollar launch (the ZERO Sievert story)](https://howtomarketagame.com/2023/01/18/how-a-pixel-art-shooter-achieved-a-million-dollar-launch-the-zero-sievert-story/)

Low-fi horror

Popular games!

[Six types of best selling games](https://howtomarketagame.com/2022/11/21/six-types-of-best-selling-games/)

How to apply these to my game?

Chart these out - consider where they go

Thematic ideas

Ophanim - wheel - move along the inside / outside of the wheel

<PERSON><PERSON><PERSON> carrying god’s throne ?

Concept - enemy is blind until you do a certain thing / that thing is necessary 

Vampire Survivors

[Studying the Success of Vampire Survivors | Dissecting Design, Game Design Analysis](https://www.youtube.com/watch?v=pXBkJN_5PGM)

Short form design

score chasing game - what more does it add ? Reverse bullet hell

Persistence through play throughs 

Beat Traveller Notes

Bullets should be spinning so their movement is more apparent

give them rougher faces so this reads better? Less round?

Think - rails - linear game movement structures

Are there games where you’re stuck in one tower taking down enemies? 

Parrying - as an opposite to current mechanic? 

Do you lock targets, or just parry when close and shoots at the enemy who shot it?

Is parrying a possible DODGE mechanic? 

Accessibility

[https://twitter.com/stevesaylor/status/1618344326312099841](https://twitter.com/stevesaylor/status/1618344326312099841)

What if facing the opposite direction, time moves backwards?

This would require