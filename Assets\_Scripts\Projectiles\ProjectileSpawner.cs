using System;
using System.Collections;
using System.Collections.Generic;
using Stylo.Epoch;
using UnityEngine;
using BTR;
using BTR.Projectiles;

namespace BTR
{

    public class ProjectileSpawner : MonoBehaviour
    {
        private static readonly int ColorProperty = Shader.PropertyToID("_Color");
        private static readonly int OpacityProperty = Shader.PropertyToID("_Opacity");
        private static readonly int TimeOffsetProperty = Shader.PropertyToID("_TimeOffset");

        // Singleton instance
        public static ProjectileSpawner Instance { get; private set; }

        [Header("Epoch Integration")]
        [SerializeField] private string epochClockKey = "Global";

        private Dictionary<int, Material> materialLookup = new Dictionary<int, Material>();

        // PHASE 5D: Legacy lastCreatedProjectile removed - use interface-based tracking

        private ProjectilePool projectilePool;
        private ProjectileEffectManager effectManager;
        private ProjectileManager projectileManager;

        private IEpochClock globalClock;

        public bool IsFullyInitialized =>
            projectilePool != null &&
            effectManager != null &&
            projectileManager != null &&
            globalClock != null;

        private void Awake()
        {
            Debug.Log($" [ProjectileSpawner] Awake START - GameObject: {gameObject.name}, InstanceID: {GetInstanceID()}");
            
            if (Instance != null && Instance != this)
            {
                Debug.LogWarning($" [ProjectileSpawner] Duplicate instance detected! Destroying {gameObject.name}, keeping {Instance.gameObject.name}");
                Destroy(gameObject);
                return;
            }
            
            Instance = this;
            Debug.Log($" [ProjectileSpawner] Instance set successfully - {gameObject.name}");

            Debug.Log($" [ProjectileSpawner] Getting components from GameObject: {gameObject.name}");

            projectilePool = GetComponent<ProjectilePool>();
            effectManager = GetComponent<ProjectileEffectManager>();
            projectileManager = GetComponent<ProjectileManager>();

            Debug.Log($" [ProjectileSpawner] Component initialization results:");
            Debug.Log($"   - ProjectilePool: {(projectilePool != null ? " FOUND" : " NULL")}");
            Debug.Log($"   - EffectManager: {(effectManager != null ? " FOUND" : " NULL")}");
            Debug.Log($"   - ProjectileManager: {(projectileManager != null ? " FOUND" : " NULL")}");
            
            if (projectilePool == null) Debug.LogError($" [ProjectileSpawner] CRITICAL: ProjectilePool component missing on {gameObject.name}!");
            if (effectManager == null) Debug.LogError($" [ProjectileSpawner] CRITICAL: ProjectileEffectManager component missing on {gameObject.name}!");
            if (projectileManager == null) Debug.LogError($" [ProjectileSpawner] CRITICAL: ProjectileManager component missing on {gameObject.name}!");

            StartCoroutine(InitializeGlobalClock());
            
            Debug.Log($" [ProjectileSpawner] Awake COMPLETE - IsFullyInitialized: {IsFullyInitialized}");
        }

        private IEnumerator InitializeGlobalClock()
        {
            // Wait a few frames to ensure EpochTimekeeper is initialized
            yield return new WaitForSeconds(0.1f);

            if (EpochTimekeeper.Instance != null)
            {
                try
                {
                    // Find the configured global clock component
                    EpochGlobalClock[] globalClocks = FindObjectsOfType<EpochGlobalClock>();
                    foreach (var clock in globalClocks)
                    {
                        if (clock.clockKey == epochClockKey)
                        {
                            globalClock = clock;
                            break;
                        }
                    }

                    if (globalClock == null)
                    {
                        Debug.LogWarning($"Global clock '{epochClockKey}' not found. Will retry in Start.");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"Error finding global clock '{epochClockKey}': {e.Message}. Will retry in Start.");
                }
            }
        }

        private void Start()
        {
            // If clock wasn't initialized in Awake, try again
            if (globalClock == null && EpochTimekeeper.Instance != null)
            {
                try
                {
                    // Find the configured global clock component
                    EpochGlobalClock[] globalClocks = FindObjectsOfType<EpochGlobalClock>();
                    foreach (var clock in globalClocks)
                    {
                        if (clock.clockKey == epochClockKey)
                        {
                            globalClock = clock;
                            break;
                        }
                    }

                    if (globalClock == null)
                    {
                        Debug.LogWarning($"Global clock '{epochClockKey}' still not found in Start. Some functionality may be limited.");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"Error finding global clock '{epochClockKey}' in Start: {e.Message}. Some functionality may be limited.");
                }
            }
        }

        // PHASE 5D: Legacy ProcessShootProjectile method removed - use ProcessShootProjectileInterface instead

        public bool RequestEnemyShot(Action shotAction)
        {
            float timeScale = globalClock != null ? globalClock.TimeScale : 1f;

            if (timeScale <= 0)
            {
                Debug.Log($"[{GetType().Name}] Shot denied due to timeScale <= 0");
                return false;
            }

            shotAction.Invoke();
            return true;
        }

        public void ShootProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float uniformScale,
            float damage,
            bool enableHoming = false,
            Material material = null,
            Transform target = null
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = uniformScale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material)
            };
            ProjectilePool.Instance.EnqueueProjectileRequest(request);

            Debug.Log($"[{GetType().Name}] Enqueued projectile request with scale: {uniformScale}");
        }

        public IProjectile ShootProjectileFromEnemy(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material,
            string clockKey,
            float accuracy,
            Transform target
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material),
                ClockKey = clockKey,
                Accuracy = accuracy
            };

            Debug.Log($"[{GetType().Name}] Attempting to get projectile from pool. ProjectilePool null: {projectilePool == null}");

            if (projectilePool == null)
            {
                Debug.LogError($"[{GetType().Name}] ProjectilePool is null! Cannot get projectile for enemy shot!");
                return null;
            }

            IProjectile projectile = projectilePool.GetProjectileInterface();
            Debug.Log($"[{GetType().Name}] ProjectilePool.GetProjectileInterface() returned: {projectile != null}");

            if (projectile == null)
            {
                Debug.LogError($"[{GetType().Name}] Failed to get projectile for enemy shot! ProjectilePool exists but GetProjectileInterface() returned null.");
                return null;
            }

            ProcessShootProjectileInterface(request, projectile);
            ProjectileManager.Instance.RegisterProjectileInterface(projectile);

            // Handle state management based on projectile type
            SetupEnemyProjectileState(projectile, target);

            Debug.Log($"[{GetType().Name}] Projectile created and registered for enemy. Position: {position}, Speed: {speed}, Target: {(target != null ? target.name : "None")}");

            return projectile;
        }

        private int RegisterMaterial(Material material)
        {
            if (material == null)
                return -1;
            int id = material.GetInstanceID();
            if (!materialLookup.ContainsKey(id))
            {
                materialLookup[id] = material;
            }
            return id;
        }

        private Material GetMaterialById(int materialId)
        {
            if (materialId != -1 && materialLookup.TryGetValue(materialId, out Material material))
            {
                return material;
            }
            return null;
        }

        // PHASE 5D: GetLastCreatedProjectile method removed - use interface-based tracking

        /// <summary>
        /// Process projectile setup using the unified IProjectile interface
        /// </summary>
        public void ProcessShootProjectileInterface(ProjectileSpawnRequest request, IProjectile projectile)
        {
            if (projectile == null) return;

            try
            {
                Debug.Log($"[ProjectileSpawner] Setting projectile position to: {request.Position}, rotation: {request.Rotation}");

                projectile.Transform.position = request.Position;
                projectile.Transform.rotation = request.Rotation;
                projectile.Transform.localScale = Vector3.one * request.Scale;

                projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, request.Target);
                projectile.GameObject.SetActive(true);

                Debug.Log($"[ProjectileSpawner] Projectile activated at final position: {projectile.Transform.position}");

                // Set up physics if available
                var rb = projectile.GameObject.GetComponent<Rigidbody>();
                if (rb != null)
                {
                    rb.isKinematic = false;
                    rb.linearVelocity = request.Rotation * Vector3.forward * request.Speed;
                }

                // PHASE 5D: Accuracy and clock management handled by new system components
                Debug.Log($"[ProjectileSpawner] Accuracy: {request.Accuracy}, Clock: {request.ClockKey}");

                Debug.Log($"[ProjectileSpawner] Setup {projectile.GetProjectileSystemType()} projectile with unified interface");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileSpawner] Error processing projectile interface request: {e.Message}");
            }
        }

        /// <summary>
        /// PHASE 5D: Setup enemy projectile properties using interface
        /// </summary>
        private void SetupEnemyProjectileState(IProjectile projectile, Transform target)
        {
            // PHASE 5D: New system only - set properties directly
            projectile.IsPlayerShot = false;
            if (target != null)
            {
                projectile.CurrentTarget = target;
            }
            Debug.Log($"[ProjectileSpawner] Enemy projectile state setup - target: {(target != null ? target.name : "None")}");
        }

        /// <summary>
        /// PHASE 5D: Setup player projectile properties using interface
        /// </summary>
        private void SetupPlayerProjectileState(IProjectile projectile)
        {
            // PHASE 5D: New system only - set properties directly
            projectile.IsPlayerShot = true;
            Debug.Log($"[ProjectileSpawner] Player projectile state setup complete");
        }

        /// <summary>
        /// Get a projectile using the unified interface (PHASE 4B: Prefer new system)
        /// </summary>
        private IProjectile GetProjectileInterface()
        {
            return projectilePool.GetProjectileInterface();
        }

        // PHASE 5D: GetProjectileForEnemy() method removed - use GetProjectileInterface() instead

        // PHASE 5D: Legacy wrapper methods removed - no longer needed with new system only

        private Queue<StaticEnemyProjectileRequest> staticEnemyProjectileQueue = new Queue<StaticEnemyProjectileRequest>();
        private bool isProcessingStaticEnemyProjectiles = false;

        private struct StaticEnemyProjectileRequest
        {
            public Vector3 Position;
            public Quaternion Rotation;
            public float Speed;
            public float Lifetime;
            public float Scale;
            public float Damage;
            public bool EnableHoming;
            public int MaterialId; // Change from Material to MaterialId
        }

        public void ShootStaticEnemyProjectile(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material)
        {
            staticEnemyProjectileQueue.Enqueue(new StaticEnemyProjectileRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                MaterialId = RegisterMaterial(material)
            });

            if (!isProcessingStaticEnemyProjectiles)
            {
                StartCoroutine(ProcessStaticEnemyProjectiles());
            }
        }

        private IEnumerator ProcessStaticEnemyProjectiles()
        {
            isProcessingStaticEnemyProjectiles = true;
            WaitForSeconds shortDelay = new WaitForSeconds(0.02f); // Reduced delay between batches

            while (staticEnemyProjectileQueue.Count > 0)
            {
                int batchSize = Mathf.Min(30, staticEnemyProjectileQueue.Count); // Process up to 30 projectiles per batch
                for (int i = 0; i < batchSize; i++)
                {
                    if (staticEnemyProjectileQueue.Count > 0)
                    {
                        StaticEnemyProjectileRequest request = staticEnemyProjectileQueue.Dequeue();
                        IProjectile projectile = GetProjectileInterface();

                        if (projectile != null)
                        {
                            SetupStaticEnemyProjectile(projectile, request);
                            projectile.GameObject.SetActive(true);
                            Debug.Log($"[{GetType().Name}] Static enemy projectile shot from {request.Position}");
                        }
                        else
                        {
                            Debug.LogWarning($"[{GetType().Name}] Failed to get projectile from pool for static enemy.");
                        }

                        // Add a small random delay between individual shots in the batch
                        yield return new WaitForSeconds(UnityEngine.Random.Range(0.005f, 0.015f));
                    }
                }

                yield return shortDelay; // Short delay between batches
            }

            isProcessingStaticEnemyProjectiles = false;
        }

        private void SetupStaticEnemyProjectile(IProjectile projectile, StaticEnemyProjectileRequest request)
        {
            projectile.Transform.position = request.Position;
            projectile.Transform.rotation = request.Rotation;
            projectile.Transform.localScale = Vector3.one * request.Scale;
            projectile.SetupProjectile(request.Damage, request.Speed, request.Lifetime, request.EnableHoming, request.Scale, null);

            if (request.MaterialId != -1)
            {
                // PHASE 5D: Set material using GameObject access
                var renderer = projectile.GameObject.GetComponentInChildren<Renderer>();
                if (renderer != null)
                {
                    renderer.material = GetMaterialById(request.MaterialId);
                }
            }

            // PHASE 5D: Set enemy projectile properties using interface
            projectile.IsPlayerShot = false;
        }

        /// <summary>
        /// PHASE 4B: Updated to use unified interface with legacy compatibility
        /// </summary>
        public IProjectile ShootPlayerProjectileInterface(
            Vector3 position,
            Quaternion rotation,
            float damage,
            float speed,
            float scale,
            float lifetime,
            bool enableHoming = false,
            Transform target = null)
        {
            Debug.Log($"[{GetType().Name}] ShootPlayerProjectileInterface called. Position: {position}, Rotation: {rotation}, Speed: {speed}, Homing: {enableHoming}");

            IProjectile projectile = GetProjectileInterface();

            if (projectile != null)
            {
                // Setup projectile using provided parameters instead of CrosshairCore
                projectile.Transform.position = position;
                projectile.Transform.rotation = rotation;
                projectile.Transform.localScale = Vector3.one * scale;
                projectile.IsPlayerShot = true;
                projectile.SetupProjectile(damage, speed, lifetime, enableHoming, scale, target);
                projectile.GameObject.SetActive(true);

                if (projectile.Rigidbody != null)
                {
                    projectile.Rigidbody.isKinematic = false;
                    Vector3 shootDirection = rotation * Vector3.forward;
                    projectile.Rigidbody.linearVelocity = shootDirection * speed;
                    Debug.Log($"[{GetType().Name}] Set projectile velocity: {shootDirection * speed}");
                }

                ProjectileManager.Instance.RegisterProjectileInterface(projectile);

                // Handle state setup based on projectile type
                SetupPlayerProjectileState(projectile);

                Debug.Log($"[{GetType().Name}] Player projectile created and shot. Position: {position}, Speed: {speed}, Direction: {rotation * Vector3.forward}, Type: {projectile.GetProjectileSystemType()}");
            }
            else
            {
                Debug.LogError($"[{GetType().Name}] Failed to get projectile from pool for player shot.");
            }

            return projectile;
        }

        /// <summary>
        /// Backward compatibility overload - uses CrosshairCore position/rotation
        /// </summary>
        public IProjectile ShootPlayerProjectileInterface(float damage, float speed, float scale)
        {
            if (CrosshairCore.Instance == null)
            {
                Debug.LogError($"[{GetType().Name}] CrosshairCore.Instance is null!");
                return null;
            }

            Vector3 shootPosition = CrosshairCore.Instance.RaySpawn.transform.position;
            Quaternion shootRotation = CrosshairCore.Instance.RaySpawn.transform.rotation;

            return ShootPlayerProjectileInterface(shootPosition, shootRotation, damage, speed, scale, 10f, false, null);
        }

        // PHASE 5D: ShootPlayerProjectile() method removed - use ShootPlayerProjectileInterface() instead

        /// <summary>
        /// PHASE 4B: Updated to use unified interface
        /// </summary>
        public IProjectile SetupProjectileInterface(
            Vector3 position,
            Quaternion rotation,
            float speed,
            float lifetime,
            float scale,
            float damage,
            bool enableHoming,
            Material material,
            string clockKey,
            float accuracy,
            Transform target
        )
        {
            var request = new ProjectileSpawnRequest
            {
                Position = position,
                Rotation = rotation,
                Speed = speed,
                Lifetime = lifetime,
                Scale = scale,
                Damage = damage,
                EnableHoming = enableHoming,
                Target = target,
                MaterialId = RegisterMaterial(material),
                ClockKey = clockKey,
                Accuracy = accuracy
            };

            IProjectile projectile = GetProjectileInterface();
            if (projectile != null)
            {
                ProcessShootProjectileInterface(request, projectile);
                ProjectileManager.Instance.RegisterProjectileInterface(projectile);
                SetupEnemyProjectileState(projectile, target);
            }

            Debug.Log($"[{GetType().Name}] Projectile created and registered. Position: {position}, Speed: {speed}, Target: {(target != null ? target.name : "None")}, Type: {projectile?.GetProjectileSystemType()}");

            return projectile;
        }

        // PHASE 5D: SetupProjectile() method removed - use SetupProjectileInterface() instead
    }
}