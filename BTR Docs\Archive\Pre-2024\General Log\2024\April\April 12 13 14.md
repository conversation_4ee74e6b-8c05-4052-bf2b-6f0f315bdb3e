# April 12 / 13 / 14

Twin snake - on each side

Polyrhythm shooting tied with music!

3 against two?

when landing on the beat - dodge!

otherwise can shoot back

- how can i do a parry option that fires in direction instead of lock on?
    - could be fun!

Attempting to add this parry shot to the game now, quickly tapping the lock button

Having problems with implementing this - removed most of the functionality but left quick tap features for trying this again 

Will need some rethinking and adjustment

Need to come back to this

Successful material swap on projectiles nows

trying to get a lock of VFX /  particle effect happening now

Added Movement sensor script to twin snakes to make sure they move a little bit if they’re about to animate / hit into the player track. 

- disabled cause it’s causing problems , will see if worthwhile in the future

Also adding fog density volumes to try and hide tails of snakes

SNake animations

- could use get hit left / right for firign things wheh nchasing on left / right

Have implemented this, i like it. 

Trying to get better projectiles accuracy at high speeds, adjustin g projectile logic for this 

Setting target to ‘Player Aim Target’ for my precise target for projectiles

twin snake boss did not have homing enabled….. 

very annoying

Need to clean up these classes a bunch

Now player appears to be getting hit

Fixed! Shoot speed effecting homing and non homing now

Now can set player and enemies much faster, and it’s not a problem for the bullets

need to chart out my relevant methods, slim down everything to make things clearer / work

For background

- Put tail animator snake on curvy spline, have it wiggle around in places

Need to properly plan out Ouroboros! AND other levels + tech needed