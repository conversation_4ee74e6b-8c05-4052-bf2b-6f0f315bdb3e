# May 16

Enemies appear to be working on mesh! Kinematic was enabled and this was the problem. 

Need to be more organized to have this information available easily, can reference own files to know if I’ve done everything I can. 

Have the browsing functionality in chat gpt and tried AGAIN to make a datamosh effect but it wasnt a much better situation 

Enemies sticking to mesh, need to fix projectile pooling

Partially fixed thsi pooling, but still seem to build up a lot of inactive projectile game objects.

Need to look closely at when it’s released from the system, how it’s recycled

Can gain performance here i think, or at least memory?

Can I set specific spawn points for specific waves? 

Addressed this with Trigger Volumes! Seems to be working fine

[Optimization Tips](May%2016%203cb913db9061453f97a1d249ccfd9b61/Optimization%20Tips%2095ef9e3b01c1490d869a2aa3567924b8.md)

Added Conditional Debug static class