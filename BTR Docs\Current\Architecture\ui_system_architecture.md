---
systems: [ui]
components: [layout, animation, localization]
tags: [visual, game-system, player-facing]
priority: p1
last_touched: 2025-04-09
links:
  - ![[Systems/Player/Architecture#^ui-integration]]
  - ![[Tech Recommendations/UI/PerformanceOptimization.md]]
---
# BTR UI System Architecture
[[User Experience Guidelines]] → [[Localization System]]

```mermaid
flowchart TB
    %% Core UI Components
    PlayerUI[PlayerUI]
    LoadingScreen[LoadingScreen]
    MainMenu[MainMenuSwitchScene]
    WaveHUD[WaveHUDTimed]
    
    %% UI Elements
    HealthUI[Health UI]
    ScoreUI[Score UI]
    StaminaUI[Stamina UI]
    LockUI[Lock System UI]
    WaveUI[Wave Counter]
    TimerUI[Timer UI]
    
    %% Game Systems
    GameEvents[GameEventsManager]
    PlayerHealth[PlayerHealth]
    ScoreManager[ScoreManager]
    StaminaController[StaminaController]
    PlayerLocking[PlayerLocking]
    
    %% Core UI Relationships
    PlayerUI --> HealthUI
    PlayerUI --> ScoreUI
    PlayerUI --> StaminaUI
    PlayerUI --> LockUI
    PlayerUI --> WaveUI
    PlayerUI --> TimerUI
    
    %% System Integrations
    GameEvents --> PlayerUI
    PlayerHealth --> HealthUI
    ScoreManager --> ScoreUI
    StaminaController --> StaminaUI
    PlayerLocking --> LockUI
    
    %% Event Subscriptions
    GameEvents -.->|OnGameRestarted| PlayerUI
    GameEvents -.->|OnGameOver| PlayerUI
    GameEvents -.->|OnScoreUpdated| PlayerUI
    GameEvents -.->|OnPlayerHealthChanged| PlayerUI
    GameEvents -.->|OnHighScoreUpdated| PlayerUI
    GameEvents -.->|OnWaveCountUpdated| PlayerUI
    
    %% Scene Management
    LoadingScreen --> MainMenu
    MainMenu --> SceneManagement[Scene Management]
    
    %% Wave System Integration
    WaveHUD --> WaveUI
    WaveHUD --> TimerUI

    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef ui fill:#bbf,stroke:#333,stroke-width:2px
    classDef system fill:#bfb,stroke:#333,stroke-width:2px
    classDef events fill:#fbb,stroke:#333,stroke-width:2px
    
    class PlayerUI,LoadingScreen,MainMenu,WaveHUD core
    class HealthUI,ScoreUI,StaminaUI,LockUI,WaveUI,TimerUI ui
    class PlayerHealth,ScoreManager,StaminaController,PlayerLocking system
    class GameEvents,SceneManagement events
```

## Color Legend
- 🟪 Core (Purple): Main UI controllers
- 🟦 UI (Blue): UI element components
- 🟩 Systems (Green): Game system integrations
- 🟥 Events (Red): Event system and scene management

## System Description
This diagram details the UI System's architecture, showing:

1. **Core UI Components**
   - PlayerUI as central UI coordinator
   - Loading screen management
   - Main menu scene switching
   - Wave HUD system

2. **UI Elements**
   - Health and stamina displays
   - Score tracking and high scores
   - Lock-on system visualization
   - Wave counter and timer
   - Dynamic score change notifications

3. **System Integration**
   - Event-based updates
   - Game state monitoring
   - Player state visualization
   - Scene transition management

4. **Event Handling**
   - Game state events
   - Score update events
   - Health change events
   - Wave progression events

5. **Performance Features**
   - String builder optimization
   - Pooled UI elements
   - Event-driven updates
   - Fade animations

[[Architecture/architecture_overview|Back to Overview]]