# July 5th

Lost of changes and improvements over the past couple weeks

Need to look back on June notes to see what was fixed / whats still standing

Brainstorming session important now on progression of Ophanim levels

On one ring / enemies on multiple others. Take them out

Take out enough enemies - move to next section

QUESTION: Is this on the same ring? Or a new ring? Can i have two waves on rings - 3rd is interior eye? 

Interior Eye - Cube or other shape with eyes on all sides 

Maybe a bit of a rhythm game in that you’re floating in the middle and different eyes are shooting at you - eyelid opens and closes - you need to lock on and shoot an open eye. if it’s closed it doesnt damage them. cube is rotating and you only need to take out 4 of 6 eyes. 

Idea for main ophanim boss 

- similar mechanic to level, but every few you take out, you go deeper on the ring?
- you eventually get to the central eye

**July 9th**

Can I use these to make snakes movement look more interesting?
https://assetstore.unity.com/packages/tools/modeling/deform-148425

Use Deform to alter levels / mesh in interesting ways? Could this help add variety to levels?

Try deform of colliders and see how enemies respond to that. Check performance of altering these things.

Tried Recast for movement of enemies in an internal cube, unfortunately did not work. 

How can i have enemies freely move along walls and odd surfaces, but have those surfaces moving?

Looking at A* demos for level/enemy ideas

Everything very flat - putting enemies on wall or similar doesnt seem to be possible. 

Can use basic unity navmesh and achieve this though
https://www.youtube.com/watch?v=QCYBI6JaeIU

Could possible use the same system meant for odd shapes and have the enemies move around properly there. need a more rounded shape with flipped normals but it might work. I dont think i could have it move around though.

**WHy not just spin the player in place instead? The whole platform and rotate making things seem like they’re spinning - probably the best bet!**

Could try both 

ALso try doing the galaxy material but with different textures for similar effect but different look 

Void Behemoth - Space - move around in the wake of a comet / asteroid ? 

Mild amount of light from it, rest of level quite dark with sound causing light to occur

Seraphim Nebula - Tendrils

Enemies likely need to move along curvy splines matched to tendrils as well.

Defeat some - shift to next set of tendrils

**World Tree Yggdrasil -** 

Similar to tendrils but they’re not moving. We’re moving along branches

Get to the base of the tree then go to move along the reflection of the tree?

Data flows from the branches

the ‘pool’ that seperates the two sides of the tree is data as well ‘Datapool’

Water element - reflection

Metatron’s Cube

- Player moves to the curvy spline that they’re currently facing while also trying to take out all the enemies
- Player and enemies move along different parts / splines
    - Use colour to delineate this
- 

 

Use Deform on stuff - it seems really good!