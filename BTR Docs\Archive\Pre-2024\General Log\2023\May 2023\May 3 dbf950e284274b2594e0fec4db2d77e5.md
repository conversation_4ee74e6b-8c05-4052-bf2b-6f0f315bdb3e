# May 3

RECAP

Ouroboros 3 - have music and other things working. Best template so far - can refence others for minor issues

Some things to fix

- Enemies are shooting but not moving
- Player running anim direction gets wonky - why?
- <PERSON><PERSON><PERSON> Reticle sideways and wonky - why?

1) Enemies moving - but fly off easily. Need to clamp them to navmesh properly. 

Using Advice from here to try and fix

["constrain inside graph" problem](https://forum.arongranberg.com/t/constrain-inside-graph-problem/11605/2)

Constrain inside graph does not seem to completely work - likely due to the way it’s implemented

Buto 64 not working - wait for Buto 65 for this to be fixed in several days

2) 

3) Crosshair reticle issue appears to be dependent on Gameplay Plane’s starting position, using Test Waypoint in fully upright position as the starting Dolly Waypoint appears to fix this problem.