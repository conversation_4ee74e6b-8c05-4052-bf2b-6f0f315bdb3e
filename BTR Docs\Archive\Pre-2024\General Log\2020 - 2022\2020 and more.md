# Rez V3 Log

**March 28th 2020**

Rebuilt using Starfox Mix and Jam + Red Dead Dead Eye and remnants of Rez V2

Looking good! Much better basis for control

Things to do

- Refine Lock on
- Import enemies and get bullet polling working
- Enemy Pooling?
- Fix Level Loader
- Koreographer shoot / lock on mechanisms

**April 3**

Working on timing 

Targets List Gathers all targets

Locked list is all targets in time

Release pulls from Locked list

Do I need true/false to prevent locking/firing from interferring with each other?

**IMP -** Need to run a check on Targets for anything that should be on LockedList

Having Lock and Shoot working, need to refine sounds and clean up

Look at Joost or other shaders effecting environment

Add vibration

Add particle effects for Death

Think of different bullet/enemy types

- Procedural Boss ex - [https://www.youtube.com/watch?v=LVSmp0zW8pY&t=124s](https://www.youtube.com/watch?v=LVSmp0zW8pY&t=124s)

Enemy ideas - Krang-like

**IDEA**  - At the end of odd grouping of locked on targets, play a tag to even it out!

April 4th

Vibration added

Reviewing Post-Processing [https://www.youtube.com/watch?v=9tjYz6Ab0oc](https://www.youtube.com/watch?v=9tjYz6Ab0oc)

Global for now, but Local Volume for Takahasi video effects?

Upgraded to URP with limited PP added

Trying out different timings for music

Added Kick vibration Script to Koreo Object

April 5th

Doing some of Particles tutorial - [https://www.youtube.com/watch?v=hyBbcFCvDR8&](https://www.youtube.com/watch?v=hyBbcFCvDR8&t=482s)

Need to tighten lock on - how to achieve this?

Basic color changing environment method added to Crosshairs class

Playing with Joost on bullets - need script to alter parameters. THinking of use cases

April 6th

**Goals**

Grey box levels! - Tested, need to make one with ProBuilder - Check!

Learn how Cinemachine Dolly works - Check!

Make a level transition! - Next level works, but timing is off - could easily setup a trigger as well

Added a character instead oh ship - adjust rotation and movement

Tried new shaders! 

Looking for particles for bullet destruction

Looking for better lock on graphic

MAKE A BACKUP SYSTEM WORK!!!!

April 7th

Backup working with Github - maybe use SourceContol as a second backup?

Learning Living Particles - affectors

Living Particles - Tiling and Offset create interesting effects - manipulate these with Koreogrpaher?

Scene Alt Audio 2-2 Trans - Procedural snake creature moving away shooting these???

Look at procedural youtube vid!

April 8th

IDEA: Voiceover with lip sync? evangelion face in horizon - looming head - type of thing

Added Odin Inspector

Added Cube Snake - [https://learn.unity.com/tutorial/using-animation-rigging-damped-transform?projectId=5f9350ffedbc2a0020193331#5faddb95edbc2a0fd05a3512](https://learn.unity.com/tutorial/using-animation-rigging-damped-transform?projectId=5f9350ffedbc2a0020193331#5faddb95edbc2a0fd05a3512)

IDEA: Fargo dePalma multiple camera angles - could use splitting the screen effectively like this?

**April 9**

Figure out issues with Particle Clip Channel Texture- What is it doing? Effects look drastically

Need Read/Write on the image used - easy fix

Using DOTween for basic movement

Building out Cube Snake Procedural aniamtion

Think about Koreographer / DOTween intergrations?

Added Iceberg Rotation

IDEA: Steve Reich movement - sync/out of sync stuff

**April 10th**

Greybox a level

Optimize?

- Shoot time already based on Koreographer Tempo

Created GlobalControls.SceneBPM for referencing Tempo

Experimented with Snake procedural graphics

Experimenting with particle shapes for bullets - can emit patterns and lots of things

Testing mixamo floating animation and Local Volume on player character

**April 11**

Matched Player HOLO Shader to Global SceneBPM

Greyboxing level concepts

Created **Object Rotations** script for setting up trigger object movements

Greyboxed level with these tools, likely interlude to song sections

Introduced Score and Lock counters on UI

Player Death now possible

Greybox a more traditional level with enemies next?

**April 12**

Introduced Audio Mixing Group

Ducking Noise track when Locking/Shooting 

Introduced Combo multiplier for Shooting - should this be an exponent?

Fixed Locks Number - shows number of locks counting down as enemies shot (didnt before)

Issues with Line Renderer - Not drawing lines between objects 

IDEA Change particle speed when locked?

Did this! Particles slowly reverse - maybe just have them hold?

IDEA Particle Spawner releases different patterns that can be drawn?

**April 13**

Sorted out an Ideas page for logging inspired thoughts

Greybox 1-2 - Melodic ideas and new skybox/Global Volume on

**April 14**

Looking into satisfying lock on rhythm adjustments

Looking into offset rhythm experiment with Rival Consoles track as example

- Koreogrpaher Pro Ideal for this?

Use F mod instead of Unity Tools? Need to see Koreographer integration

IDEA: Draw shapes with target selections - could this be a shield to protect against an enemy?

**April 17th**

Koreogrpaher Pro - Midi implementation

Ableton2Midi - MidiEditor for tempo - Import

DoTween Movement script - Koreo Square Movement

Object moves in square pattern in time with track

Working on Greybox 7 - use a span to dictate when an enemy is shooting?

**April 18th**

Particle shooting is working! Also found Emit on Span script for particles

Greybox 8 - Arp spastic tempo moves on enemies?

**April 19th**

Joined the Gamma Space casual chat! Talked for a couple hours about making games / playing games

**April 20th**

Need to fix shooting in Greybox 7 - see what shooting bug is

Fixed this - missing shooting track - also replaced audio for shooting for Greybox 7

Issues with MMFeedbacks giving up part way through scene

Upgrading to newest Unity 2019 LTS to fix TextMeshPro error - see if it helps anything else

MMFeedbacks Impulse Error - `Impulses exist in 3D space 

**April 21**

Change to Boxcast for aiming, I think it's working better

Need to check sizing 

Question Ray cast choices.... UI better? Unsure

Extended song a little bit

**April 22**

G7 song extended, put into level 

Working! 

Want to time particle releases to beat more

Need to identify how I want mixer to work - proper submixes etc

Need naming scheme on Koreographers - G7 - etc - similar to submixes

Set particle rate lower - makes it obvious ray cast not catching everything - FIX AIMING

Aiming Idea - Just use Square recticle as RaySpawn?

- How to always make it face forward? or where i want?

Look at Rez Ref tracks and think level design - FITNESSS ? 

**April 24**

**ds**dd Object Pooling not working as before? Maybe never working?

Particles are not dying, gathering up into big ball behind player

Added CowCatcher to Recycle bullet objects!

I THINK some locked on targets are getting recycled and causing issues - need to investigate this!

Playing with Player Moment script as well 

Camera Follow + Player Movement determine screen space

Need to find a nice balance here

Writing a mehtod to find the lost enenmies to try aND FIX THE PROBLEM 

**April 25**

**f**- Bug testing Lock on/particle bullets

Added color switching as well! some level design

**April 26**

Some bullets hanging in mid air - not being added to musical lock list. Still on targets list

![Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled.png](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled.png)

![Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%201.png](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%201.png)

![Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%202.png](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%202.png)

Seen as Locked and Released but missed being added to musicallocklist

![Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%203.png](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/Untitled%203.png)

Approaches to solving this problem?

Tried moving Lock=true from OnLock to OnMusicalLock, didnt work. Game feel was off and ended up with list of Targets that were not being added to MusicalLockList

Another approach, If something is on Target list for X amount of time but not MusicalLockList then DO THING?

[January 2022](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/January%202022%20130755c3ec8943419f6377e790bf4a86.md)

[December](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/December%20f39a3eb63d26466181638298c44037cb.md)

[November](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/November%20f777df0593434fac8b4f8ac2ee005e19.md)

[October](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/October%207e102a42b48a410fbd7119b993a2ba49.md)

[September](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/September%20648dffdc72a3457196e7cc30fa7eb51c.md)

[August](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/August%202d6ba10dca0b4efcb35ad2661f74a789.md)

[July](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/July%2002293e6a48594ec5a26f54f054b1dc1d.md)

[June](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/June%20ab4aa62b375044a6a3811fbb59c390f0.md)

[May](Rez%20V3%20Log%2097bf7586d4af4297a6c68d43fbbf768d/May%2083a377602a67417bb2a63c8cc1bbd387.md)

**April 28**

Playing with shapes - lock on shapes? - particle emitting shapes?

Where does this bring in an interesting second action for gameplay?

And how does this fit into/effect the structure of the music?

**April 29**

Change bullet movement from particle system to script based movement (translate based on speed)

This allows me to rewind movement with Chronos feature

Also allowed me to add a homing feature, with a switch can make bullet look at player and move towards them

Looking at Chronos/Koreographer integration now. Can hit rewind button on beat, but cannot return to normal time on beat of koreographer timeline

Also seems a bit buggy - cant switch modes quickly

Trying it out with a set amount of rewind, then launching back into normal scale with left bumper

Some issues may be due to record interval - needs to match tempo?

Error - when time resume to normal, player returns to original position

- Dolly issue? Fixed with slight offset in time

Need Shooting on Gameplay dolly - but this rewinds it's movement as well. Way to exclude certain axis from timeline? Want it to move with Gameplay Plane but not X and Y of screen

EXPLORE - central Koreo and multiple KOreo timelines effected by chronos

Need to blow up all bullets when entering reverse time?

Bullets being released from particle system issues - improved cow catcher to remove lock locked enemy and also DEATH everything it catches

Disabled rewind on shooting element

**May 1**

Found CowCatcher Bug - needs to remove LOCKS number (not currently doing this)

Still problems with Burst and released Bullet objects? Not sure whats happening - colliding with each other?

Moving Time Control to Shooting class?

Need a OnRewindShoot method because Koreographer timeline not available during rewind I THINK

Introduced shooting continuing through rewind loops

Maybe set a different sound for this? or reverse the shooting sound? not sure if thats happening now

Removed TimeControl - now integrated into Crosshairs script

At some point rewinding stops working.... Not sure why

Bullet freeze no longer working

**May 2**

Fixed Bullet freeze! Pretty sure this fixed cow catcher issues as well (locked items never hit cow catcher now)

Seems like time glitch isn't breaking anymore too - maybe a result of this?

**May 5**

Winding Rez Halls for a new level?

**May 6th**

Trying enemy snake with multi selects

CUBESNAKE

Some errors - probably need Regular Enemy script and Enemy Script for particle object system

- this is for releases from system on death, etc. maybe just a check if it's part of a particle system or not

GLobalControls like Scene BPM in ascriptable object seems like a good idea! look into this again

Need to setup a proper archetype for enemies and movement!

Also look at object particle spanner videos of shapes and styles! Lots of bullet styles here

IDEA: Move whole dolly and system in lock on pattern?

May 7th

If at full combo, slow down time? testing different combo numbers / effects

Glitching during musical firing

Make a log of current ongoing systems

IDEA - Shoot out objects in a particular shape, lock on to make shape, then particle shooter switches to new shape

Construct a volume at those points - color of objects looks different through that

shooting applies that color to the rest of the scene

This allows you to prepare for the change - but what is this change? and it's value?

Gntk movement videoHow to create constant flow in movement 

**Mayy 11th**

Shape as notation - different combos / different rhythms? notation and puzzles?

Release bullets as triangle that you connect together

Try doing a snare or kick release pattern for this - link together generate portal? or generate thing?

Triangles!!!

Things to do!

-Find out how to make a proper triangle

-Rotate to the beat

-3d audio / sounds of synths rising up as bullets come in?

Make a series of these type of levels / puzzles  and chain them together

Look up Hindsight game trailer for effect

May 12

Replace Rayspawn in Cross hair script with Reticle - does the aim work better????

THIS BROKE THINGS

Had to go through a lot of trouble to fix

Replace G9 with G8 and build up trianlge movement

Figure out with G8 bullets move backwards when not homing

**May 14th**

Fixed G9 

Seperate by month now