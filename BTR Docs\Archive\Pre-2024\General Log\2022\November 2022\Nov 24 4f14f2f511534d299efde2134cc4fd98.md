# Nov 24

Updated Packages - Need to check <PERSON><PERSON><PERSON> andoth<PERSON> for added features

Check Ko<PERSON><PERSON> thread in discord regarding tempo!

Profiler Tutorials 

CPU in Milliseconds

3-4 and 5 - 7

10 average player loop performance?

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled.png)

Resolution settings and issue for steam deck performance? 

Add Graphics settings to menu! Add ability to save these as well 

Recording specific pieces of code using the following!

Profliler.BeginSample( method name here )

Profile.EndSample ( method name here ) 

[How to profile and optimize a game | Unite Now 2020](https://www.youtube.com/watch?v=epTPFamqkZo)

My performance during play mode in-editor VS editor mode - BUSY SCENE

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%201.png)

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%202.png)

Tri drop from 15 Mil to 60 K using GPU Instancer

Process for Optimization

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%203.png)

If noGPU time is listed, look for the following

WaitforGFX or Waitfo GPU call in Profiler

If this comes up, we know that the GPU is taking longer and the CPU is waiting

Remote profiling from target device - try this in Windows builds?

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%204.png)

Will this make a difference in my game?

Bake lighting!

Ensure Dynamic Batching is working / on by verifying Render Pipeline settings 

Verify this is not an issue for other assets (GPU Instancer)

Look into Occlusion Culling

![Untitled](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%205.png)

Need to marker renderers as static in the scene - how does this affect GPU Instancer???

[How to customize performance metrics in the Unity Profiler | Unite 2022](https://www.youtube.com/watch?v=ixEk6EWrL6Y)

Interesting but not useful to me at this moment 

[How to Actually optimize your game in Unity - Complete Game Optimization Guide](https://www.youtube.com/watch?v=ysk7ATmIeOs)

Adjusting performance based around Medium settings

Changed Datastream to static - Minor performance difference at most - What about in GPU Instancer?

Many other performance tips in this video 

Physics - Auto Sync Transforms - Dsiabled but what does it do? Off by default

Turn down smoothness is recommended

Lighting is in subtractive mode - does this work for my project?

Currently no Lightmaps - not sure why? Nothing casts shadows?

[GPU Instancer Tutorial - Detail Manager - Terrain Grass and Vegetation](https://www.youtube.com/watch?v=ydeYDg_Ncno&list=PLWEl7wzzCgPjYO3_9M6QwvoPOXV9R6P1K&index=2&t=6s)

Doing an overview of GPU Instancer tutorials because I think this is a bottleneck 

GPU vs CPU

Looking into Floating Origin Point - Cant use Gameplay Plane - What else?

Try painting scene with GPU Instancer Terrain 

Move into Rigidbodies and Post Processing Performance Tutorial videos