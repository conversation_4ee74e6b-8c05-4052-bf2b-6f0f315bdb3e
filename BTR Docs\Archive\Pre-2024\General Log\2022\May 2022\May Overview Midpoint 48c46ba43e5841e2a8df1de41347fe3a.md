# May Overview Midpoint

Look over these things

May Overview Points to carry forward

- Possibly interesting system reference for music documentation
    - [https://mixolumia.com/makingmusic/](https://mixolumia.com/makingmusic/)
- Coming back to early inspirations
    - [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
- Mechanics and core narrative pillars - tying these things together
    - Is movement core or is it a method of getting from one puzzle to the next?
- Need to reassess music system integration with game systems
    - Look at original core documents
    - May 2 tutorial links and ableton setup - good influence on direction!
- Random Flow for Blender - cool asset to try on models!
- April 5 - good tips from Tunic dev on Post-Processing
- Could clean up code more! always an opportunity for that and the many assets in the project
- Revisit JSON / WaveSpawner tie ins - could this be better? Easier?
- Look up this different between Action and EventHandler! Seems useful
- GO over some of these notes on electronic music to compile ideas on them outside of the video game documents
- Color switching lock on not fully working - what is going on there?
- Tried a countdown on screen in transitions - how to revisit this?
    - Start function timer with tempo influenced numbers on screen?
    - Can do coroutine for this! Start as Coroutine!
- Reinstall Animation pack from Amplify and see if anything is useful?
- More animations for crosshair / lock on
    - Use the radars lock on info attachment?
- Bring in other scenery / buildings / environments - much like Tetris Effect!
    - Find a best method for doing this?
- Fix bullet direction firing issues
    - Fires from enemy in a weird direction?
- Need to figure out Pool Manager issues eventually! Cant currently reuse enemies
    - Pretty sure reusing bullets is fine, but is that per enemy or in general?
- Look into making lock on cube on enemies transparent or something else
- More FX in general! Shader Graph? Other things?
- Disable PAUSE in certain menus, or have it close out everything! Don’t want players glitching out of menus

File naming for Kore assets

Drums 1-8

Drums 1-16

Drums 1-32

Naming scheme for Perc Koreo Track ID 

Perc

Perc Half

Perc Half Half

These names can carry over to all sections and Koreography will automatically pick it up

Revisit this video on Delegates and Events?

[https://www.youtube.com/watch?v=6NRqwL3N5Go](https://www.youtube.com/watch?v=6NRqwL3N5Go)