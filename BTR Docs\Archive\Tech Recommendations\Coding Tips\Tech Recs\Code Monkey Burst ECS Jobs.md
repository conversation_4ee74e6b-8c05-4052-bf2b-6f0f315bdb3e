---
title: Code Monkey Burst ECS Jobs
tags: [<PERSON>, DOTS, ECS, Burst, Optimization]
date: 2025-01-20
---

# [[Code Monkey Burst ECS Jobs]]

## [[Introduction]]
Hello and welcome! In this tutorial, we'll learn how to get started using Unity's [[Data-Oriented Tech Stack (DOTS)]] with [[<PERSON>urs<PERSON>]] and the [[Entity Component System (ECS)]]. This technology stack can provide some insane performance results - literally 100x faster in some cases!

We'll cover:
- What is a [[Component]], [[System]], and [[Entity]]
- How to create jobs and run them with the [[Burst Compiler]]
- Combining entities and [[GameObjects]]
- Practical examples and [[Optimizations]]

```csharp
// Example component
public struct RotateSpeed : IComponentData {
    public float Value;
}
```

## [[Setting Up]]
Before we begin, make sure you're using:
- Unity 2022 LTS or newer
- Visual Studio 2022 (Community edition works fine)
- Enable Burst compilation (Jobs > Burst > Enable Compilation)

### Recommended Project Settings
1. Go to Edit > Project Settings > Editor
2. Enable Enter Play Mode Options
3. Disable Domain Reload and Scene Reload

## [[Core Concepts]]

### [[Entities]]
Entities are the basic units in ECS. They're essentially just IDs that reference collections of components.

```csharp
// Creating an entity
Entity entity = EntityManager.CreateEntity();
```

### [[Components]]
Components are pure data containers. They're implemented as structs that implement IComponentData.

```csharp
public struct Movement : IComponentData {
    public float3 Direction;
    public float Speed;
}
```

### [[Systems]]
Systems contain the logic that operates on entities with specific component combinations.

```csharp
public partial class MovementSystem : SystemBase {
    protected override void OnUpdate() {
        Entities
            .ForEach((ref Translation translation, in Movement move) => {
                translation.Value += move.Direction * move.Speed * Time.DeltaTime;
            }).ScheduleParallel();
    }
}
```

## [[Burst Compiler]]
The Burst compiler is one of the most powerful features of DOTS. It can make your code run orders of magnitude faster.

```csharp
[BurstCompile]
public struct RotateJob : IJobEntity {
    public float DeltaTime;

    void Execute(ref Rotation rotation, in RotateSpeed speed) {
        rotation.Value = math.mul(rotation.Value, 
            quaternion.RotateY(speed.Value * DeltaTime));
    }
}
```

## [[Job System]]
The Job System allows you to run code across multiple threads. Combine this with Burst for maximum performance.

```csharp
[BurstCompile]
public struct MoveJob : IJobEntity {
    public float DeltaTime;

    void Execute(ref Translation translation, in Movement move) {
        translation.Value += move.Direction * move.Speed * DeltaTime;
    }
}
```

## [[Practical Example: Rotating Cubes]]
Let's create a system that rotates cubes using ECS and Burst.

1. Create a RotateSpeed component
2. Create a RotationSystem
3. Implement a Burst-compiled job
4. Schedule the job in the system

```csharp
// Rotation System
public partial class RotationSystem : SystemBase {
    protected override void OnUpdate() {
        new RotateJob {
            DeltaTime = Time.DeltaTime
        }.ScheduleParallel();
    }
}
```

## [[Optimizations]]
- Use [[EntityCommandBuffer]] for structural changes
- Enable Burst compilation
- Use [[NativeArrays]] and [[NativeContainers]] for high-performance data storage
- Implement [[Aspects]] to group related components

## [[Conclusion]]
DOTS is a powerful tool for creating high-performance Unity applications. While it has a learning curve, the performance benefits are substantial. Remember:
- Start with simple systems
- Gradually introduce Burst and jobs
- Profile your code regularly
- Mix GameObjects and entities where appropriate

For more information, check out Unity's official [[DOTS Documentation]] and sample projects.