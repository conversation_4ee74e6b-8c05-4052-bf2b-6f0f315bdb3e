# May 23

THINGS TO DO

Fix Death / FX of Enemies - Parts are busted 

Enabled Funnel modifier on enemies - may have perofrmance impact - keep in mind!

Adjusting and cleaning up PorjectileStateBased a bit

Projectiles adjusted, seem to be hitting properly now 

Looking for good animated snake asset for transition

Using dummy snake head for now

Tried adding time slowdown for enemy death, not working. It’s in enemy basics script

Maybe do rewind instead? Implemented this - works oK!

Enemies bug out though - bug found the other day

Disabling Rewindable to see if that helps? DOES NOT

I think I need to disable movement while time rewinds - may require them to not be rewindable as well

Make a call to the RVO Controller to make the units Locked while time rewinds?

Unlock when time is normal

Also disabling rewindable to help this

Looks like this works!!

Need to fix enemy death, here’s the error

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled.png)

FIxed these, need some adjustments - not ideal effects and maybe too big

<aside>
💡 Bug!

</aside>

Need to resolve this projecitle issue

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled%201.png)

Recent bug report, this seems to be the solution 

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled%202.png)

Projectiles are not set to kinematic, so unsure why this is being flagged

Playing with lighting settings, trying to get something more interesting happening.

Need some well places lights around structures for a more dynamic look 

<aside>
💡 Bug!

</aside>

Seems I may need to have a check on time rewind on death, seems if two happen too close together it causes an infinite loop 

Also setting a hard 60 fps limit in game. This is on manager - DemoGameInit

This also sets up resolution and other things on start, may want to change this

Disabling ground check on Shooter Reticle movement because it’s more problems caused than solved