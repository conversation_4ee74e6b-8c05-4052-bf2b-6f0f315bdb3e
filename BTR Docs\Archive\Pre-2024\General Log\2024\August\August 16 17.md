# August 16/17

Fixed score issues where values displayed for player getting hit were not accurate

Enemy Killer script not fully workign, something wrong in Enemy class I think, debugging

- Fixed

Section 3 music not looping properly

Enemy spawning issue

Make tail aniamtor work when off screen as well? or some thing issue ossuring?

Enemy bullets too slow? NOt getting hit

Major code revisions in Game Manager to account for async scene loads

Seem to work! 

Upgraded Editor version in hopes of solving lack of memory issues occuring during deep profile

so far so good! but Memory Allocation tracking in prime tween not as straightforward as i thought

Fixed some issues with Enemy Damagable parts, and managed to maintain Burst 

A LOT of time on resetting projecitle lifetimes, necessary work. need to refine this so you cant just hold bullets forever - i think, could be a mechanic

like bomberman sort of

May need to rebuild Section 3, weird issue with not being able to shoot there. Not sure what the problem is exactly :(

Likely a koreography problem - track missing for shooting? Seems reasonable

THis was the problem! I need to chart out all of these things so i know and dont get stuck on stuff liek this again

Need to be more organized