# April 10

Debugging WaveSpawner issues - why wont waves advance?

Realized that when i disable pooling enemies will not work properly - this is due the reference for enemies to spawn, pointing to the prefab and not the enemy in the scene, attached to A*

Trying some fixes for this - can the enemy connect with A* Graph once enabled?

Enemy Plane is titled EP1

Implemented code to attach this once enemy is awake or enabled in scene but doesnt seem to work for the enemies, they dont move properly and seem confused

<PERSON><PERSON> email about it to Wave Spawn Dev

ALSO - double shooting issues appears to be the particle system flying off lol? WHat’s happening there i wonder