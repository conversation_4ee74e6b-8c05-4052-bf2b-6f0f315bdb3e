# August 8

COnversion of <PERSON><PERSON><PERSON> to three speerate scripts seems to mostly work!
Montioring…..

Need to also monitor previous error of being stuck on reverse time

- Have not figured out exactly why this occurs

Adjusting see through material for enemies - making it less visible

Want chained enemy type in Section 3

Makeing them move in a snake like fasion…. unsure this can happen. Will require further deep experiments. Right now it might make the most sense to try something in line with what works right now. have the other parts spn around? use PrimeTween? 

setting them up as orbitals! testing

Seems the previous snake prefab works ok, need to figure out quick turning or other issues, but could potentially do something cool with this. 

Use both enemy type in different areas, play with these concepts

Types

- Snakes
- Orbitals

More ideas 

- Fractured - destroying it breaks into multiple smaller enemies - or projectiles! That are flynig towards the player to hit them.
- Tesselated Wall - A large enemy composed of many interlocking polyhedrons that form a wall or barrier. As the player attacks, the polyhedrons detach and become independent attackers.
- Elastic - think vertices - A shape that stretches and morphs its shape, making it difficult to predict its movement. It can extend its sides to form spikes or turn into a flat sheet to dodge attacks. Also maybe these vertices are the only lock on poitns?
- Mines - these need to be taken out before the player hits them and gets hurt
- Phased movement - disappear and reappear in different spots
- Large guardians - maybe static in their positon and slow to move, but deal big damage
- Reflective shields - attacks boucne off them at differnet times. this could be the time ability in action or just a timed shield based on the music
- Cloaked Stalkers: move between invisible and visible! radar comes in handy for this especially