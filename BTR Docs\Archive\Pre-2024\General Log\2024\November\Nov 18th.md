# Nov. 18th

```csharp
    public override void OnTriggerEnter(Collider other)
    {
        GameObject hitObject = other.gameObject;

        if (hitObject.CompareTag("Enemy"))
        {
            // Handle enemy collision
            HandleCollision(hitObject);
        }
        else
        {
            Debug.Log($"Projectile hit non-enemy object: {hitObject.name}");
            _projectile.gameObject.SetActive(false); // Deactivate the projectile
            ProjectilePool.Instance.ReturnProjectileToPool(_projectile); // Return it to the pool
        }
    }

```

PlayerShotState is deactivating projectiles too frequently, likely waht we need to do is dsiable certain collision layers on the projectile - this may have been messed up in the switch to Unity 6.

For now, i’ve commented out the deactivation and return to pool when colliding

Trying to optimzie the infinite track generation, implement a loadignm screen that infintie ouroboros uses during intiial optimizaiton

Trying to figure out the looping functioanlity for locking mode - trying Destiantion Marker named ‘LoopPoint’ for Scene 3 - where the performance is heavily impacted by previous implementation of couroutines

NOT WORKING

Need to rethink this - idea is pretty good, have the shooting change in lock state, or something like that, just need to implement it smarter