using UnityEngine;
using FMODUnity;
using FMOD.Studio;
using BTR.EnemySystem.Interfaces;
using BTR.EnemySystem.Configurations;
using BTR.Audio;
using Cysharp.Threading.Tasks;

namespace BTR
{
    public class EnemyAudioBehavior : MonoBehaviour, IEnemyBehavior
    {
        [SerializeField] private EventReference spawnSoundEvent;
        [SerializeField] private EventReference deathSoundEvent;
        [SerializeField] private EventReference hitSoundEvent;

        private Transform enemyTransform;
        private EnemyConfiguration configuration;

        public void Initialize(Transform transform, EnemyConfiguration config)
        {
            enemyTransform = transform;
            configuration = config;

            if (configuration != null)
            {
                // Override serialized values with configuration if available
                spawnSoundEvent = !configuration.spawnSoundEvent.IsNull ? configuration.spawnSoundEvent : spawnSoundEvent;
                deathSoundEvent = !configuration.deathSoundEvent.IsNull ? configuration.deathSoundEvent : deathSoundEvent;
                hitSoundEvent = !configuration.hitSoundEvent.IsNull ? configuration.hitSoundEvent : hitSoundEvent;
            }
        }

        public async void PlaySound(EventReference eventRef)
        {
            if (!eventRef.IsNull)
            {
                var instance = await AudioManager.Instance.GetOrCreateInstanceEnhancedAsync(
                    eventRef.Path,
                    AudioConfigurationSO.AudioCategory.Combat
                );

                if (instance.isValid())
                {
                    instance.set3DAttributes(RuntimeUtils.To3DAttributes(enemyTransform.position));
                    instance.start();

                    // Phase 4 LOD system removed; FMOD natively handles 3D audio and optimization.
                }
            }
        }

        public void PlaySpawnSound() => PlaySound(spawnSoundEvent);
        public void PlayDeathSound() => PlaySound(deathSoundEvent);
        public void PlayHitSound() => PlaySound(hitSoundEvent);
    }
}