# June 28

MentalCheckpoint Rhythm game thoughts - 2 parts!

[Why Rhythm Games Haven't Changed for 20 Years](https://www.youtube.com/watch?v=wb45O2NciL8&list=PL67pghfWOIM3r3fd_ydsyr0HvuGH7gcXd)

Whats the hook?

Catching notes on a judgement line - need good telegraphing!

Flexible mechanic that can have depth

Stretch an idea out to many iterations!

Flow as most important aspect of rhythm game!

Beatmap / charting

Continuous clusters of notes to create Flow

Intensity curve important! cant go all hard all the time

Teaching mechanics through visual and audio cues

Rhythm Doctor example highlighted

Performative play

How the player feels and looks while they play the game

IDEA: Dance Rush Stardom applied to a controller?

Difficulty Depth

As much depth as possible out of as little complexity as possible

Difficulty in sight reading and Difficulty in control

Hitboxes for timing - how much lenience is important - too forgiving its too easy!

Community Content

Keeps games going on for super long! Clone Hero, Step Mania, Osu, etc

Account for modding and level editors

Progression Systems

Campaign or story mode → how does this progress? Need flow

Taiko game - layer of collectibles built into rhythm gameplay

Sound Voltex - Boss songs as a way of unlocking another song. finish 3 specific songs to unlock the 4th

Accessibility

Visual accessibility options can be helpful 

More customization helpful for access to game

How to instill a sense of wonder?

What do we do that causes Breaking sense of wonder?

[https://www.youtube.com/watch?v=Xd7u6r5IvGQ](https://www.youtube.com/watch?v=Xd7u6r5IvGQ)

Evoking Wonder

- Witholding info from your player
- Leave some things out on purpose!

subvert expectations and skeptcism!

Acknowledge the players efforts when they dont think you will

Negative possibility Space

Advice - learn not to fall in love with your own work

Be good at being wrong!

May need to change direction and throw ideas aside

But keep a document of those old ideas!

Limiting Creativity

Limitations to stoke creativity - remember a game is never done, only released

Fog in silent hill as limitation made creativity

What am I even making anymore????

SIDENOTE: Thumper meets Pacman idea

On story, use of technology, and more

think eight grade - what are the kids doing?

On BLade Runner:
Rutger Hauer was the antagonist to Decker's protagonist but the villain was the world/Tyrell corporation.

reminds me of ‘simulacra & simulation’: culture has become a copy without an original

Scaled Down Scene - How to improve? 

- Are bullets too far away / outline makes them appear closer?

Made some adjustments! Minor improvement

Need to adjust rotation of characters at edge of screen - attempting something!

Referencing originam ax and jam starfox code

Top line commented out is error - WHats wrong here?

![Untitled](June%2028%20a14d15ce6eb74fc596072af22d236133/Untitled.png)

Can I add boomerang x teleport mechanic? Think about this? Surfboard ? Teleport away from it but need to return back /  quick button for returning back? 

How does it integrate with music?