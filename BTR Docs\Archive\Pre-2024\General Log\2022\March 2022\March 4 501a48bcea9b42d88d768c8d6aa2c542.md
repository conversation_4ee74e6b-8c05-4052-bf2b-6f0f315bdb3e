# March 4

Did some game design research

**Aaero** 

two movement based mechanics, essentially inversions of go here/avoid here and a shooting mechanic

Will need to play more to see if there’s something more fruitful later on

**Oco**

Great one button platform / rhythmic game. 

Interesting Level editor! good short experiences as well

WOrking on Spatialized audio

PlayOneShot - just the initial locaiton

PlayOneShotAttachked - checks every frame? Resource intensive? Look this up

SPatialized working with Min/Max distances adjusted

Implemented enemy type in scripting for folder path of OneShots, etc

Starting with Basic for building up needed sounds

GS_HB_Fx_08.wav COOL SOUND - look at these 

Adding some sounds to the game

- Enemy Fire
    - Using Spatialized OneShot - Should I be using instances? Are they iterrupting each other?
- Birth
- Death

Watching REZ for inspiration 

- Should use different end tags for different combos of sounds

Need a design language for projectiles vs enemies vs more decorative sounds

Also - what is the need of this when you have a map? Level idea - DISABLED map

Seperate Projectile and Enemy folders for sounds

Different enemies could shoot same projectile types, and vice versa

Trying to time projectile pulse with unity - thinking of this all wrong! Can exist as a loop in FMOD

Pulse visual should probably loop the same way, otherwise they’re all the same - unless I setup a counter per FMOD instance to accept or deny a play of the sound?? try this

Need to set the loop of the particles to the same speed as the koreo - look into this! Or seperate these things into other systems

DId this for audio! Need to do things for particles - ALSO

Need to STOP the audio loop at some point as well. On death for bullets???

Need to consult FMOD docs for create instance abiltiies, so that i can stop it when object dies

[https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d](https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d)