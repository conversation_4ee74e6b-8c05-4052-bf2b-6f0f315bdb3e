# July 25

Trying to fix weird issues with shooting reticle movement. Remove Time.deltaTime from the speed determinant in the script, may have helped things

Weird issue with things not working in build with this movement script, versus things working somewhat in editor. This may be a factor?

Fixed UI not appearing on screen. 

Changed game to IL2CPP builds. Seems better? faster builds at least. 

Change Camera system, mostly working ok. Should allow for adjustments to reticle range now in ways i didnt have before. will need tweaking!

Need to fix UI menu, not working properly.