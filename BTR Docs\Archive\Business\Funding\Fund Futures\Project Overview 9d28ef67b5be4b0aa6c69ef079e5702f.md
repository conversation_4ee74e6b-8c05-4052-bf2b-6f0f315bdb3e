# Project Overview

> Project overview including a summary of the proposed features, components, story, subject matter, target platforms and markets, creative strength, current status, etc. The content of the project overview may vary but the information submitted must provide a clear overview of the market-ready product that will follow from the early-stage activities supported through this stream of the program. **Please detail the extent to which the project supports and reflects diversity, particularly under-represented voices within the interactive digital media industry**. **1 to 3 pages (maximum)**
> 

[Version 1](Project%20Overview%209d28ef67b5be4b0aa6c69ef079e5702f/Version%201%20178b955fba794e418cf3e2281c885f2c.md)

Proposed features:

Beat Project is a chaotic musical bullet-hell where every action controls the music. 

- 10 levels of Rhythm and sonic mayhem!
- Musical Puzzle Box Boss Battles
- Accessible control options
- Dance club inspired, Techno-Psychedelic Visuals
- Incredible player-reactive audio in full 3D spatialization for all music and sound effects.
- Accessible audio options for hearing impaired, cochlear implants, and deaf players.

**Components:**

- WHAT IS MEANT BY COMPONETS OF THE GAME?

Story:

You play the role of a Systems Runner, tasked with going into depths of cyber space. You ride one-way light pipes to find the central power source of these corners of the net, in order to shut them down and clean them up for re-comissioning. While on your journey, you need to avoid old, still operational programs that litter these areas. They will send out packets that you have to avoid! Luckily you can use your powers to freeze and launch these back at the interferring programs, corrupting them so you can proceed. 

How to integrate rhythm and flow into this???

Subject Matter:

The game deals with the ideas of displacement, and who the haves and have nots are in a society. 

Could it relate to history, what it means to preserve, what it means to erase?

Target Platforms:

PC planned for initial release. Research shows expanding to VR may be worthwhile, so a future update to integrate VR and launch on VR platforms is possible.

Markets:

Age Range - US : [https://www.statista.com/statistics/189582/age-of-us-video-game-players/](https://www.statista.com/statistics/189582/age-of-us-video-game-players/)

[https://steamspy.com/](https://steamspy.com/) may have relevant data?

**Fans of Dance music**

Club and Trance music has had widespread appeal since the late 80s / early 90s. 

People between the ages of 18 and 40 are the ones who listen most to electronic music. 

49% of EDM listeners are female globally

[https://www.lowtone.co/articles/edm-statistics/](https://www.lowtone.co/articles/edm-statistics/)

Creative Strength:

Music + action game combination that allows the player to create teh soundtrack as they proceed through the level. 

Current Status:

Work on a prototype, refining the gameplay to integrate with the creative musical aspects of the game design. 

Diversity: 

Important aspect of my game involves translating the playfulness of music into other mediums, allowing a wider diversity of people to experience the language of music. 

As currently the sole proprietor, I do not identify as a marginalized group however I work with communities that are very focused on representation. It is a large part of how I choose who to partner with on projects. Along with this, fair and equitable working conditions for everyone involved stands as an important part of any project I take.