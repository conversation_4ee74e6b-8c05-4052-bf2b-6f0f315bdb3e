# Sept 21

Idea - boomerang / axe throw with projectiles but you have to dodge / shield them when they come back

[Recreating God of War's Axe Throw | Mix and Jam](https://www.youtube.com/watch?v=M-3P59GtRW4)

Looking at adding a dodge mechanic

Or maybe dodge + parry?

[Recreating <PERSON>'s Freeflow Combat | Mix and Jam](https://www.youtube.com/watch?v=GFOpKcpKGKQ&t=1s)

What if the proximity of the bullet decides the action? 

Using Sensor Toolkit as a basis of this

Range Sensor

Issue - bullets not dying upon impact with player? 

Box Collider was too low, more approrpiately placed now.

Continuing Range Sensor work for bullets

- deleted projectileslowsphere
- delted ground collider

Trying to implement dodge and parry

<PERSON>,<PERSON><PERSON><PERSON>,and <PERSON><PERSON> all do something like this

- Allow hold to function  longer block?
- Tap would function as parry if at right time

Pathway for Dodge Parry control partially setup

How should this work? Should parry be its own state?

- Parry meaning it flies back towards the enemy who shot it

Dodge will likely be easier to implement

- character moves away from in coming bullet
- timing based - use dotween?
- 
- 

—LIGHTING—

Watching how to setup lights in Unity

 

[Lighting tutorial: 4 techniques to light environments in Unity | Unite 2022](https://www.youtube.com/watch?v=DlxuvvYZO4Q&t=32s)

Some errors occuring when baking lighting

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled.png)

Bake lighting experiement taking a while - try it overnight

Sept 26th

Fixed Cinemachine camera movement - attached ot reticle movement

Looking at enemy lockon - issues bouncing between different enemies - not sure why this happens?

Adjusted lock on enemyLockInterval from 0.5 to 1

In OnLockEnemy, Adjusted RaySpawnEnemyLocking.transform.lossyScale / 2 to RaySpawnEnemyLocking.transform.lossyScale / 4

Seems better! Need to play and test

Now looking at reticle placement with rotating camera movement

Needs work, expecially when looking backwards

AimRotationSPhere parenteed to camera seems like an issue, putting it back on gameplay plane

This helps, investigating camera movement issues now. When nothing moves, camera movement seems appropriate distance from reticle and character. 

Is it a camera physics issue, that movement causes it’s to get close when aimed backwards?

Manually adjusting Z value of Shooting when platform rotates, to multiples of itself to adjust for thigns moving off a bit. Seems to work fine! May be the permanent solution. This is done in player movement where platform rotation is handled

On a better track with camera movement now it seems, but way too sensitive to movement

need to dampen and try things

Use this video for reference on what to try 

[How to use Cinemachine's Free Look Camera | 3rd Person Camera in Unity](https://www.youtube.com/watch?v=XT6mUlpO4fA&t=1s)

Looking at fixing my highlight render feature now 

Not sure what’s wrong yet - need to investigate more

Sept 27th

RenderFeature set wrong! For opaque layer when it should have been transparent

Highlight fixed

Need to dial in materials for this - for different objects like enemies vs bullets

Added UModeler X for AI texturing, but thinking this might be better in blender? May be case by case basis for these things. Need to go through tutorials

Sept 28th

Attempting to fix some camera issues. Want to keep player and reticle in frame always

Camera Target Group for Look At is working, but camera movement is a bit much

Increasing dead zone for all rigs, may help this - it didnt!

Lowered that significantly and camera is a bit better

Camera may be ‘good enough’ currently - needs more work though

Hard to play needs more adjustments!

Fixed RenderOnTop for Reticle. Need seperate ones for differnet objects, currently doesn’t looking great

Adjusted the  Reticle Spin because it wasn’t working on disable / enable. May work now but probably need to adjust this for unregister / register for events

Daniel Ilett has some good videos, maybe good for adding interesting things

Notes from build

- better camera control
    - Need to be able to look up more!
    - Adjust Clamp values in Shooter Movement to allow for this?
- better death animations for enemies
    - does unscaling the particle effect allow it to play properly? it may not be playing due to the time skipping upon enemy death?
        - No, not seeing it now? Not sure whats up
    - current effect is ok, may need more to it
- better death animations for bullets
    - unscaling time for this too
    - current effect is jank, need to look for others

Pitch, yaw, and roll damping appear to be a large part of the speed of rotation. 

Fine tune these so that it’s not too slow / not too fast

Player flies off of second stage - refine path - this will fix it

Blast of coloured light like rez for some death effects? 

VFX graph solar flare? look for ideas

Change Birth/Deaht particles on enemy to prefabs and reference them? More efficient

Unity Muse has good suggestions for efficiency and pooling for this

Need URP versions of Shapes FX packs

Also need to look up data mosh reddit stuff - recreate

Need to look at SHMUP boss and other toolkits as well - any usefulness ?

Triadic color palette for setting up game?

[Triad Color Palette Generator | Toptal®](https://www.toptal.com/designers/colourcode/triad-color-builder)

UI

[Interface In Game | Collection of video games UI | Screenshots and videos](https://interfaceingame.com/)

Designing UI - turn everything off and turn on one by one to see if you need things, and where to put them. 

Whats a game that puts information like health around your cursor?

Dont use more than 3 fonts in a game! 

Tween tool seems interesting

[Just launched PrimeTween - my high-performance tween library with an extremely simple but powerful API. Create thousands of animations, delays, and sequences with zero KB of memory allocations! Check out the performance tests in the first comment! FREE on Asset Store.](https://www.reddit.com/r/Unity3D/comments/16vdwmn/just_launched_primetween_my_highperformance_tween/)

Effects to use in Beat Remake

Level with this as mechanic?

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%201.png)

Other advanced dissolve effects also look good for this

Need to consider collectables in the game - what for? where might they exist?

mirza beig - - psys loop plexus from ultimate vfx - good for enemy death?

Lots of interesting mesh effects in shape fx 1 and 2, not sure where they are most applicable

Sept 30th

Enemy Death effect 

- psys loop organica
- netOcean-4k
- plexus

Projectile Death

- Sparks Plexus
- turbulent impact

Other fun ones

- psys loop horizon - for background?
- psys loop hyperspace
- serenity
- starfall
- savezone 3
- loop gravitron
- super galaxy
- logo dissolve

Make adjustment to overall scene lighting or skybox when enemy dies? 

Tried this in Destroy script - doesnt seem to work. 

May be better to adjust Filter attribute 

Can uni bullet hell be used with my object particle system shooting? Or will i use more than one system for bullets, same bullet prefab

[Uni Bullet Hell](https://assetstore.unity.com/packages/tools/integration/uni-bullet-hell-19088)

CMF Advice

[Caldera Interactive on Twitter / X](https://twitter.com/CalderaInteract/status/1708888222980510001)

October 2nd

Fixing paths of ouroboros

- Weird camera behaviour on Mobius Tube 6 - rotating for no reason
    - Taking that out of rotation for now
- Minor issues with Mobius Tube 2
    - Keeping in rotation, just need to fix
- Multi Mobius 1 and Mobius Tube 3 disabled due to not being round enough - i think
    - Can I round these out? Do something with them?

Disabling Player Movement Extension because I don’t think it’s working. I think I need to integrate it into A* movement of player. 

Trying to adapt snake movement for new movement style of player 

- giving it a shot - untested

Bullet type idea

- Lock on, splits into multiple bullets
- Need to approach these differently

How can i break down my systems in more meaningful / deep ideas?

- Can on-rail traversal be looked at in a few different ways?
- Would a slower photo-mode / sentry mode be useful?
    - Place something down before you go full on in the level? What is the musical equivalent?

**From CDJ Music standpoint**

Cue Points and Loops:

Players can set, recall, and clear cue points.
Auto/manual looping functions for creating loops of varying lengths.
Hot cues allow for immediate playback from predetermined points.

Allow player to setup cue points in a level? 

Ableton Features - Inspiration here?

Ableton Live is a popular digital audio workstation (DAW) known for its versatility and ease of use, especially in live performance settings. Here are some of its basic features:

1. **Session View and Arrangement View**:
    - **Session View**: A non-linear grid for recording and playing back audio and MIDI clips in real-time.
    - **Arrangement View**: A traditional, timeline-based interface for arranging, recording, and editing music.
2. **Clip Launching**:
    - Ability to launch audio and MIDI clips independently or in sync, allowing for real-time performance and composition.
3. **Warping**:
    - Real-time time-stretching and pitch-shifting of audio, enabling tempo and pitch changes without affecting the other.
4. **MIDI Sequencing and Virtual Instruments**:
    - Extensive MIDI sequencing capabilities for both hardware and software instruments.
    - Comes with a variety of built-in virtual instruments.
5. **Audio and MIDI Effects**:
    - A wide range of audio effects (e.g., EQ, reverb, delay) and MIDI effects (e.g., arpeggiator) for sound processing and creative manipulation.
6. **Audio Recording and Editing**:
    - Multi-track audio recording and comprehensive audio editing features.
7. **Max for Live**:
    - An integrated platform for building custom instruments, effects, and tools within Ableton Live.
8. **Instrument and Effect Racks**:
    - Combining multiple instruments and effects into a single rack for easier control and more complex sound design.
9. **Automation and Modulation**:
    - Real-time recording of parameter changes, automation curves, and modulation for dynamic sound changes.
10. **Browser and Collections**:
    - Easy browsing, previewing, and importing of sounds, samples, and effects.
    - Collections for organizing and quickly accessing favorite or frequently used items.
11. **Built-in Audio and MIDI Setup**:
    - Comprehensive audio and MIDI setup with support for a wide range of hardware.
12. **Export and Sharing**:
    - Export options for audio, video, and stems, with integrated sharing features for collaborating with other musicians or sharing your work online.

These features make Ableton Live a powerful tool for musicians, producers, and live performers, allowing them to create, produce, and perform music in a fluid and intuitive manner.

Using prefab generator to try and make a basic structure for player to walk across on the snake. Most of the way along, hope to figure this out tomorrow. 

If this doesn’t work - could disable the movement maybe when on snake? Re-enable for other structures? Not worth spending too much time on the movement maybe

May need to create a U like structure to box player in on the snake, so they dont fall off

Not a bad idea! Can function as prefab!

Oct. 3rd

Thinking about this today - issues due to movement of bones in all dimensions, affects path length

if i lock Y and Z values of the animation, does that help? 

- path length still changing - but is it more managable?

What if I just locked the x? 

NOT WORKING

What is working? Position and Move mode set to Relative- this works!

Works for movement - Camera struggling with this a bit

Likely need new camera mode for this or to lock some constraints

Rough snake mode working - issues primarily with camera

Need tow versions of enemies - navmesh and on rails (for snake)

Could use behaviour designed on snake but just stick it to set path? 

Magic Light Probes for placing light probes? Or bakery?

Trying MLP first - could be good!

Look into using Bakery+RTPreview

Snake roughly working, enemy that follows a curvy spline setup but just need appropriate Behavior Designer for AI - look at forums and discord for this!

Oct. 4th

Adding Curvy integration package to Behavior Designer

Not really what I wanted

Trying more basic - Behavior Desginer and A* - Patrol implementation

Not really woring either with grid graph. 

Thiking curvy integration for movement would still be better than this

Need to write up proper BD scripts for that

Can just have curvy spline do movement - working!

Seperate attack into BD 

Very basic AI - can get way more advanced

Need to find a way to change forward / backward in spline controller. How is this not obvious? Search forums and discord. Doesnt appear to be an exposed property. 

New method of changing direction that seems to properly compile! 

Test and see

Oct. 5th

Upgraded Buto to 2022 version

Changed light to Bakery setup

Need to figure out why snake teleport script is no longer working

Want to improve outline / detection of bullets

try pro radar builder for this

Cannot get UI target Tracker working properly, finnicky as hell

Pro Radar seems fine for minimap though, may just need to use seperate system? 

Trying HUD Navigation System

HUD Navigation is much better! Feature rich, works great. Stick with the new system. 

Light Cookies fixed in Buto

Need to setup the wave spawn points for moving snakes - figure out how whole snake thing will work

Maybe when you reach the end of the snake wave is over? Requires proper tightening…. refinement on what completing that properly looks like. Feels possible. 

This can be an interlude to several more ouroboros

Can i have a snake look like it’s unwinding from an ouroboros? What does that camera shot look like? You fly off and land on a snake moving forward? 

Flag these distoriton effects

[Distortion Shader Pack 2](https://assetstore.unity.com/packages/vfx/shaders/distortion-shader-pack-2-62426)

Advanced Dissolve really good - new to use this for something!

Level where everyting is dark and bullets are the only visible things?

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%202.png)

What is my game’s vision?

Really simplify what that is

Particle effect not appearing on projectiles - fix this!

Refine projectile look - disable circle mesh or make transparent more? Need a cooler look

Reference other games 

birth/death effects for projectiles - looking for stuff

Some interesting ones!

- Effect_04_chargeshot
- Efect_12_CosmicHorror
- Effect_37_PlanetStone
- Effect_47_PreciseShot

Basically any planet one may be good to use and tweak

Need better way to handle death particles - instantiate in place probably

Do I need P Move in Crosshair script? Remove if unecessary

Reticle lock on not working - fixing this

Position mode needs to be Relative - Relative for snake it seems, but this likely causes problems on the static ouroboros forms. May need to switch between setups ofr these 

Got that working!

Trying to fix camera on snake now - thing it’s weights in camera target group

Maybe a bit of damping?

Adjust weights to equal and damping from 1,1,1, on each access but still bad

Change back? Look into what else needs to be done here

Maybe slower or more static snake for now?

Need to fix camera!

Back to old one - testing…. better in some ways

Need to add line generation back to shooting of bullets

Trailer renderer or something!

Need to make sure camera works for all sections now as well  

Oct. 11th

[Three Steps to Becoming a Better Artist Immediately](https://www.youtube.com/watch?v=amlwcI8dh_g&list=TLPQMTExMDIwMjN_lCXzutHS6A&index=1)

Idea - What is it? Intellectual read

Design - style choices, shape language, proportions

Technique - method of rendering

All three in service of tone - mood, vibe, enforce themes - lighting, colors, cinematography. Feel more than see

Idea, Design, Technique, Tone, Execution

Fuck style - Expression is the ultimate goal!

Checklist for better art

- Stop and think - what are you doing / saying?
- Use reference

A creature is a feature!

- how does move, where does it live, all the things surrounding it

why ami doing this?

what do i want to say?

who am i speaking to?

how can i be most expressive, to reach them best?

REMEMBER - anything can have character - a barrel is not just a barrel!

Think set design

Fill in your design spec - what why who where how etc

Creating art is like arranging flowers

Expressive reference can say what you want to say for you! Use an expressive reference

Fixed movement Player Restriciton sphere! Working now!

Oct. 18th

FPS Encounter design video

DPS (Damage Per Second) Race designs - boring approach

Getting reacquainted with project

Successfully created bounds for player model

so I can edit the rail path for player to more closely match level

<aside>
💡 Also need to look over ground adjustment setup for player height

</aside>

May not be working properly 

Oct. 24

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%203.png)

trying reflective shaders and materials for metatron scene

ghost one

Good talk on FMOD audio!

[The 'TUNIC' Audio Talk](https://www.youtube.com/watch?v=sCIK78OHrIY)

**Oct. 30th**

As i built out other levels, need to properly define prefab for player

Break this out into most basic parts / blocks