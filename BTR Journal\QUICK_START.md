# BTR Journal - Quick Start Guide

## Creating a New Journal Entry

### Method 1: Using the Python Script (Recommended)
```bash
# Navigate to BTR_Journal folder
cd "j:\BTR U6 2025 RG\BTR_Journal"

# Create a bug fix entry
python create_journal_entry.py bug "shooting system not working"

# Create a feature entry  
python create_journal_entry.py feature "new enemy AI system"

# Create a debug session entry
python create_journal_entry.py debug "projectile initialization timing"
```

### Method 2: Manual Creation
1. Copy the appropriate template from `Templates/` folder
2. Rename with format: `YYYY-MM-DD_TYPE_description.md`
3. Place in the current month folder: `2025/MM-MonthName/`
4. Update the timestamp at the top
5. Fill in all sections

## Common Entry Types

### 🐛 Bug Fix Entry
Use when you've identified and fixed a bug:
- Document the problem symptoms
- Explain the root cause
- Detail the solution implemented
- Include testing verification

### ✨ Feature Entry  
Use when developing new functionality:
- Describe the feature purpose and requirements
- Document design decisions
- Include implementation details
- Add usage examples

### 🔍 Debug Session Entry
Use when investigating issues:
- Document the investigation process
- Record what was tried and what worked
- Include diagnostic tools created
- Note lessons learned

## File Organization

### Current Structure
```
BTR_Journal/
├── 2025/
│   └── 07-July/
│       └── 2025-07-18_BUG_shooting_system_fix.md
├── Templates/
│   ├── bug_fix_template.md
│   ├── feature_template.md
│   └── debug_session_template.md
└── Index/
    └── bug_fixes_index.md
```

### Naming Convention
- **Date**: Always start with `YYYY-MM-DD`
- **Type**: Use `BUG`, `FEATURE`, or `DEBUG`
- **Description**: Brief, underscore-separated description
- **Example**: `2025-07-18_BUG_shooting_system_fix.md`

## Best Practices

### ✅ Do This
- **Be Specific**: Include exact error messages, file names, line numbers
- **Include Context**: Explain why the change was needed
- **Document Testing**: How you verified the fix works
- **Add Code Snippets**: Show before/after code when relevant
- **Update Indexes**: Keep the index files current

### ❌ Avoid This
- Vague descriptions like "fixed stuff"
- Missing timestamps or incorrect dates
- Skipping the testing section
- Not updating related documentation
- Forgetting to commit journal entries to version control

## Tips for Effective Journaling

### 🎯 Focus on Value
- **Future Self**: Write for yourself 6 months from now
- **Team Members**: Include enough detail for others to understand
- **Debugging**: Document what didn't work to save time later

### 📝 Writing Style
- Use clear, concise language
- Include screenshots or diagrams when helpful
- Link to related issues or documentation
- Use bullet points and checklists for readability

### 🔄 Regular Maintenance
- Review and update entries as situations evolve
- Archive old entries that are no longer relevant
- Update index files when adding new entries
- Backup journal entries with your code repository

## Integration with Development Workflow

### Version Control
```bash
# Add journal entries to git
git add BTR_Journal/
git commit -m "docs: add journal entry for shooting system fix"
```

### Code Reviews
- Reference journal entries in pull requests
- Include journal entry creation as part of bug fix process
- Use journal entries to document architectural decisions

### Team Communication
- Share journal entries in team meetings
- Use entries to onboard new team members
- Reference entries when similar issues arise

---
**Quick Commands Reference**:
- `python create_journal_entry.py bug "issue description"` - Create bug fix entry
- `python create_journal_entry.py feature "feature name"` - Create feature entry  
- `python create_journal_entry.py debug "system name"` - Create debug session entry

**Need Help?** Check the templates in the `Templates/` folder for detailed examples.
