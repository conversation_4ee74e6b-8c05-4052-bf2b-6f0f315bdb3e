# May 3/4/5
 2 | 
 3 | Sorting and aggregating issues from last couple days
 4 | 
 5 | | Task | Details | Status |
 6 | | --- | --- | --- |
 7 | | New UI for projectile and enemy locks | current one that follows the reticle is not very useful and not working properly |  |
 8 | | Refine reticle control and enemy / projectile lock on | Doesn’t feel as good as it should, hard to lock on to enemies |  |
 9 | | Play with parry feature and refine or remove | Doesnt feel responsive, logic seemed off when trying to use it, may need some rethinking |  |
10 | | Motion Extraction Effect | Figure this out! |  |  |
11 | | May 1st Ideas Implementation | Go through May 1st Ideas and write up implementations |  |  |
12 | | Make enemy death effect more visible | Looks like its happening in scene view but barely see it in Play mode |  |
13 | | Motion Extraction Effect | Figure this out! |  |
14 | | A* or Ground Fitter Fix | Y rotation issues - disabled for now |  |
15 | | Skip Between Scenes | Previous method didn’t work and is removed |  |
16 | | Toonkit Manager Adjustments | Adjust shadows and light as appropriate |  |
17 | | Keijiro Duotone | Look at how to use this - test as scene transition effect? |  |
18 | | Reticle Spin | Not tied to koreographer - look at if this is ok implementation |  |
19 | | Game Manager Controls Music Transition | Need to build this out more - tied to scene list? |  |
20 | | Game Objects Inactive on Scene Load | Need to find proper solution for this |  |
21 | | Visual Studio 2019 Warning | Upgrade to Visual Studio 2022 for better performance |  |
22 | 
23 | According to f impossible dev - spine animator is a better tool for snake like behaviour - we’ll see!
24 | 
25 | [【Ground Fitter (free)】- Fit your dynamic objects to ground](https://forum.unity.com/threads/ground-fitter-free-fit-your-dynamic-objects-to-ground.541434/page-2#post-5549257)
26 | 
27 | NOTE: Ground fitter not totally working for player character? TEST