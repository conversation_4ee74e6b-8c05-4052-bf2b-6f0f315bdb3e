# May 23

Fixing the volume control in the system menu. Works with FMOD now. 

Need to define categories better though - do i like the groups as they currently exist? 

Master, U<PERSON>, Player, Enemies

Looking into how Reach works with controls and rebinding.

Still not quite sure how to get that working properly 

Got the proejctile radius feature working, its LifetimeExtended

adds to lifetime of projecitles within certain radius of player (currently 25)

seems to have worked to stop dissolve 

Need better player hit effect from projectile, feels too quick and non-impactful

Added on - MIXED opion on it, but its an improvement

Prefab effect thats on the player health, so plays from pool when player is hit

Make sure death effect from projectile manager is being returned to pool 

Infinite Spline

Snake shoots in a rhythm

Pow pow pow 

pow pow

QUICK - requires a dodge

MAYBE first snake like this, just steady and fast like it currently is

second like this - more of a pattern

maybe a third like this? Or double snake rotate section

have different patterns occur with double snake

Mechanics

Think about how these interact with each other

- can slow time initiate doppler on projectiles?