# Build Optimization

1. Shader Variant Reduction:
- Current shader stripping: 27.52% (50,718/184,286 variants)
- Target: Increase stripping to >50% by:
a) Removing unused features
b) Consolidating similar shaders
c) Using shader_feature instead of multi_compile
1. Fix Shader Safety Issues:
- Add abs() for pow() operations in:
    - SineVFX/GalaxyMaterials
    - Ciconia Studio/CS_Ghost
    - Shader Graphs/Transparent-ReceiveFullFog-Shader
1. Update Deprecated Paths:
- Replace ScriptableRenderer.cameraColorTargetHandle with Render Graph API
- Update ReAllocateIfNeeded calls to ReAllocateHandleIfNeeded

```csharp
// 1. Replace FindObjectOfType calls with cached references
private void Awake()
{
  // Cache at startup instead of searching repeatedly
  _cachedManager = FindFirstObjectByType<GameManager>();
}

// 2. Use FindObjectsByType with better performance
var objects = FindObjectsByType<Enemy>(FindObjectsSortMode.None);
```

Fix

Fix required for animation transition:
'Standing Run Forward -> Running Jump' in 'Joostman Controller'

- Add either an Exit Time or condition to prevent transition being ignored