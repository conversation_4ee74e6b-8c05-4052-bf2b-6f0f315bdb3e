# June 8

<aside>
💡 Edited version of Warp Effect for Long Rewind - this is temporary!

</aside>

VFX graph works well for this - can control particles nicely

Making sure dolly switcher works

Looking into Rendering enemies and bullets on top of everything else. 

Editing QUibli Renderer for this

Tried this, didn’t work

[CREATE YOUR OWN RENDERER WITHOUT CODE in Unity!](https://www.youtube.com/watch?v=szsWx9IQVDI)

May need to seperate Projectile object from model for proper RenderOnTop Layer support

Why do I have Projectile Layer? For Radar?

Things are darker now using Render on Top - why?

Used this video

[Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE)

<aside>
💡 RenderOnTop was added and is working! Colors are strange but it looks cool?

</aside>

Looking at ghost trail options for Rewind effect

I like trails in general, trying them for now

<aside>
💡 Feedback Long Rewind happens when rewinding now

</aside>

Use right glitch as bomb? Need lots of stamina for it?

Need environments to ignore raycast so that I can see enemies through the muck?

Seems lighting depends on the current skybox color when generating

Find out what things are set to ignore raycast and why - maybe dont need it on certain environment

Seems like lighting and specular aren’t being applied to Ignore Raycast - look at that!

Look liks Culling mask of light source needed Ignore Raycast added to it - should help all objects on RenderOnTop as well!

Eveyry time i build asked about shift outline - what is it?

Amplify animation pack causes errors on build as well- fails - why?