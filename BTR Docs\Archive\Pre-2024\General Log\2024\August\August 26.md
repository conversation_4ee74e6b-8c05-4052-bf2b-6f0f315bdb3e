# August 26

Analyzing transition system

On Wave Started 

Do we need OnSwitchScene event script? Im nto sure its neceaary on enemy Spawn Controller

Making wave event subscriptions the only way for spline manager to increment, in an effort to simplify process and understand what’s happening better 

This is working now

Wave → Spline Manager → Game Manager if last spline

Various graphics / effect broken in build - unsure why

Need to verify 

- Fixed JPG effects but not sure why initial texture isnt working and gasme volumes are at 0
- Try without main menu - see if it fixes things?
- Need to figure out resolution is low, settings messed up in builds

Read docs on Reach menu system, figure it all out 

**August 27**

Been updating this and figuring out whats up 

QTE Implemented, but errors, need to bug test but it might be interesting!

Stuck in rewind music phasem, need to address this

other action seem find, feels like its just the music?