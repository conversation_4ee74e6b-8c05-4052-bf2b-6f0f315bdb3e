using System.Collections;
using UnityEngine;
using UnityEngine.Events;
using BTR;

namespace BTR
{
    /// <summary>
    /// Simplified replacement for CinemachineCameraSwitching
    /// Handles player body/reticle visibility during scene transitions without camera switching
    /// </summary>
    public class SimpleCameraManager : MonoBehaviour
    {
        [Header("Player Elements")]
        [SerializeField] private GameObject playerBody;
        [SerializeField] private GameObject reticle;
        
        [Header("Transition Settings")]
        [SerializeField] private float transitionDuration = 1f;
        [SerializeField] private bool hideElementsDuringTransition = true;
        [SerializeField] private bool enableDebugLogs = false;
        
        [Header("Events")]
        [SerializeField] private UnityEvent playerFacingForward;
        
        private GameEvents _gameEvents;
        private GameEvents gameEvents
        {
            get
            {
                if (_gameEvents == null && GameEventsManager.Instance != null)
                {
                    _gameEvents = GameEventsManager.Instance.Events;
                }
                return _gameEvents;
            }
        }
        
        private bool isInTransition = false;
        private Coroutine transitionCoroutine;
        
        private void Start()
        {
            InitializeGameEvents();
            EnsureMainCameraState();
        }
        
        private void InitializeGameEvents()
        {
            if (GameEventsManager.Instance == null || GameEventsManager.Instance.Events == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogError($"[{GetType().Name}] GameEvents not assigned!");
                }
                return;
            }

            _gameEvents = GameEventsManager.Instance.Events;

            if (gameEvents != null)
            {
                // Subscribe to transition events
                gameEvents.OnTransitionCameraRequested += HandleTransitionRequested;
                gameEvents.OnStartingTransition += HandleTransitionRequested;
                gameEvents.OnSceneTransitionStarted += HandleSceneTransitionStarted;
                gameEvents.OnSceneTransitionCompleted += HandleSceneTransitionCompleted;
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Subscribed to camera transition events");
                }
            }
        }
        
        /// <summary>
        /// Ensures main camera state - equivalent to SetMainCamera from original
        /// </summary>
        public void SetMainCamera()
        {
            if (isInTransition)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] SetMainCamera called during transition - will apply after transition");
                }
                return;
            }
            
            // Show player elements
            if (playerBody != null)
            {
                playerBody.SetActive(true);
                playerBody.transform.localPosition = Vector3.zero;
            }
            
            if (reticle != null)
            {
                reticle.SetActive(true);
            }
            
            // Trigger forward-facing event
            playerFacingForward?.Invoke();
            
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Main camera state restored - player elements visible");
            }
        }
        
        /// <summary>
        /// Handles transition camera request - simplified version
        /// </summary>
        public void SwitchToTransitionCamera()
        {
            if (transitionCoroutine != null)
            {
                StopCoroutine(transitionCoroutine);
            }
            
            transitionCoroutine = StartCoroutine(HandleTransitionCoroutine());
        }
        
        private void HandleTransitionRequested()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Transition camera requested");
            }
            
            SwitchToTransitionCamera();
        }
        
        private void HandleSceneTransitionStarted()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Scene transition started");
            }
            
            SwitchToTransitionCamera();
        }
        
        private void HandleSceneTransitionCompleted()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Scene transition completed - restoring main camera state");
            }
            
            // Delay restoration to ensure scene is fully loaded
            StartCoroutine(DelayedMainCameraRestore());
        }
        
        private IEnumerator DelayedMainCameraRestore()
        {
            yield return new WaitForSeconds(0.5f);
            SetMainCamera();
        }
        
        private IEnumerator HandleTransitionCoroutine()
        {
            isInTransition = true;
            
            if (hideElementsDuringTransition)
            {
                // Hide player elements during transition
                if (reticle != null)
                {
                    reticle.SetActive(false);
                }
                
                if (playerBody != null)
                {
                    playerBody.SetActive(false);
                }
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Player elements hidden for transition");
                }
            }
            
            // Wait for transition duration
            yield return new WaitForSeconds(transitionDuration);
            
            // Restore main camera state
            isInTransition = false;
            SetMainCamera();
            
            transitionCoroutine = null;
        }
        
        /// <summary>
        /// Ensures main camera state is applied - called by SceneSwitchCleanup
        /// </summary>
        private void EnsureMainCameraState()
        {
            // Small delay to ensure everything is initialized
            StartCoroutine(DelayedMainCameraRestore());
        }
        
        /// <summary>
        /// Public method for external components to trigger main camera state
        /// </summary>
        public void RestoreMainCameraState()
        {
            SetMainCamera();
        }
        
        /// <summary>
        /// Public method to check if currently in transition
        /// </summary>
        public bool IsInTransition()
        {
            return isInTransition;
        }
        
        private void OnDestroy()
        {
            if (transitionCoroutine != null)
            {
                StopCoroutine(transitionCoroutine);
            }
            
            if (gameEvents != null)
            {
                gameEvents.OnTransitionCameraRequested -= HandleTransitionRequested;
                gameEvents.OnStartingTransition -= HandleTransitionRequested;
                gameEvents.OnSceneTransitionStarted -= HandleSceneTransitionStarted;
                gameEvents.OnSceneTransitionCompleted -= HandleSceneTransitionCompleted;
            }
            
            _gameEvents = null;
        }
        
        #region Context Menu Methods
        
        [ContextMenu("Set Main Camera")]
        private void SetMainCameraFromMenu()
        {
            SetMainCamera();
        }
        
        [ContextMenu("Switch To Transition Camera")]
        private void SwitchToTransitionCameraFromMenu()
        {
            SwitchToTransitionCamera();
        }
        
        [ContextMenu("Test Transition")]
        private void TestTransition()
        {
            StartCoroutine(TestTransitionCoroutine());
        }
        
        private IEnumerator TestTransitionCoroutine()
        {
            Debug.Log($"[{GetType().Name}] Testing transition...");
            SwitchToTransitionCamera();
            yield return new WaitForSeconds(transitionDuration + 1f);
            Debug.Log($"[{GetType().Name}] Transition test complete");
        }
        
        #endregion
        
        #region Validation
        
        private void OnValidate()
        {
            if (transitionDuration <= 0f)
            {
                transitionDuration = 1f;
            }
        }
        
        #endregion
    }
}
