# GameManager Optimization Implementation Guide

## 1. Spatial Grid System Implementation

### 1.1 SpatialGrid Class
```csharp
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;

public class SpatialGrid : MonoBehaviour
{
    public float CellSize = 5f;
    public int MaxCells = 1000;
    
    private NativeMultiHashMap<int, Transform> _grid;
    private float3 _gridOffset;

    public void Initialize(float3 boundsCenter)
    {
        _grid = new NativeMultiHashMap<int, Transform>(MaxCells, Allocator.Persistent);
        _gridOffset = boundsCenter;
    }

    public void Add(Transform entity, float3 position)
    {
        var cell = GetCellHash(position);
        _grid.Add(cell, entity);
    }

    public void Query(float3 position, float radius, List<Transform> results)
    {
        var minCell = GetCellHash(position - radius);
        var maxCell = GetCellHash(position + radius);
        
        for (int cell = minCell; cell <= maxCell; cell++)
        {
            if (_grid.TryGetFirstValue(cell, out var entity, out var iterator))
            {
                do {
                    if (Vector3.Distance(entity.position, position) <= radius) {
                        results.Add(entity);
                    }
                } while (_grid.TryGetNextValue(out entity, ref iterator));
            }
        }
    }

    private int GetCellHash(float3 position)
    {
        var gridPos = (position - _gridOffset) / CellSize;
        return (int)(math.floor(gridPos.x) + 
               math.floor(gridPos.y) * 1000 + 
               math.floor(gridPos.z) * 1000000);
    }

    private void OnDestroy()
    {
        _grid.Dispose();
    }
}
```

### 1.2 GameManager Integration
```diff
public class GameManager : MonoBehaviour
{
+    private SpatialGrid _enemyGrid;
    
     private void Awake()
     {
+        _enemyGrid = gameObject.AddComponent<SpatialGrid>();
+        _enemyGrid.Initialize(transform.position);
     }

     public void RegisterEnemy(Transform enemy)
     {
-        if (!spawnedEnemies.Contains(enemy))
+        _enemyGrid.Add(enemy, enemy.position);
     }

     public void KillAllEnemies()
     {
-        var enemies = FindObjectsByType<EnemyCore>(FindObjectsSortMode.None);
+        var results = new List<Transform>();
+        _enemyGrid.Query(player.position, 100f, results);
+        foreach (var enemy in results.OfType<EnemyCore>())
         {
             enemy.TakeDamage(float.MaxValue);
         }
     }
}
```

## 2. Burst-Compatible Job System

### 2.1 Enemy State Job
```csharp
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

[BurstCompile]
public struct EnemyUpdateJob : IJobParallelFor
{
    public NativeArray<float3> Positions;
    public NativeArray<float> HealthValues;
    [ReadOnly] public float DeltaTime;

    public void Execute(int index)
    {
        // Example: Update enemy AI calculations
        if (HealthValues[index] > 0)
        {
            // Simulated AI calculations
            Positions[index] += new float3(0, 0, DeltaTime); 
        }
    }
}

// In GameManager:
private NativeArray<float3> _enemyPositions;
private NativeArray<float> _enemyHealth;
private JobHandle _jobHandle;

private void LateUpdate()
{
    _jobHandle.Complete();
    
    var updateJob = new EnemyUpdateJob {
        Positions = _enemyPositions,
        HealthValues = _enemyHealth,
        DeltaTime = Time.deltaTime
    };
    
    _jobHandle = updateJob.Schedule(_enemyPositions.Length, 64);
}
```

## 3. Event System Validation

### 3.1 Rate-Limited Event Wrapper
```csharp
public class RateLimitedEvent
{
    private float _lastTriggerTime;
    private float _minInterval;
    private UnityEvent _event;

    public RateLimitedEvent(float minInterval)
    {
        _minInterval = minInterval;
        _event = new UnityEvent();
    }

    public void Invoke()
    {
        if (Time.time - _lastTriggerTime >= _minInterval)
        {
            _event.Invoke();
            _lastTriggerTime = Time.time;
        }
    }
}

// Usage in GameEvents:
public static class GameEvents
{
    public static RateLimitedEvent OnDamageTaken = 
        new RateLimitedEvent(0.1f);
}
```

## 4. Memory Pooling Implementation

### 4.1 Pooled Collections
```csharp
public class PooledList<T> : IDisposable
{
    private static Queue<List<T>> _pool = new Queue<List<T>>();
    private List<T> _list;

    public PooledList()
    {
        _list = _pool.Count > 0 ? _pool.Dequeue() : new List<T>();
    }

    public void Dispose()
    {
        _list.Clear();
        _pool.Enqueue(_list);
    }
}

// In GameManager:
using (var enemies = new PooledList<Transform>())
{
    _enemyGrid.Query(player.position, 10f, enemies.List);
    // Process enemies
}
```

## 5. Conditional Debug Optimization

### 5.1 Optimized Logging
```csharp
[System.Diagnostics.Conditional("DEBUG_LOGS")]
public static void ConditionalDebugLog(string message)
{
    var sb = StringBuilderPool.Get();
    sb.Append("[Frame ").Append(Time.frameCount).Append("] ").Append(message);
    Debug.Log(sb.ToString());
    StringBuilderPool.Release(sb);
}

public static class StringBuilderPool
{
    private static Queue<StringBuilder> _pool = new Queue<StringBuilder>();

    public static StringBuilder Get()
    {
        return _pool.Count > 0 ? _pool.Dequeue().Clear() : new StringBuilder();
    }

    public static void Release(StringBuilder sb)
    {
        _pool.Enqueue(sb);
    }
}
```

## Implementation Steps

1. **Phase 1 - Spatial Grid**
   - Add SpatialGrid.cs to Core/Systems/
   - Migrate enemy tracking methods
   - Update combat-related systems

2. **Phase 2 - Job System**
   - Convert enemy state arrays to NativeArrays
   - Create Job definitions
   - Implement dependency chain

3. **Phase 3 - Event Validation**
   - Wrap high-frequency events
   - Add rate limiting configs
   - Update event documentation

4. **Phase 4 - Memory Management**
   - Replace all List/Dictionary with Pooled versions
   - Add cleanup in OnDestroy methods
   - Update unit tests

## Testing Protocol

1. **Performance Metrics**
   - Profile before/after GC allocations
   - Measure Update/LateUpdate durations
   - Track job execution times

2. **Validation Tests**
   - 1000-enemy stress test
   - Rapid event triggering (1000+/sec)
   - Memory leak detection via Profiler

## Reference Documents
- [Tech Recs: Spatial Hashing](BTR Docs/Tech Recs/Insanely FAST Spatial Hashing in Unity with Jobs & Burst.md)
- [Coding Standards](.clinerules)
- [Event System Docs](BTR Docs/Event_System_Documentation.md)