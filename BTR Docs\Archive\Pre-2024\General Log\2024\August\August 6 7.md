# August 6/7

Here's a bullet-pointed list of suggestions to tackle one by one, based on the provided code snippets:

- Optimize ProjectileManager.cs:
    - Replace Queue<ProjectileStateBased> with Stack<ProjectileStateBased> for better performance
        - potentially minor improvement? Have not implemented
    - Implement Unity's C# Job System for projectile updates
        - Job System is being used but only for processing new projectile requests, not for updating existing projectiles.
    - Use NativeArray or NativeList for projectiles and projectileLifetimes
        - Done!
    - Apply <PERSON>urst compilation to performance-critical methods
        - Done!
- Enhance ParticleSystemPooler.cs:
    - Implement Unity's C# Job System for particle system updates
        - Done!
    - Use NativeArray for the pool instead of List<ParticleSystem>
        - Done!
    - Apply Burst compilation to GetFromPool and ReturnToPool methods
        - Done!
- Optimize GameManager.cs:
    - Convert frequently updated properties to structs
    - Use NativeCollections for spawnedEnemies and lockedEnemies
    - Implement a custom memory allocator for frequently allocated/deallocated objects
- Improve Crosshair.cs:
    - Utilize Unity's new Input System for more efficient input handling
    - Use NativeCollections for enemy<PERSON>arget<PERSON>ist, projectileTargetList, and LockedList
- Optimize SplineManager.cs:
    - Implement Unity's Job System for spline calculations
    - Use NativeArrays for storing spline data
- Enhance OuroborosInfiniteTrack.cs:
    - Use Unity's Job System for procedural generation
    - Implement object pooling for spawned prefabs
    - Use NativeCollections for activePrefabs and despawnedPrefabs
- Improve PlayerHealth.cs:
    - Optimize hit effects pool using NativeArray or NativeList
    - Implement a custom update loop to reduce update frequency for non-critical objects
- Optimize SceneSwitchCleanup.cs:
    - Use more efficient scene traversal methods
    - Implement asynchronous object finding to reduce performance impact
- Enhance EnemyKiller.cs:
    - Use Unity's Job System for enemy and projectile destruction
    - Implement more efficient methods for finding objects by layer
- General optimizations:
    - Implement custom update loops across all scripts to reduce update frequency where possible
    - Use structs for small, short-lived data structures throughout the project
    - Implement a centralized object pooling system for all frequently created/destroyed objects
    - Use Unity's Burst Compiler for all performance-critical code sections
    - Implement Unity's new Input System across all input-handling scripts
    - Implement memory-efficient data structures (e.g., NativeCollections) across all scripts
    - Optimize scene loading and unloading processes
    - Implement asynchronous operations where possible to improve performance

Made various improvements to Custom AI Path ALgined to surface, unsure how I feel on these. May or may not be helping. SOme bugs in behaviour of this script need to be fixed (how an error is handled)

Added GPU Instancing to Ty Fast Ghost Shader

August 7

Some more optimizations

Also, working on FMOD 

Improving some sounds

Upgraded to 2.03 for new features that may be useful, like multiband, frequency sidechain, etc

Some issues with hard coded fmod events pointing to the wrong things, fixing this. 

Removing firing blasts as it seems unecessary

- Enemy Lock on sound is not apparent enough, need something better
    - Improve with CLAP but maybe could be better

- Enemy Death creating stuck time rewind / pause - need to fix this
    - reverted fmod inspector assignment changes because something is breaking this, and i dont want to look into it too deeply
    - Seemed to have fixed this i think… monitoring

Is this missing from Crosshair and needs to be added again?

Under LockedList.RemoveAt(i);

```jsx
 // Instantiate and animate the lock-on prefab for each projectile shot
                    if (lockOnPrefab != null)
                    {
                        GameObject lockOnInstance = Instantiate(lockOnPrefab, Reticle.transform);
                        lockOnInstance.SetActive(true);
                        lockOnInstance.transform.localPosition = Vector3.zero; // Center it on the Reticle
                        lockOnInstance.transform.localScale = Vector3.zero; // Set the initial scale to zero

                        // Assuming the prefab has a SpriteRenderer component
                        SpriteRenderer spriteRenderer = lockOnInstance.GetComponent<SpriteRenderer>();
                        if (spriteRenderer != null)
                        {
                            Color initialColor = spriteRenderer.color;
                            initialColor.a = 0f; // Set initial transparency to 0
                            spriteRenderer.color = initialColor;

                            // Animate transparency to fully visible and then back to not visible
                            spriteRenderer.DOFade(1f, 0.25f).OnComplete(() => spriteRenderer.DOFade(0f, 0.25f));
                        }

                        // Scale up and destroy
                        lockOnInstance.transform.DOScale(Vector3.one * initialScale, 0.5f).OnComplete(() => Destroy(lockOnInstance)); // Scale up
                    }
```

Think i took care of that

FINALLL split up Crosshair to 3 classes

Work to be done but its mostly working!